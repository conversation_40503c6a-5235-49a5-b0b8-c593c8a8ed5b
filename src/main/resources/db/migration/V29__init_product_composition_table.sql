CREATE TABLE product_composition
(
     id                   UUID PRIMARY KEY,
     original_id          INT NOT NULL,
     product_id           UUID NOT NULL,
     product_component_id UUID NOT NULL,
     amount               NUMERIC NOT NULL,
     created_at           TIMESTAMP NOT NULL,
     updated_at           TIMESTAMP NOT NULL
);

CREATE UNIQUE INDEX "a5aba40f561345f083e7_ui" ON product_composition (original_id);
ALTER TABLE product_composition ADD CONSTRAINT "1ba829091e0648f7bdfe_fk"
    FOREIGN KEY (product_id) REFERENCES product(id);
ALTER TABLE product_composition ADD CONSTRAINT "eb9549449e304840b146_fk"
    FOREIGN KEY (product_component_id) REFERENCES product_component(id);
