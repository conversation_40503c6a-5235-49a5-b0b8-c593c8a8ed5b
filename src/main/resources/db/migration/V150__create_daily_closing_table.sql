CREATE TABLE daily_closing
(
    id                       UUID PRIMARY KEY,
    pos_configuration_id     UUID      NOT NULL,
    state                    TEXT      NOT NULL,
    receipt_number           TEXT      NOT NULL,
    tickets_count            INTEGER   NOT NULL,
    cancelled_tickets_count  INTEGER   NOT NULL,
    products_count           INTEGER   NOT NULL,
    cancelled_products_count INTEGER   NOT NULL,
    created_at               TIMESTAMP NOT NULL,
    updated_at               TIMESTAMP NOT NULL,
    closed_at                TIMESTAMP,
    previous_closed_at       TIMESTAMP,
    created_by               TEXT      NOT NULL,
    updated_by               TEXT      NOT NULL,
    CONSTRAINT "407317480c6d4f0d967a_fk" FOREIGN KEY (pos_configuration_id) REFERENCES pos_configuration (id)
);

CREATE INDEX "d63fb2c046a14caebf0e_ix" ON "daily_closing" (pos_configuration_id);
CREATE UNIQUE INDEX "7f0866821add47fc96a9_ui" ON "daily_closing" (receipt_number);

CREATE SEQUENCE daily_closing_receipt_number_seq AS BIGINT INCREMENT BY 1 START WITH 1;