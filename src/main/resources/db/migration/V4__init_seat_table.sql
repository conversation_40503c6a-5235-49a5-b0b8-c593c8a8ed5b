CREATE TABLE seat
(
   id             UUID PRIMARY KEY,
   original_id    INT NOT NULL,
   auditorium_id  UUID NOT NULL,
   type           TEXT NOT NULL,
   state          TEXT NOT NULL,
   row            TEXT NOT NULL,
   seat           INT NOT NULL,
   position_left  INT,
   position_top   INT NOT NULL,
   created_at     TIMESTAMP NOT NULL,
   updated_at     TIMESTAMP NOT NULL
);

CREATE UNIQUE INDEX "5069ad909bd34366b0f8_ui" ON seat (original_id);
ALTER TABLE seat ADD CONSTRAINT "e08196a773a7457faa02_fk" FOREIGN KEY (auditorium_id) REFERENCES auditorium(id);
