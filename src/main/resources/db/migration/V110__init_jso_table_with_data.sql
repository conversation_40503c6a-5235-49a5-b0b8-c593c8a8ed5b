CREATE TABLE IF NOT EXISTS jso
(
   id                 UUID PRIMARY KEY,
   original_id        INT NOT NULL,
   code               TEXT NOT NULL,
   title              TEXT NOT NULL,
   created_at         TIMESTAMP NOT NULL,
   updated_at         TIMESTAMP NOT NULL
);

CREATE UNIQUE INDEX IF NOT EXISTS "6d2df8239a184e10ad2f_ui" ON jso (original_id);
CREATE UNIQUE INDEX IF NOT EXISTS "4af44ce1b960462a99b1_ui" ON jso (code);

INSERT INTO jso (id, original_id, code, title, created_at, updated_at) VALUES(gen_random_uuid(), 1, 'N', 'Násilie', now(), now()) ON CONFLICT(original_id) DO NOTHING;
INSERT INTO jso (id, original_id, code, title, created_at, updated_at) VALUES(gen_random_uuid(), 2, 'D', '<PERSON>skrimin<PERSON>cia', now(), now()) ON CONFLICT(original_id) DO NOTHING;
INSERT INTO jso (id, original_id, code, title, created_at, updated_at) VALUES(gen_random_uuid(), 3, 'S', 'Strach', now(), now()) ON CONFLICT(original_id) DO NOTHING;
INSERT INTO jso (id, original_id, code, title, created_at, updated_at) VALUES(gen_random_uuid(), 4, 'Z', 'Závislosť', now(), now()) ON CONFLICT(original_id) DO NOTHING;
INSERT INTO jso (id, original_id, code, title, created_at, updated_at) VALUES(gen_random_uuid(), 5, 'X', 'Sex', now(), now()) ON CONFLICT(original_id) DO NOTHING;
INSERT INTO jso (id, original_id, code, title, created_at, updated_at) VALUES(gen_random_uuid(), 6, 'V', 'Vulgarizmy', now(), now()) ON CONFLICT(original_id) DO NOTHING;
INSERT INTO jso (id, original_id, code, title, created_at, updated_at) VALUES(gen_random_uuid(), 7, 'H', 'Nahota', now(), now()) ON CONFLICT(original_id) DO NOTHING;

-- forgotten unique index for rating
CREATE UNIQUE INDEX IF NOT EXISTS "7123a93746d245f8aabe_ui" ON rating (code);
