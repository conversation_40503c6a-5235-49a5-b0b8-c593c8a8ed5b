CREATE TABLE auditorium_default
(
    id                     UUID PRIMARY KEY,
    original_id            INT,
    auditorium_id          UUID      NOT NULL,
    surcharge_vip          NUMERIC,
    surcharge_premium      NUMERIC,
    surcharge_imax         NUMERIC,
    surcharge_ultra_x      NUMERIC,
    service_fee_vip        NUMERIC,
    service_fee_premium    NUMERIC,
    service_fee_imax       NUMERIC,
    service_fee_ultra_x    NUMERIC,
    service_fee_d_box      NUMERIC,
    pro_commission         INT,
    film_fond_commission   INT,
    distributor_commission INT,
    sale_time_limit        INT       NOT NULL,
    publish_online         BOOLEAN   NOT NULL,
    created_at             TIMESTAMP NOT NULL,
    updated_at             TIMESTAMP NOT NULL,
    created_by             TEXT      NOT NULL,
    updated_by             TEXT      NOT NULL,

    CONSTRAINT "2f0d567565394dfb8fa2_fk" FOREIGN KEY (auditorium_id) REFERENCES auditorium (id)
);

CREATE INDEX "b60c1ca6ca0b46ce918f_ix" ON auditorium_default (auditorium_id);
CREATE UNIQUE INDEX "5c37447eb251425d865e_ui" ON auditorium_default (original_id) WHERE original_id IS NOT NULL;
CREATE UNIQUE INDEX "0e2dd8b2c6bc406ab89f_ui" ON auditorium_default (auditorium_id);
