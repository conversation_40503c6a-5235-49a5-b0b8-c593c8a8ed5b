ALTER TABLE product
    ADD COLUMN tablet_order         INT,
    ADD COLUMN is_packaging_deposit BOOLEAN DEFAULT false,
    ADD COLUMN tax_rate             INT,
    ADD COLUMN discount_amount      NUMERIC,
    ALTER COLUMN original_id        DROP NOT NULL;

UPDATE product
SET is_packaging_deposit = true
WHERE title ILIKE '%zaloha%obal%';

ALTER TABLE product ALTER COLUMN is_packaging_deposit DROP DEFAULT;

DROP INDEX "0f126c7ed3d1412da469_ui"; --product (original_id)
CREATE UNIQUE INDEX "7ac4d56800d648ff9a97_ui" ON product (original_id) WHERE original_id IS NOT NULL;

UPDATE synchronization_from_mssql SET last_synchronization = 'epoch' WHERE type = 'PRODUCT';
