CREATE TABLE branch
(
    id               UUID PRIMARY KEY,
    original_id      INT NOT NULL,
    original_number  INT NOT NULL,
    name             TEXT NOT NULL,
    code             TEXT NOT NULL,
    created_at       TIMESTAMP NOT NULL,
    updated_at       TIMESTAMP NOT NULL,
    created_by       TEXT NOT NULL,
    updated_by       TEXT NOT NULL
);

CREATE UNIQUE INDEX "1344421fd63141a7957b_ui" ON branch(original_id);
CREATE UNIQUE INDEX "924970afc62f4c4da916_ui" ON branch(code);
