-- drop some obsolete 'DISdata' defaults
ALTER TABLE basket ALTER COLUMN created_by DROP DEFAULT;
ALTER TABLE basket ALTER COLUMN updated_by DROP DEFAULT;
ALTER TABLE basket_item ALTER COLUMN updated_by DROP DEFAULT;
ALTER TABLE basket_item ALTER COLUMN updated_by DROP DEFAULT;
ALTER TABLE reservation ALTER COLUMN updated_by DROP DEFAULT;
ALTER TABLE reservation ALTER COLUMN updated_by DROP DEFAULT;
ALTER TABLE ticket ALTER COLUMN updated_by DROP DEFAULT;
ALTER TABLE ticket ALTER COLUMN updated_by DROP DEFAULT;

-- auditorium
ALTER TABLE auditorium ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE auditorium ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- basket
ALTER TABLE basket ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE basket ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);
ALTER TABLE basket ALTER COLUMN deleted_at TYPE TIMESTAMPTZ USING timezone('UTC', deleted_at);
ALTER TABLE basket ALTER COLUMN paid_at TYPE TIMESTAMPTZ USING timezone('UTC', paid_at);

-- basket_item
ALTER TABLE basket_item ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE basket_item ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);
ALTER TABLE basket_item ALTER COLUMN deleted_at TYPE TIMESTAMPTZ USING timezone('UTC', deleted_at);

-- discount_card
ALTER TABLE discount_card ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE discount_card ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- discount_card_usage
ALTER TABLE discount_card_usage ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE discount_card_usage ALTER COLUMN deleted_at TYPE TIMESTAMPTZ USING timezone('UTC', deleted_at);
ALTER TABLE discount_card_usage ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- distributor
ALTER TABLE distributor ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE distributor ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);
ALTER TABLE distributor ALTER COLUMN deleted_at TYPE TIMESTAMPTZ USING timezone('UTC', deleted_at);

-- employee
ALTER TABLE employee ALTER COLUMN last_login_at TYPE TIMESTAMPTZ USING timezone('UTC', last_login_at);
ALTER TABLE employee ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE employee ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);
ALTER TABLE employee ALTER COLUMN accessible_at TYPE TIMESTAMPTZ USING timezone('UTC', accessible_at);
ALTER TABLE employee ALTER COLUMN deleted_at TYPE TIMESTAMPTZ USING timezone('UTC', deleted_at);

-- genre
ALTER TABLE genre ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE genre ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- jso
ALTER TABLE jso ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE jso ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- language
ALTER TABLE language ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE language ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- movie
ALTER TABLE movie ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE movie ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- outbox_event
ALTER TABLE outbox_event ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE outbox_event ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- pos_configuration
ALTER TABLE pos_configuration ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE pos_configuration ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- price_category
ALTER TABLE price_category ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE price_category ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- price_category_item
ALTER TABLE price_category_item ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE price_category_item ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- product
ALTER TABLE product ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE product ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- product_category
ALTER TABLE product_category ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE product_category ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- product_component
ALTER TABLE product_component ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE product_component ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- product_composition
ALTER TABLE product_composition ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE product_composition ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- production
ALTER TABLE production ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE production ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- rating
ALTER TABLE rating ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE rating ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- receipt
ALTER TABLE receipt ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE receipt ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- reservation
ALTER TABLE reservation ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE reservation ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);
ALTER TABLE reservation ALTER COLUMN deleted_at TYPE TIMESTAMPTZ USING timezone('UTC', deleted_at);

-- screening
ALTER TABLE screening ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE screening ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);
ALTER TABLE screening ALTER COLUMN deleted_at TYPE TIMESTAMPTZ USING timezone('UTC', deleted_at);

-- screening_type
ALTER TABLE screening_type ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE screening_type ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- screening_fee
ALTER TABLE screening_fee ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE screening_fee ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- seat
ALTER TABLE seat ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE seat ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- synchronization_from_mssql
ALTER TABLE synchronization_from_mssql ALTER COLUMN last_synchronization TYPE TIMESTAMPTZ USING timezone('UTC', last_synchronization);
ALTER TABLE synchronization_from_mssql ALTER COLUMN last_heartbeat TYPE TIMESTAMPTZ USING timezone('UTC', last_heartbeat);

-- table
ALTER TABLE "table" ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE "table" ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- technology
ALTER TABLE technology ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE technology ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- terminal_payment
ALTER TABLE terminal_payment ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE terminal_payment ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- ticket
ALTER TABLE ticket ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE ticket ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);
ALTER TABLE ticket ALTER COLUMN deleted_at TYPE TIMESTAMPTZ USING timezone('UTC', deleted_at);

-- ticket_discount
ALTER TABLE ticket_discount ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE ticket_discount ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- ticket_price
ALTER TABLE ticket_price ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE ticket_price ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);

-- tms_language
ALTER TABLE tms_language ALTER COLUMN created_at TYPE TIMESTAMPTZ USING timezone('UTC', created_at);
ALTER TABLE tms_language ALTER COLUMN updated_at TYPE TIMESTAMPTZ USING timezone('UTC', updated_at);
