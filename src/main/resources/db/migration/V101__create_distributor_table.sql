CREATE TABLE distributor
(
    id                UUID PRIMARY KEY,
    original_id       INT       NOT NULL,
    original_code     TEXT      NOT NULL,
    title             TEXT      NOT NULL,
    address_street    TEXT,
    address_city      TEXT,
    address_post_code TEXT,
    contact_name_1    TEXT,
    contact_name_2    TEXT,
    contact_name_3    TEXT,
    contact_phone_1   TEXT,
    contact_phone_2   TEXT,
    contact_phone_3   TEXT,
    contact_emails    TEXT[]    NOT NULL,
    bank_name         TEXT,
    bank_account      TEXT,
    id_number         TEXT,
    tax_id_number     TEXT,
    vat_rate          INT,
    note              TEXT,
    created_at        TIMESTAMP NOT NULL,
    updated_at        TIMESTAMP NOT NULL,
    deleted_at        TIMESTAMP,
    created_by        TEXT,
    updated_by        TEXT
);

CREATE UNIQUE INDEX "8502f91591b54726aba7_ui" ON distributor (original_id) WHERE deleted_at IS NULL;
CREATE UNIQUE INDEX "943f672ed9244044abf7_ui" ON distributor (original_code) WHERE deleted_at IS NULL;

