package com.cleevio.cinemax.api.module.discountcardusage.event.listener

import com.cleevio.cinemax.api.module.basket.event.MessagingBasketItemWithDiscountCardCreatedEvent
import com.cleevio.cinemax.api.module.basket.event.MssqlBasketItemWithDiscountCardCreatedEvent
import com.cleevio.cinemax.api.module.basketitem.event.BasketItemWithDiscountCardUpdatedEvent
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageJpaFinderService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageService
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateMessagingDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateMssqlDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.UpdateDiscountCardUsageCommand
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    "features.discount-card-usage.update.enabled",
    havingValue = "true",
    matchIfMissing = true
)
class BasketItemDiscountCardUsageEventListener(
    private val discountCardUsageJpaFinderService: DiscountCardUsageJpaFinderService,
    private val discountCardUsageService: DiscountCardUsageService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToBasketItemWithDiscountCardUpdatedEvent(event: BasketItemWithDiscountCardUpdatedEvent) {
        discountCardUsageJpaFinderService.findNonDeletedByDiscountCardIdAndBasketIdAndBasketIsInModifiableState(
            basketId = event.basketId,
            discountCardId = event.discountCardId
        )?.let {
            discountCardUsageService.updateDiscountCardUsage(
                UpdateDiscountCardUsageCommand(
                    discountCardUsageId = it.id,
                    basketItemId = event.basketItemId
                )
            )
        }
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToMssqlBasketItemWithDiscountCardCreatedEvent(event: MssqlBasketItemWithDiscountCardCreatedEvent) {
        discountCardUsageService.createMssqlDiscountCardUsage(
            CreateMssqlDiscountCardUsageCommand(
                discountCardId = event.discountCardId,
                screeningId = event.screeningId,
                basketId = event.basketId,
                ticketBasketItemId = event.ticketBasketItemId
            )
        )
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToMessagingBasketItemWithDiscountCardCreatedEvent(event: MessagingBasketItemWithDiscountCardCreatedEvent) {
        discountCardUsageService.createMessagingDiscountCardUsage(
            CreateMessagingDiscountCardUsageCommand(
                discountCardId = event.discountCardId,
                basketId = event.basketId,
                basketItemId = event.basketItemId
            )
        )
    }
}
