package com.cleevio.cinemax.api.module.stockmovement.service

import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementType
import com.cleevio.cinemax.api.module.stockmovement.exception.InvalidStockMovementTypeException
import com.cleevio.cinemax.api.module.stockmovement.service.StockMovementViewModelType.INPUT_VIEW_MODEL
import com.cleevio.cinemax.api.module.stockmovement.service.StockMovementViewModelType.OUTPUT_VIEW_MODEL
import com.cleevio.cinemax.api.module.stockmovement.service.query.AdminExportStockMovementsQuery
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class StockMovementExportService(
    private val adminExportStockMovementsQueryService: AdminExportStockMovementsQueryService,
    private val stockMovementXlsxExportResultMapper: StockMovementXlsxExportResultMapper,
) {

    fun exportStockMovements(@Valid query: AdminExportStockMovementsQuery): ExportResultModel =
        when (query.exportFormat) {
            ExportFormat.XLSX -> exportStockMovementsToXlsx(query)
            ExportFormat.XML -> throw UnsupportedOperationException("XML export is not supported.")
        }

    private fun exportStockMovementsToXlsx(query: AdminExportStockMovementsQuery): ExportResultModel =
        stockMovementXlsxExportResultMapper.mapToExportResultModel(
            data = adminExportStockMovementsQueryService(query),
            viewModelType = resolveStockMovementViewModel(query.filter.types),
            username = query.username,
            recordedAtFrom = query.filter.recordedAtFrom,
            recordedAtTo = query.filter.recordedAtTo
        )

    private fun resolveStockMovementViewModel(types: Set<StockMovementType>?): StockMovementViewModelType = when {
        types.isNullOrEmpty() -> throw InvalidStockMovementTypeException()
        types.all { it == StockMovementType.GOODS_RECEIPT } -> INPUT_VIEW_MODEL
        types.none { it == StockMovementType.GOODS_RECEIPT } -> OUTPUT_VIEW_MODEL
        else -> throw InvalidStockMovementTypeException()
    }
}

enum class StockMovementViewModelType {
    INPUT_VIEW_MODEL,
    OUTPUT_VIEW_MODEL,
}
