package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.common.constant.RESERVATION
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basket.service.command.DeleteBasketCommand
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationJpaFinderService
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationService
import com.cleevio.cinemax.api.module.groupreservation.service.command.DeleteGroupReservationCommand
import com.cleevio.cinemax.api.module.reservation.constant.ReservationLockValues.UPDATE_OR_DELETE_RESERVATION
import com.cleevio.cinemax.api.module.reservation.service.ReservationJpaFinderService
import com.cleevio.cinemax.api.module.reservation.service.ReservationService
import com.cleevio.cinemax.api.module.reservation.service.command.DeleteReservationCommand
import com.cleevio.cinemax.api.module.screening.service.command.WebDeleteGroupReservationCommand
import com.cleevio.library.lockinghandler.service.LockService
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated

@Service
@Validated
class WebDeleteGroupReservationService(
    private val basketJpaFinderService: BasketJpaFinderService,
    private val groupReservationService: GroupReservationService,
    private val groupReservationJpaFinderService: GroupReservationJpaFinderService,
    private val reservationJpaFinderService: ReservationJpaFinderService,
    private val basketService: BasketService,
    private val reservationService: ReservationService,

    @Suppress("SpringJavaInjectionPointsAutowiringInspection")
    private val lockService: LockService,
) {

    private val log = logger()

    @Transactional
    operator fun invoke(@Valid command: WebDeleteGroupReservationCommand) {
        if (!groupReservationJpaFinderService.existsOnlineNonDeletedById(command.groupReservationId)) {
            log.warn("Online group reservation id ${command.groupReservationId} already deleted or doesn't exist.")
            return
        }
        val reservations = reservationJpaFinderService.findAllNonDeletedByGroupReservationId(command.groupReservationId)
        // lock all reservation
        reservations.forEach { reservation ->
            lockService.obtainBlockingLock(
                RESERVATION,
                UPDATE_OR_DELETE_RESERVATION,
                reservation.id.toString()
            )
        }

        val basket = basketJpaFinderService.findWithBasketItemWithGroupReservationId(command.groupReservationId)
        basketService.deleteBasket(
            DeleteBasketCommand(basketId = basket.id)
        )

        reservations.forEach { reservation ->
            reservationService.deleteReservation(
                DeleteReservationCommand(reservation.id)
            )
        }

        groupReservationService.deleteGroupReservation(DeleteGroupReservationCommand(command.groupReservationId))
    }
}
