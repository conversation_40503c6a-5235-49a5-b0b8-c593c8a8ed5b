package com.cleevio.cinemax.api.module.receipt.model.request

import com.cleevio.cinemax.api.common.util.jackson.BigDecimalReceiptSerializer
import com.cleevio.cinemax.api.module.receipt.constant.ReceiptItemType
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import java.math.BigDecimal

data class SvkXmlReceiptItem(

    @field:JacksonXmlProperty(localName = "NameF", isAttribute = true)
    @field:JsonInclude(JsonInclude.Include.NON_NULL)
    val name: String?,

    @field:JacksonXmlProperty(localName = "QR", isAttribute = true)
    @field:JsonInclude(JsonInclude.Include.NON_NULL)
    val qr: String?,

    @field:JacksonXmlProperty(localName = "Name", isAttribute = true)
    val description: String,

    @field:JacksonXmlProperty(localName = "ItemType", isAttribute = true)
    val type: ReceiptItemType,

    @field:JacksonXmlProperty(localName = "Quantity", isAttribute = true)
    @field:JsonSerialize(using = BigDecimalReceiptSerializer::class)
    val quantity: BigDecimal,

    @field:JacksonXmlProperty(localName = "VatRate", isAttribute = true)
    val vatRate: Int,

    @field:JacksonXmlProperty(localName = "UnitPrice", isAttribute = true)
    val unitPrice: BigDecimal,

    @field:JacksonXmlProperty(localName = "Price", isAttribute = true)
    val price: BigDecimal,

    @field:JacksonXmlProperty(localName = "ReferenceReceiptId", isAttribute = true)
    @field:JsonInclude(JsonInclude.Include.NON_NULL)
    val referenceReceiptNumber: String? = null,
)
