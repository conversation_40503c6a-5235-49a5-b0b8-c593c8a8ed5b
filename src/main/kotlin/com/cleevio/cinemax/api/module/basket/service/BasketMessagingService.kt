package com.cleevio.cinemax.api.module.basket.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.util.associateByNotNull
import com.cleevio.cinemax.api.common.util.associateNotNull
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.module.auditorium.entity.Auditorium
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumJpaFinderService
import com.cleevio.cinemax.api.module.basket.event.BasketPaidMessagingEvent
import com.cleevio.cinemax.api.module.basket.model.BasketModel
import com.cleevio.cinemax.api.module.basket.model.PRODUCT_BASKET_ITEM_TYPES
import com.cleevio.cinemax.api.module.basket.model.productAndDiscountItemIds
import com.cleevio.cinemax.api.module.basket.model.ticketItemIds
import com.cleevio.cinemax.api.module.basket.service.command.ReportBasketToHeadquartersCommand
import com.cleevio.cinemax.api.module.basket.util.validatePaidBasketState
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemJpaFinderService
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardJpaFinderService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageJpaFinderService
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.service.ProductJpaFinderService
import com.cleevio.cinemax.api.module.screening.entity.Screening
import com.cleevio.cinemax.api.module.screening.service.ScreeningJpaFinderService
import com.cleevio.cinemax.api.module.seat.service.SeatJpaFinderService
import com.cleevio.cinemax.api.module.ticket.entity.Ticket
import com.cleevio.cinemax.api.module.ticket.service.TicketJpaFinderService
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountJpaFinderService
import com.cleevio.cinemax.api.module.ticketprice.entity.TicketPrice
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceJpaFinderService
import jakarta.validation.Valid
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.time.LocalDateTime
import java.util.UUID

@Service
@Validated
class BasketMessagingService(
    private val basketFinderService: BasketFinderService,
    private val basketJpaFinderService: BasketJpaFinderService,
    private val basketItemJpaFinderService: BasketItemJpaFinderService,
    private val ticketFinderService: TicketJpaFinderService,
    private val ticketPriceFinderService: TicketPriceJpaFinderService,
    private val ticketDiscountFinderService: TicketDiscountJpaFinderService,
    private val auditoriumFinderService: AuditoriumJpaFinderService,
    private val seatFinderService: SeatJpaFinderService,
    private val discountCardUsageFinderService: DiscountCardUsageJpaFinderService,
    private val discountCardFinderService: DiscountCardJpaFinderService,
    private val screeningFinderService: ScreeningJpaFinderService,
    private val productFinderService: ProductJpaFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
) {
    private val log = logger()

    fun reportBasketToHeadquarters(@Valid command: ReportBasketToHeadquartersCommand) {
        log.info("Reporting Basket id=${command.basketId} to headquarters...")
        val basketModel = basketFinderService.getNonDeletedWithItemsById(command.basketId).also {
            validatePaidBasketState(basket = it.basket)
        }

        val ticketIdToTicket = ticketFinderService.findAllNonDeletedByIdIn(
            ids = basketModel.ticketItemIds()
        ).associateByNotNull { it.id }

        val ticketPriceIdToTicketPrice = ticketPriceFinderService.findAllByIdIn(
            ids = ticketIdToTicket.values.map { it.ticketPriceId }.toSet()
        ).associateByNotNull { it.id }

        val screeningIdToScreening = screeningFinderService.findAllNonDeletedByIdIn(
            ids = ticketIdToTicket.values.map { it.screeningId }.toSet()
        ).associateByNotNull { it.id }

        val auditoriumIdToAuditorium = auditoriumFinderService.findAll().associateByNotNull { it.id }
        val seatIdToOriginalId = seatFinderService.findAllByIdIn(
            ids = ticketPriceIdToTicketPrice.values.map { it.seatId }.toSet()
        ).associateNotNull { it.id to it.originalId }

        val ticketDiscountIds = ticketIdToTicket.values.mapNotNull { it.ticketDiscountPrimaryId } +
            ticketIdToTicket.values.mapNotNull { it.ticketDiscountSecondaryId }
        val ticketDiscountIdToCode = ticketDiscountFinderService.findAllNonDeletedById(
            ids = ticketDiscountIds.toSet()
        ).associateNotNull { it.id to it.code }

        val discountCardUsages = discountCardUsageFinderService.findAllNonDeletedByBasketIdIn(setOf(command.basketId))
        val basketItemIdToDiscountCardId = discountCardUsages.associateNotNull { it.ticketBasketItemId to it.discountCardId } +
            discountCardUsages.associateNotNull { it.productBasketItemId to it.discountCardId } +
            discountCardUsages.associateNotNull { it.productDiscountBasketItemId to it.discountCardId }
        val discountCardIdToCode = discountCardFinderService.findAllByIdIn(
            ids = basketItemIdToDiscountCardId.values.toSet()
        ).associateNotNull { it.id to it.code }
        val basketItemIdToDiscountCardCode = basketItemIdToDiscountCardId
            .mapValues { discountCardIdToCode[it.value] as String }

        val productIdToProduct = productFinderService.findAllNonDeletedByIdIn(
            ids = basketModel.productAndDiscountItemIds()
        ).associateByNotNull { it.id }

        applicationEventPublisher.publishEvent(
            basketModel.toMessagingEvent(
                ticketIdToTicket = ticketIdToTicket,
                ticketDiscountIdToCode = ticketDiscountIdToCode,
                ticketPriceIdToTicketPrice = ticketPriceIdToTicketPrice,
                auditoriumIdToAuditorium = auditoriumIdToAuditorium,
                seatIdToOriginalId = seatIdToOriginalId,
                basketItemIdToDiscountCardCode = basketItemIdToDiscountCardCode,
                screeningIdToScreening = screeningIdToScreening,
                productIdToProduct = productIdToProduct,
                branchCode = cinemaxConfigProperties.branchCode
            )
        )
    }

    // TODO: refactor to reuse code from reportBasketToHeadquarters
    fun syncBasketsToHeadquarters(
        paidAtFrom: LocalDateTime,
        paidAtTo: LocalDateTime,
    ) {
        val baskets = basketJpaFinderService.findAllNonDeletedByPaidAtIn(
            paidAtFrom = paidAtFrom,
            paidAtTo = paidAtTo
        )
        val basketIdToBasketItems = basketItemJpaFinderService.findAllNonDeletedByBasketIdIn(
            basketIds = baskets.mapToSet { it.id }
        ).groupBy { it.basketId }

        val basketModels = baskets.map {
            BasketModel(
                basket = it,
                basketItems = basketIdToBasketItems[it.id] ?: run {
                    log.error("Trying to sync basket ${it.id} with no items to headquarters.")
                    listOf()
                }
            )
        }

        val ticketIdToTicket = ticketFinderService.findAllNonDeletedByIdIn(
            ids = basketModels.flatMap { it.ticketItemIds() }.toSet()
        ).associateByNotNull { it.id }

        val ticketPriceIdToTicketPrice = ticketPriceFinderService.findAllByIdIn(
            ids = ticketIdToTicket.values.map { it.ticketPriceId }.toSet()
        ).associateByNotNull { it.id }

        val screeningIdToScreening = screeningFinderService.findAllNonDeletedByIdIn(
            ids = ticketIdToTicket.values.map { it.screeningId }.toSet()
        ).associateByNotNull { it.id }

        val auditoriumIdToAuditorium = auditoriumFinderService.findAll().associateByNotNull { it.id }
        val seatIdToOriginalId = seatFinderService.findAllByIdIn(
            ids = ticketPriceIdToTicketPrice.values.map { it.seatId }.toSet()
        ).associateNotNull { it.id to it.originalId }

        val ticketDiscountIds = ticketIdToTicket.values.mapNotNull { it.ticketDiscountPrimaryId } +
            ticketIdToTicket.values.mapNotNull { it.ticketDiscountSecondaryId }
        val ticketDiscountIdToCode = ticketDiscountFinderService.findAllNonDeletedById(
            ids = ticketDiscountIds.toSet()
        ).associateNotNull { it.id to it.code }

        val discountCardUsages = discountCardUsageFinderService.findAllNonDeletedByBasketIdIn(basketIdToBasketItems.keys)
        val basketItemIdToDiscountCardId = discountCardUsages.associateNotNull { it.ticketBasketItemId to it.discountCardId } +
            discountCardUsages.associateNotNull { it.productBasketItemId to it.discountCardId } +
            discountCardUsages.associateNotNull { it.productDiscountBasketItemId to it.discountCardId }
        val discountCardIdToCode = discountCardFinderService.findAllByIdIn(
            ids = basketItemIdToDiscountCardId.values.toSet()
        ).associateNotNull { it.id to it.code }
        val basketItemIdToDiscountCardCode = basketItemIdToDiscountCardId
            .mapValues { discountCardIdToCode[it.value] as String }

        val productIdToProduct = productFinderService.findAllNonDeletedByIdIn(
            ids = basketModels.flatMap { it.productAndDiscountItemIds() }.toSet()
        ).associateByNotNull { it.id }

        basketModels.forEach { basketModel ->
            applicationEventPublisher.publishEvent(
                basketModel.toMessagingEvent(
                    ticketIdToTicket = ticketIdToTicket,
                    ticketDiscountIdToCode = ticketDiscountIdToCode,
                    ticketPriceIdToTicketPrice = ticketPriceIdToTicketPrice,
                    auditoriumIdToAuditorium = auditoriumIdToAuditorium,
                    seatIdToOriginalId = seatIdToOriginalId,
                    basketItemIdToDiscountCardCode = basketItemIdToDiscountCardCode,
                    screeningIdToScreening = screeningIdToScreening,
                    productIdToProduct = productIdToProduct,
                    branchCode = cinemaxConfigProperties.branchCode
                )
            )
        }
    }
}

fun BasketModel.toMessagingEvent(
    ticketIdToTicket: Map<UUID, Ticket>,
    ticketDiscountIdToCode: Map<UUID, String>,
    ticketPriceIdToTicketPrice: Map<UUID, TicketPrice>,
    auditoriumIdToAuditorium: Map<UUID, Auditorium>,
    seatIdToOriginalId: Map<UUID, Int>,
    basketItemIdToDiscountCardCode: Map<UUID, String>,
    screeningIdToScreening: Map<UUID, Screening>,
    productIdToProduct: Map<UUID, Product>,
    branchCode: String,
) = BasketPaidMessagingEvent(
    id = this.basket.id,
    totalPrice = this.basket.totalPrice,
    state = this.basket.state,
    paidAt = this.basket.paidAt ?: error("Basket without 'paidAt' can't be mapped to BasketPaidMessagingEvent."),
    paymentType = this.basket.paymentType ?: error("Basket without 'paymentType' can't be mapped to BasketPaidMessagingEvent."),
    variableSymbol = this.basket.variableSymbol,
    branchCode = branchCode,
    items = this.basketItems.map { basketItem ->
        BasketPaidMessagingEvent.BasketItem(
            id = basketItem.id,
            type = basketItem.type,
            price = basketItem.price,
            quantity = basketItem.quantity,
            isCancelled = basketItem.isCancelled,
            discountCardCode = basketItemIdToDiscountCardCode[basketItem.id],
            product = if (basketItem.type in PRODUCT_BASKET_ITEM_TYPES) {
                val product = productIdToProduct[basketItem.productId]
                    ?: error("Can't find respective Product for BasketItem ${basketItem.id}.")
                val receiptNumber = basketItem.productReceiptNumber
                    ?: error("BasketItem with PRODUCT or PRODUCT_DISCOUNT type must have 'productReceiptNumber'.")

                BasketPaidMessagingEvent.BasketProductItem(
                    productCode = product.code,
                    productReceiptNumber = receiptNumber
                )
            } else {
                null
            },
            ticket = if (basketItem.type == BasketItemType.TICKET) {
                val ticket = ticketIdToTicket[basketItem.ticketId]
                    ?: error("Can't find respective Ticket for BasketItem ${basketItem.id}.")
                val screening = screeningIdToScreening[ticket.screeningId]
                    ?: error("Can't find Screening's originalId for Ticket ${ticket.id}.")
                val ticketPrice = ticketPriceIdToTicketPrice[ticket.ticketPriceId]
                    ?: error("Can't find TicketPrice for Ticket ${ticket.id}.")
                val auditorium = auditoriumIdToAuditorium[screening.auditoriumId]
                    ?: error("Can't find Auditorium for Auditorium ${ticket.id}.")
                val seatOriginalId = seatIdToOriginalId[ticketPrice.seatId]
                    ?: error("Can't find Seat's originalId for TicketPrice ${ticketPrice.id}.")

                BasketPaidMessagingEvent.BasketTicketItem(
                    id = basketItem.ticketId ?: error("BasketItem#ticketId for type TICKET is null."),
                    screeningId = screening.id,
                    auditoriumOriginalCode = auditorium.originalCode,
                    seatOriginalId = seatOriginalId,
                    basePriceItemNumber = ticketPrice.basePriceItemNumber,
                    ticketReceiptNumber = ticket.receiptNumber ?: error("Ticket has null receiptNumber."),
                    ticketDiscountPrimaryCode = ticketDiscountIdToCode[ticket.ticketDiscountPrimaryId],
                    ticketDiscountSecondaryCode = ticketDiscountIdToCode[ticket.ticketDiscountSecondaryId],
                    isGroupTicket = ticket.isGroupTicket,
                    isUsed = ticket.isUsed,
                    includes3dGlasses = ticket.includes3dGlasses
                )
            } else {
                null
            }
        )
    }
)
