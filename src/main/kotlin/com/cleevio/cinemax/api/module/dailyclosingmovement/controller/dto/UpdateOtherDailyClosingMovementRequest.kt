package com.cleevio.cinemax.api.module.dailyclosingmovement.controller.dto

import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.command.UpdateOtherDailyClosingMovementCommand
import java.math.BigDecimal
import java.util.UUID

data class UpdateOtherDailyClosingMovementRequest(
    val title: String,
    val type: DailyClosingMovementType,
    val itemType: DailyClosingMovementItemType,
    val paymentType: PaymentType,
    val variableSymbol: String? = null,
    val otherReceiptNumber: String? = null,
    val amount: BigDecimal,
) {

    fun toCommand(dailyClosingId: UUID, dailyClosingMovementId: UUID) =
        UpdateOtherDailyClosingMovementCommand(
            id = dailyClosingMovementId,
            dailyClosingId = dailyClosingId,
            title = title,
            type = type,
            itemType = itemType,
            paymentType = paymentType,
            variableSymbol = variableSymbol,
            otherReceiptNumber = otherReceiptNumber,
            amount = amount
        )
}
