package com.cleevio.cinemax.api.module.basket.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.module.basket.controller.dto.WebBasketTicketsResponse
import com.cleevio.cinemax.api.module.basket.controller.dto.WebCompleteBasketPaymentRequest
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basket.service.GetWebBasketTicketsQueryService
import com.cleevio.cinemax.api.module.basket.service.command.WebCompleteBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.query.GetWebBasketTicketsQuery
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@RestController
@RequestMapping("/web-app/baskets")
class WebBasketController(
    private val basketService: BasketService,
    private val getWebBasketTicketsQueryService: GetWebBasketTicketsQueryService,
) {

    @PostMapping("/{basketId}/payment", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun completeBasketPayment(
        @PathVariable basketId: UUID,
        @RequestBody request: WebCompleteBasketPaymentRequest,
    ) {
        basketService.webCompleteWebBasketPayment(
            WebCompleteBasketPaymentCommand(
                basketId = basketId,
                variableSymbol = request.variableSymbol
            )
        )
    }

    @GetMapping("/{basketId}/tickets", produces = [ApiVersion.VERSION_1_JSON])
    fun getBasketTickets(
        @PathVariable basketId: UUID,
    ): WebBasketTicketsResponse {
        return getWebBasketTicketsQueryService(GetWebBasketTicketsQuery(basketId))
    }
}
