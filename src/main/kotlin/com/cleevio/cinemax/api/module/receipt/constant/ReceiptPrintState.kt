package com.cleevio.cinemax.api.module.receipt.constant

enum class ReceiptPrintState {
    CREATED, // when the file is generated and called in.xml
    PROCESSING, // when printer starts to process in.xml, file is renamed to in$.xml
    PROCESSED, // after printer finishes processing, out.xml is created
    SUCCESS, // when out.xml does not contain "Success=false" header
    FAILED, // out.xml contains "Success=false" header or substring
    NO_FILES, // when there's no file in receipts directory
    MULTIPLE_FILES, // when there are multiple files in receipts directory
    UNKNOWN, // unknown filename
}
