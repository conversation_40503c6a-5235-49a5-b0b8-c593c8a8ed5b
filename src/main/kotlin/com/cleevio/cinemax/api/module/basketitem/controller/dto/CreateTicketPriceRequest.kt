package com.cleevio.cinemax.api.module.basketitem.controller.dto

import com.cleevio.cinemax.api.module.basket.service.command.CreateTicketPriceInput
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber

data class CreateTicketPriceRequest(
    val priceCategoryItemNumber: PriceCategoryItemNumber,
) {
    fun toInput() = CreateTicketPriceInput(
        priceCategoryItemNumber = priceCategoryItemNumber
    )
}
