package com.cleevio.cinemax.api.module.receipt.model.request

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

data class SvkXmlHeader(

    @field:JacksonXmlProperty(localName = "Version", isAttribute = true)
    val version: String = "1.0",

    @field:JacksonXmlProperty(localName = "Uuid", isAttribute = true)
    val uuid: String,

    @field:JacksonXmlProperty(localName = "ExpectResponse", isAttribute = true)
    val expectResponse: Boolean = true,

    @field:JacksonXmlProperty(localName = "SwId", isAttribute = true)
    val swId: String = "12200.82 (11.3.2019)",
)
