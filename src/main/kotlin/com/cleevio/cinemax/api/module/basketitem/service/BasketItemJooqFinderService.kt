package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.exception.BasketItemNotFoundException
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class BasketItemJooqFinderService(
    private val basketItemJooqFinderRepository: BasketItemJooqFinderRepository,
) {

    fun getById(id: UUID): BasketItem = basketItemJooqFinderRepository.findById(id) ?: throw BasketItemNotFoundException()

    fun findAll(): List<BasketItem> = basketItemJooqFinderRepository.findAll()
}
