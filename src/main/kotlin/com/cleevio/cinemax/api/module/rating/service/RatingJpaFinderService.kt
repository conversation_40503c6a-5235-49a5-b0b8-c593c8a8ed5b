package com.cleevio.cinemax.api.module.rating.service

import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.rating.exception.RatingNotFoundException
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class RatingJpaFinderService(
    private val ratingRepository: RatingRepository,
) {

    fun findAll(): List<Rating> = ratingRepository.findAll()

    fun findByCode(code: String): Rating? = ratingRepository.findByCode(code)

    fun findById(id: UUID): Rating? = ratingRepository.findById(id).orElse(null)

    fun getById(id: UUID): Rating = findById(id) ?: throw RatingNotFoundException()

    fun findAllByIdIn(ids: Set<UUID>): List<Rating> = ratingRepository.findAllById(ids)
}
