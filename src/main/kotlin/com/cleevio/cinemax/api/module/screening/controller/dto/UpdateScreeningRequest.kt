package com.cleevio.cinemax.api.module.screening.controller.dto

import com.cleevio.cinemax.api.module.screening.service.command.CreateOrUpdateScreeningCommand
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID

data class UpdateScreeningRequest(
    val movieId: UUID,
    val auditoriumId: UUID,
    val priceCategoryId: UUID,
    val auditoriumLayoutId: UUID,
    val screeningTypeIds: Set<UUID>,
    val date: LocalDate,

    @field:Schema(type = "string", pattern = "HH:mm:ss")
    val time: LocalTime,
    val saleTimeLimit: Int,
    val adTimeSlot: Int,
    val stopped: Boolean,
    val cancelled: Boolean,
    val surchargeVip: BigDecimal,
    val surchargePremium: BigDecimal,
    val surchargeImax: BigDecimal,
    val surchargeUltraX: BigDecimal,
    val serviceFeeVip: BigDecimal,
    val serviceFeePremium: BigDecimal,
    val serviceFeeImax: BigDecimal,
    val serviceFeeUltraX: BigDecimal,
    val surchargeDBox: BigDecimal,
    val serviceFeeGeneral: BigDecimal,
    val proCommission: Int,
    val filmFondCommission: Int,
    val distributorCommission: Int,
    val publishOnline: Boolean,
) {
    fun toCommand(screeningId: UUID): CreateOrUpdateScreeningCommand = CreateOrUpdateScreeningCommand(
        id = screeningId,
        movieId = movieId,
        auditoriumId = auditoriumId,
        priceCategoryId = priceCategoryId,
        auditoriumLayoutId = auditoriumLayoutId,
        screeningTypeIds = screeningTypeIds,
        date = date,
        time = time,
        saleTimeLimit = saleTimeLimit,
        adTimeSlot = adTimeSlot,
        surchargeVip = surchargeVip,
        surchargePremium = surchargePremium,
        surchargeImax = surchargeImax,
        surchargeUltraX = surchargeUltraX,
        serviceFeeVip = serviceFeeVip,
        serviceFeePremium = serviceFeePremium,
        serviceFeeImax = serviceFeeImax,
        serviceFeeUltraX = serviceFeeUltraX,
        surchargeDBox = surchargeDBox,
        serviceFeeGeneral = serviceFeeGeneral,
        proCommission = proCommission,
        filmFondCommission = filmFondCommission,
        distributorCommission = distributorCommission,
        publishOnline = publishOnline,
        stopped = stopped,
        cancelled = cancelled
    )
}
