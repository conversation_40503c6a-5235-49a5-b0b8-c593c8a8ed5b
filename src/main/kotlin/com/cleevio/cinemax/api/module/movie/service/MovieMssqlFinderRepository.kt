package com.cleevio.cinemax.api.module.movie.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.DeploymentType
import com.cleevio.cinemax.api.common.util.buildGreaterThanDateTimeMssqlCondition
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables.RFILM
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Rfilm
import org.jooq.DSLContext
import org.jooq.impl.DSL.length
import org.jooq.impl.DSL.ltrim
import org.jooq.impl.DSL.rtrim
import org.jooq.impl.DSL.selectFrom
import org.jooq.impl.DSL.table
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class MovieMssqlFinderRepository(
    private val mssqlCinemaxDslContext: DSLContext,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
) {

    fun findAll(): List<Rfilm> {
        return mssqlCinemaxDslContext
            .selectFrom(RFILM)
            .fetchInto(Rfilm::class.java)
    }

    fun findAllByUpdatedAtGt(updatedAt: LocalDateTime? = null): List<Rfilm> {
        return when (cinemaxConfigProperties.deploymentType) {
            DeploymentType.HEADQUARTERS -> {
                mssqlCinemaxDslContext
                    .select(table(RFILM.name).asterisk().except(RFILM.FORM_2D, RFILM.FORM_3D, RFILM.JAZYK, RFILM.CFOTRM))
                    .from(RFILM.name)
                    .where(buildGreaterThanDateTimeMssqlCondition(updatedAt, RFILM.ZCAS))
                    .fetchInto(Rfilm::class.java)
            }

            DeploymentType.BRANCH -> {
                mssqlCinemaxDslContext
                    .selectFrom(RFILM)
                    .where(buildGreaterThanDateTimeMssqlCondition(updatedAt, RFILM.ZCAS))
                    .fetchInto(Rfilm::class.java)
            }
        }
    }

    fun findByOriginalId(originalId: Int): Rfilm? {
        return mssqlCinemaxDslContext
            .selectFrom(RFILM)
            .where(RFILM.RFILMID.eq(originalId))
            .fetchOneInto(Rfilm::class.java)
    }

    fun existsByOriginalId(originalId: Int): Boolean {
        return mssqlCinemaxDslContext
            .fetchExists(
                selectFrom(RFILM)
                    .where(RFILM.RFILMID.eq(originalId))
            )
    }

    fun findDisfilmCodeToOriginalId(): Map<String, Int> {
        return mssqlCinemaxDslContext
            .select(
                RFILM.RFILMID,
                RFILM.CISFILMUFD
            )
            .from(RFILM)
            .where(length(ltrim(rtrim(RFILM.CISFILMUFD))).gt(0))
            .fetch()
            .map { it.component2().trim() to it.component1() }
            .toMap()
    }
}
