package com.cleevio.cinemax.api.module.ticketprice.util

import com.cleevio.cinemax.api.common.util.isGreaterThan
import com.cleevio.cinemax.api.module.screeningfee.entity.ScreeningFee
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import java.math.BigDecimal

fun mapSeatSurchargeTypeAndPrice(screeningFee: ScreeningFee, seatType: SeatType): Pair<SeatSurchargeType, BigDecimal>? {
    return when (seatType) {
        SeatType.VIP -> Pair(SeatSurchargeType.VIP, screeningFee.surchargeVip)
        SeatType.PREMIUM_PLUS -> Pair(SeatSurchargeType.PREMIUM_PLUS, screeningFee.surchargePremium)
        SeatType.DBOX -> if (screeningFee.hasZeroDBoxSurcharge()) {
            return Pair(SeatSurchargeType.PREMIUM_PLUS, screeningFee.surchargePremium)
        } else {
            return Pair(SeatSurchargeType.DBOX, screeningFee.surchargeDBox)
        }
        else -> null
    }
}

fun mapAuditoriumSurchargeTypeAndPrice(screeningFee: ScreeningFee): Pair<AuditoriumSurchargeType, BigDecimal>? {
    if (screeningFee.surchargeImax isGreaterThan BigDecimal.ZERO) {
        return Pair(AuditoriumSurchargeType.IMAX, screeningFee.surchargeImax)
    }
    if (screeningFee.surchargeUltraX isGreaterThan BigDecimal.ZERO) {
        return Pair(AuditoriumSurchargeType.ULTRA_X, screeningFee.surchargeUltraX)
    }
    return null
}

fun mapSeatServiceFeeTypeAndPrice(
    screeningFee: ScreeningFee,
    seatType: SeatType,
): Pair<SeatServiceFeeType, BigDecimal>? {
    return when (seatType) {
        SeatType.VIP -> Pair(SeatServiceFeeType.VIP, screeningFee.serviceFeeVip)
        SeatType.PREMIUM_PLUS -> Pair(SeatServiceFeeType.PREMIUM_PLUS, screeningFee.serviceFeePremium)
        SeatType.DBOX -> if (screeningFee.hasZeroDBoxSurcharge()) {
            Pair(SeatServiceFeeType.PREMIUM_PLUS, screeningFee.serviceFeePremium)
        } else {
            null
        }
        else -> null
    }
}

fun mapAuditoriumServiceFeeTypeAndPrice(screeningFee: ScreeningFee): Pair<AuditoriumServiceFeeType, BigDecimal>? {
    if (screeningFee.serviceFeeImax isGreaterThan BigDecimal.ZERO) {
        return Pair(AuditoriumServiceFeeType.IMAX, screeningFee.serviceFeeImax)
    }
    if (screeningFee.serviceFeeUltraX isGreaterThan BigDecimal.ZERO) {
        return Pair(AuditoriumServiceFeeType.ULTRA_X, screeningFee.serviceFeeUltraX)
    }
    return null
}
