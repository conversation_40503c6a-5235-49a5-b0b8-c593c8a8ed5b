package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.ExportCurrency
import com.cleevio.cinemax.api.common.constant.ExportLanguage
import com.cleevio.cinemax.api.common.service.getLocalizedName
import com.cleevio.cinemax.api.common.service.model.ExportMainHeaderModel
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.common.service.model.ListExportModel
import com.cleevio.cinemax.api.common.util.CURRENCY
import com.cleevio.cinemax.api.common.util.columnHeader
import com.cleevio.cinemax.api.common.util.createBasicCellStyle
import com.cleevio.cinemax.api.common.util.createCell
import com.cleevio.cinemax.api.common.util.createDecimalCell
import com.cleevio.cinemax.api.common.util.createHeaderStyle
import com.cleevio.cinemax.api.common.util.createNumberStyle
import com.cleevio.cinemax.api.common.util.createSummarySection
import com.cleevio.cinemax.api.common.util.mainHeader
import com.cleevio.cinemax.api.common.util.titleTableRow
import com.cleevio.cinemax.api.common.util.toCents
import com.cleevio.cinemax.api.common.util.toDateString
import com.cleevio.cinemax.api.common.util.toTimeString
import com.cleevio.cinemax.api.common.util.translate
import com.cleevio.cinemax.api.common.util.valueTableRow
import com.cleevio.cinemax.api.common.util.withCurrency
import com.cleevio.cinemax.api.module.basketitem.model.TicketBasketItemExportRecordModel
import com.cleevio.cinemax.api.module.basketitem.model.TicketBasketItemsSummaryExportRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.model.Section
import com.cleevio.cinemax.api.module.dailyclosing.model.SectionExportModel
import org.apache.poi.xssf.streaming.SXSSFWorkbook
import org.springframework.stereotype.Component
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.time.LocalDate

@Component
class TicketBasketItemXlsxExportResultMapper(
    private val cinemaxConfigProperties: CinemaxConfigProperties,
) {

    fun mapToExportResultModel(
        data: Pair<List<TicketBasketItemExportRecordModel>, TicketBasketItemsSummaryExportRecordModel>,
        username: String,
        basketPaidAtDateFrom: LocalDate?,
        basketPaidAtDateTo: LocalDate?,
    ): ExportResultModel {
        val exportLanguage = ExportLanguage.fromBusinessCountry(cinemaxConfigProperties.businessCountry)
        val header = getTicketBasketItemsExportHeader(
            username = username,
            basketPaidAtDateFrom = basketPaidAtDateFrom,
            basketPaidAtDateTo = basketPaidAtDateTo,
            branchName = cinemaxConfigProperties.branchName
        )
        val listExportModel = ListExportModel(
            header = header,
            columnNames = getTicketBasketItemsExportColumnNames(),
            columnData = data.first
        )

        val summaryExportModel = SectionExportModel(
            header = header,
            sections = getTicketBasketItemsSections(
                language = exportLanguage,
                data = data.second
            )
        )

        val listModelColumnTitles = listExportModel.columnNames[exportLanguage]
            ?: error("Export language for column titles not found.")
        val listModelMainHeader = listExportModel.header[exportLanguage]
            ?: error("Export language for main header not found.")

        val workbook = SXSSFWorkbook(50)
        try {
            val headerStyle = workbook.createHeaderStyle()
            val cellStyle = workbook.createBasicCellStyle()
            val numberStyle = workbook.createNumberStyle()

            val listSheet = workbook.createSheet((listModelMainHeader as ExportMainHeaderModel).exportTitle)
            listSheet.mainHeader(mainHeader = listModelMainHeader)
            listSheet.columnHeader(columnTitles = listModelColumnTitles)

            var rowNum = 5
            for (item in listExportModel.columnData) {
                val row = listSheet.createRow(rowNum++)
                with(row) {
                    createCell(0, item.basketPaidAtDate.toDateString(), cellStyle)
                    createCell(1, item.basketPaidAtTime.toTimeString(), cellStyle)
                    createCell(2, item.receiptNumber ?: "", cellStyle)
                    createCell(3, item.paymentPosConfigurationTitle ?: "", cellStyle)
                    createCell(4, item.basketUpdatedBy, cellStyle)
                    createCell(5, item.auditoriumCode, cellStyle)
                    createCell(6, item.branchName, cellStyle)
                    createCell(7, item.distributorTitle, cellStyle)
                    createCell(8, item.screeningDate.toDateString(), cellStyle)
                    createCell(9, item.screeningTime.toTimeString(), cellStyle)
                    createCell(10, item.movieRawTitle, cellStyle)
                    createCell(11, item.isUsed.getLocalizedName(exportLanguage), cellStyle)
                    createCell(12, item.paymentType.getLocalizedName(exportLanguage), cellStyle)
                    createCell(13, item.seatRow, cellStyle)
                    createCell(14, item.seatNumber, cellStyle)
                    createDecimalCell(15, item.basePrice.toCents(), numberStyle)
                    createDecimalCell(16, item.surchargeVip.toCents(), numberStyle)
                    createDecimalCell(17, item.surchargePremium.toCents(), numberStyle)
                    createDecimalCell(18, item.surchargeImax.toCents(), numberStyle)
                    createDecimalCell(19, item.surchargeUltraX.toCents(), numberStyle)
                    createDecimalCell(20, item.surchargeDBox.toCents(), numberStyle)
                    createDecimalCell(21, item.serviceFeeVip.toCents(), numberStyle)
                    createDecimalCell(22, item.serviceFeePremium.toCents(), numberStyle)
                    createDecimalCell(23, item.serviceFeeImax.toCents(), numberStyle)
                    createDecimalCell(24, item.serviceFeeUltraX.toCents(), numberStyle)
                    createDecimalCell(25, item.serviceFeeGeneral.toCents(), numberStyle)
                    createCell(26, item.isCancelled.getLocalizedName(exportLanguage), cellStyle)
                    createCell(27, item.isPriceCategoryItemDiscounted.getLocalizedName(exportLanguage), cellStyle)
                    createCell(28, item.primaryTicketDiscountCode ?: "", cellStyle)
                    createCell(29, item.secondaryTicketDiscountCode ?: "", cellStyle)
                    createCell(30, item.discountCardType?.getLocalizedName(exportLanguage) ?: "", cellStyle)
                    createCell(31, item.discountCardTitle ?: "", cellStyle)
                    createCell(32, item.discountCardCode ?: "", cellStyle)
                    createDecimalCell(33, item.proDeduction.toCents(), numberStyle)
                    createDecimalCell(34, item.filmFondDeduction.toCents(), numberStyle)
                    createDecimalCell(35, item.distributorDeduction.toCents(), numberStyle)
                    createDecimalCell(36, item.taxAmount.toCents(), numberStyle)
                    createCell(37, item.includes3dGlasses.getLocalizedName(exportLanguage), cellStyle)
                }
            }

            val summarySheet = workbook.createSheet(
                translationsMap[exportLanguage]?.get(SUMMARY_SHEET_TITLE)
                    ?: error("Export language for summary sheet title not found.")
            )

            summarySheet.mainHeader(
                mainHeader = listModelMainHeader.copy(dataColumnsCount = 8)
            )
            summarySheet.createSummarySection(
                summaryModel = summaryExportModel,
                headerStyle = headerStyle
            )

            for (i in listModelColumnTitles.indices) {
                listSheet.trackColumnForAutoSizing(i)
                listSheet.autoSizeColumn(i)

                val currentWidth = listSheet.getColumnWidth(i)
                when (currentWidth) {
                    in 0..500 -> listSheet.setColumnWidth(i, currentWidth * 8)
                    in 501..750 -> listSheet.setColumnWidth(i, currentWidth * 3)
                    in 751..1250 -> listSheet.setColumnWidth(i, (currentWidth * 1.5).toInt())
                    in 1251..1750 -> listSheet.setColumnWidth(i, (currentWidth * 1.25).toInt())
                    else -> {}
                }
            }

            ByteArrayOutputStream().use { outputStream ->
                workbook.write(outputStream)
                return ExportResultModel(
                    inputStream = ByteArrayInputStream(outputStream.toByteArray()),
                    size = outputStream.size().toLong()
                )
            }
        } finally {
            workbook.dispose()
        }
    }

    private fun getTicketBasketItemsExportHeader(
        username: String,
        basketPaidAtDateFrom: LocalDate?,
        basketPaidAtDateTo: LocalDate?,
        branchName: String,
    ): Map<ExportLanguage, ExportMainHeaderModel> = mapOf(
        ExportLanguage.CZECH to ExportMainHeaderModel(
            branch = branchName,
            branchLabel = "Kino",
            dateLabel = "Datum",
            userLabel = "Uživatel",
            username = username,
            exportTitle = "Přehled prodejů vstupenek",
            exportSubtitleFrom = "Prodej od",
            exportDateFrom = basketPaidAtDateFrom,
            exportSubtitleTo = "Prodej do",
            exportDateTo = basketPaidAtDateTo,
            timeLabel = "Čas",
            dataColumnsCount = 38
        ),
        ExportLanguage.SLOVAK to ExportMainHeaderModel(
            branch = branchName,
            branchLabel = "Kino",
            dateLabel = "Dátum",
            userLabel = "Používateľ",
            username = username,
            exportTitle = "Prehľad predajov vstupeniek",
            exportSubtitleFrom = "Predaj od",
            exportDateFrom = basketPaidAtDateFrom,
            exportSubtitleTo = "do",
            exportDateTo = basketPaidAtDateTo,
            timeLabel = "Čas",
            dataColumnsCount = 38
        )
    )

    private fun getTicketBasketItemsSections(
        language: ExportLanguage,
        data: TicketBasketItemsSummaryExportRecordModel,
    ): List<Section> {
        val translations = translationsMap[language] ?: error("Translations not found for language: $language")

        val salesSummarySection = Section(
            tableRows = listOf(
                titleTableRow(SUMMARY_TITLE),
                valueTableRow(SCREENINGS_COUNT, data.sales.screeningsCount.toString()),
                valueTableRow(TICKETS_SOLD_COUNT, data.sales.ticketsCount.toString()),
                valueTableRow(USED_TICKETS_COUNT, data.sales.ticketsUsedCount.toString()),
                valueTableRow(CASH_SUM, data.sales.salesCash.withCurrency()),
                valueTableRow(CASHLESS_SUM, data.sales.salesCashless.withCurrency()),
                valueTableRow(GROSS_SALES, data.sales.grossSales.withCurrency()),
                valueTableRow(NET_SALES, data.sales.netSales.withCurrency()),
                valueTableRow(DISTRIBUTOR_DEDUCTION_VAR, data.sales.distributorDeduction.withCurrency()),
                valueTableRow(PRO_DEDUCTION_VAR, data.sales.proDeduction.withCurrency()),
                valueTableRow(FILM_FUND_DEDUCTION_VAR, data.sales.filmFondDeduction.withCurrency()),
                valueTableRow(VAT, data.sales.taxAmount.withCurrency()),
                valueTableRow(CANCELLED_COUNT, data.sales.cancelledTicketsCount.toString()),
                valueTableRow(CANCELLED_SUM, data.sales.cancelledTicketsExpense.withCurrency())
            ),
            border = true
        ).translate(translations)

        val ticketTypesSection = Section(
            tableRows = listOf(
                titleTableRow(TICKET_TYPES_TITLE),
                valueTableRow(GENERAL_SERVICES, data.serviceFees.general.withCurrency()),
                valueTableRow(VIP_SERVICES, data.serviceFees.vip.withCurrency()),
                valueTableRow(PREMIUM_SERVICES, data.serviceFees.premiumPlus.withCurrency()),
                valueTableRow(IMAX_SERVICES, data.serviceFees.imax.withCurrency()),
                valueTableRow(ULTRA_X_SERVICES, data.serviceFees.dolbyAtmos.withCurrency()),
                valueTableRow(VIP_SURCHARGES, data.surcharges.vip.withCurrency()),
                valueTableRow(DBOX_SURCHARGES, data.surcharges.dBox.withCurrency()),
                valueTableRow(PREMIUM_SURCHARGES, data.surcharges.premiumPlus.withCurrency()),
                valueTableRow(IMAX_SURCHARGES, data.surcharges.imax.withCurrency()),
                valueTableRow(ULTRA_X_SURCHARGES, data.surcharges.dolbyAtmos.withCurrency())
            ),
            border = true
        ).translate(translations)

        val technologiesCountSection = Section(
            tableRows = listOf(
                titleTableRow(TECHNOLOGIES_COUNT_TITLE),
                valueTableRow(GENERAL_TOTAL, data.technologyCount.general.toString()),
                valueTableRow(VIP_TOTAL, data.technologyCount.vip.toString()),
                valueTableRow(PREMIUM_TOTAL, data.technologyCount.premiumPlus.toString()),
                valueTableRow(IMAX_TOTAL, data.technologyCount.imax.toString()),
                valueTableRow(ULTRA_X_TOTAL, data.technologyCount.dolbyAtmos.toString()),
                valueTableRow(DBOX_TOTAL, data.technologyCount.dBox.toString())
            ),
            border = true
        ).translate(translations)

        val discountSection = Section(
            tableRows = listOf(
                titleTableRow(DISCOUNTS_TITLE),
                *data.discounts.map {
                    valueTableRow(it.title, it.count.toString())
                }.toTypedArray()
            ),
            border = true
        ).translate(translations)

        return listOf(salesSummarySection, ticketTypesSection, technologiesCountSection, discountSection)
    }

    private fun getTicketBasketItemsExportColumnNames(): Map<ExportLanguage, List<String>> = mapOf(
        ExportLanguage.CZECH to listOf(
            "Datum prodeje",
            "Čas prodeje",
            "Číslo dokladu",
            "Pokladna",
            "Prodal",
            "Sál",
            "Kino",
            "Distributor",
            "Datum promít.",
            "Čas promít.",
            "Film",
            "Použito",
            "Platba",
            "Řada",
            "Sedadlo",
            "Zákl. cena",
            "Přípl. VIP",
            "Přípl. PP",
            "Přípl. IMAX",
            "Přípl. UltraX",
            "Přípl. DBOX",
            "Služ. VIP",
            "Služ. PP",
            "Služ. IMAX",
            "Služ. UltraX",
            "Služ. obec.",
            "Storno",
            "Sleva základ",
            "Sleva stand.",
            "Sleva nestand.",
            "Karta/voucher",
            "Použitá karta",
            "Číslo karty",
            "OSA",
            "Fond kin.",
            "Distributor",
            "DPH",
            "3D brýle"
        ),
        ExportLanguage.SLOVAK to listOf(
            "Dátum predaja",
            "Čas predaja",
            "Číslo dokladu",
            "Pokladňa",
            "Predal",
            "Sála",
            "Kino",
            "Distribútor",
            "Dátum premiet.",
            "Čas premiet.",
            "Film",
            "Použité",
            "Platba",
            "Rad",
            "Sedadlo",
            "Zákl. cena",
            "Prípl. VIP",
            "Prípl. PP",
            "Prípl. IMAX",
            "Prípl. UltraX",
            "Prípl. DBOX",
            "Služ. VIP",
            "Služ. PP",
            "Služ. IMAX",
            "Služ. UltraX",
            "Služ. obec.",
            "Storno",
            "Zľava základ",
            "Zľava štand.",
            "Zľava neštand.",
            "Karta/voucher",
            "Použitá karta",
            "Číslo karty",
            "SOZA",
            "Fond kin.",
            "Distribútor",
            "DPH",
            "3D okuliare"
        )
    )
}

private const val SUMMARY_TITLE = "#summaryTitle#"
private const val TICKET_TYPES_TITLE = "#ticketTypesTitle#"
private const val TECHNOLOGIES_COUNT_TITLE = "#technologiesCountTitle#"
private const val DISCOUNTS_TITLE = "#discountsTitle#"
private const val SUMMARY_SHEET_TITLE = "#summarySheetTitle#"

private const val SCREENINGS_COUNT = "#screeningsCount#"
private const val TICKETS_SOLD_COUNT = "#ticketsSoldCount#"
private const val USED_TICKETS_COUNT = "#usedTicketsCount#"
private const val CASH_SUM = "#cashSum#"
private const val CASHLESS_SUM = "#cashlessSum#"
private const val GROSS_SALES = "#grossSales#"
private const val NET_SALES = "#netSales#"
private const val DISTRIBUTOR_DEDUCTION_VAR = "#distributorDeduction#"
private const val PRO_DEDUCTION_VAR = "#proDeduction#"
private const val FILM_FUND_DEDUCTION_VAR = "#filmFundDeduction#"
private const val VAT = "#vat#"
private const val CANCELLED_COUNT = "#cancelledCount#"
private const val CANCELLED_SUM = "#cancelledSum#"
private const val GENERAL_SERVICES = "#generalServices#"
private const val VIP_SERVICES = "#vipServices#"
private const val PREMIUM_SERVICES = "#premiumServices#"
private const val IMAX_SERVICES = "#imaxServices#"
private const val ULTRA_X_SERVICES = "#ultraXServices#"
private const val VIP_SURCHARGES = "#vipSurcharges#"
private const val DBOX_SURCHARGES = "#dboxSurcharges#"
private const val PREMIUM_SURCHARGES = "#premiumSurcharges#"
private const val IMAX_SURCHARGES = "#imaxSurcharges#"
private const val ULTRA_X_SURCHARGES = "#ultraXSurcharges#"
private const val NO_DISCOUNT = "#noDiscount#"
private const val GENERAL_TOTAL = "#generalTotal#"
private const val VIP_TOTAL = "#vipTotal#"
private const val PREMIUM_TOTAL = "#premiumTotal#"
private const val IMAX_TOTAL = "#imaxTotal#"
private const val ULTRA_X_TOTAL = "#ultraXTotal#"
private const val DBOX_TOTAL = "#dboxTotal#"

private val translationsMap = mapOf(
    ExportLanguage.CZECH to mapOf(
        CURRENCY to ExportCurrency.CZK.symbol,
        SCREENINGS_COUNT to "Počet promítání",
        TICKETS_SOLD_COUNT to "Počet prodaných vstupenek",
        USED_TICKETS_COUNT to "Počet použitých vstupenek",
        CASH_SUM to "Suma hotovost",
        CASHLESS_SUM to "Suma bezhotovost",
        GROSS_SALES to "Tržba hrubá",
        NET_SALES to "Tržba čistá",
        DISTRIBUTOR_DEDUCTION_VAR to "Odvod distributorovi",
        PRO_DEDUCTION_VAR to "Odvod OSA",
        FILM_FUND_DEDUCTION_VAR to "Odvod fondu kinematografie",
        VAT to "DPH",
        CANCELLED_COUNT to "Počet storno",
        CANCELLED_SUM to "Suma storno",
        GENERAL_SERVICES to "Služby obecné",
        VIP_SERVICES to "Služby VIP",
        PREMIUM_SERVICES to "Služby Premium",
        IMAX_SERVICES to "Služby IMAX",
        ULTRA_X_SERVICES to "Služby Ultra X",
        VIP_SURCHARGES to "Příplatky VIP",
        DBOX_SURCHARGES to "Příplatky DBox",
        PREMIUM_SURCHARGES to "Příplatky Premium",
        IMAX_SURCHARGES to "Příplatky IMAX",
        ULTRA_X_SURCHARGES to "Příplatky Ultra X",
        NO_DISCOUNT to "Bez slevy",
        GENERAL_TOTAL to "Počet obecné",
        VIP_TOTAL to "Počet VIP",
        DBOX_TOTAL to "Počet DBox",
        PREMIUM_TOTAL to "Počet Premium",
        IMAX_TOTAL to "Počet IMAX",
        ULTRA_X_TOTAL to "Počet Ultra X",
        SUMMARY_TITLE to "Zhrnutí podle vybraných prodejů",
        TICKET_TYPES_TITLE to "Typy vstupenek",
        TECHNOLOGIES_COUNT_TITLE to "Počet podle technologií",
        DISCOUNTS_TITLE to "Zvýhodněné vstupné",
        SUMMARY_SHEET_TITLE to "Shrnutí"
    ),
    ExportLanguage.SLOVAK to mapOf(
        CURRENCY to ExportCurrency.EUR.symbol,
        SCREENINGS_COUNT to "Počet premietaní",
        TICKETS_SOLD_COUNT to "Počet predaných vstupeniek",
        USED_TICKETS_COUNT to "Počet použitých vstupeniek",
        CASH_SUM to "Suma hotovosť",
        CASHLESS_SUM to "Suma bezhotovosť",
        GROSS_SALES to "Tržba hrubá",
        NET_SALES to "Tržba čistá",
        DISTRIBUTOR_DEDUCTION_VAR to "Odvod distribútorovi",
        PRO_DEDUCTION_VAR to "Odvod SOZA",
        FILM_FUND_DEDUCTION_VAR to "Odvod fondu kinematografie",
        VAT to "DPH",
        CANCELLED_COUNT to "Počet storno",
        CANCELLED_SUM to "Suma storno",
        GENERAL_SERVICES to "Služby obecné",
        VIP_SERVICES to "Služby VIP",
        PREMIUM_SERVICES to "Služby Premium",
        IMAX_SERVICES to "Služby IMAX",
        ULTRA_X_SERVICES to "Služby Ultra X",
        VIP_SURCHARGES to "Príplatky VIP",
        DBOX_SURCHARGES to "Príplatky DBox",
        PREMIUM_SURCHARGES to "Príplatky Premium",
        IMAX_SURCHARGES to "Príplatky IMAX",
        ULTRA_X_SURCHARGES to "Príplatky Ultra X",
        NO_DISCOUNT to "Bez slevy",
        GENERAL_TOTAL to "Počet obecné",
        VIP_TOTAL to "Počet VIP",
        DBOX_TOTAL to "Počet DBox",
        PREMIUM_TOTAL to "Počet Premium",
        IMAX_TOTAL to "Počet IMAX",
        ULTRA_X_TOTAL to "Počet Ultra X",
        SUMMARY_TITLE to "Zhrnutie podľa vybraných predajov",
        TICKET_TYPES_TITLE to "Typy vstupeniek",
        TECHNOLOGIES_COUNT_TITLE to "Počet podľa technológií",
        DISCOUNTS_TITLE to "Zvýhodnené vstupné",
        SUMMARY_SHEET_TITLE to "Zhrnutie"
    )
)
