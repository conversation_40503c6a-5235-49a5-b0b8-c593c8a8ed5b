package com.cleevio.cinemax.api.module.reservation.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.RESERVATION
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationJpaFinderService
import com.cleevio.cinemax.api.module.reservation.constant.RESERVED_STATES
import com.cleevio.cinemax.api.module.reservation.constant.ReservationLockValues.CREATE_OR_UPDATE_RESERVATION
import com.cleevio.cinemax.api.module.reservation.constant.ReservationLockValues.CREATE_RESERVATION
import com.cleevio.cinemax.api.module.reservation.constant.ReservationLockValues.UPDATE_OR_DELETE_RESERVATION
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.constant.UNAVAILABLE_MSSQL_STATE
import com.cleevio.cinemax.api.module.reservation.entity.Reservation
import com.cleevio.cinemax.api.module.reservation.entity.toOnlineReservationModel
import com.cleevio.cinemax.api.module.reservation.event.ReservationAlreadyExistsInMssqlEvent
import com.cleevio.cinemax.api.module.reservation.event.ReservationConfirmedEvent
import com.cleevio.cinemax.api.module.reservation.event.ReservationCreatedEvent
import com.cleevio.cinemax.api.module.reservation.event.ReservationDeletedEvent
import com.cleevio.cinemax.api.module.reservation.event.ReservationStateChangedEvent
import com.cleevio.cinemax.api.module.reservation.exception.GroupReservationForReservationNotFoundException
import com.cleevio.cinemax.api.module.reservation.exception.ReservationAlreadyExistsException
import com.cleevio.cinemax.api.module.reservation.exception.ReservationAlreadyExistsInMssqlException
import com.cleevio.cinemax.api.module.reservation.exception.ReservationNotFoundException
import com.cleevio.cinemax.api.module.reservation.exception.ScreeningForReservationNotFoundException
import com.cleevio.cinemax.api.module.reservation.exception.SeatForReservationNotFoundException
import com.cleevio.cinemax.api.module.reservation.service.command.CreateOnlineMultiSeatReservationCommand
import com.cleevio.cinemax.api.module.reservation.service.command.CreateOrUpdateReservationCommand
import com.cleevio.cinemax.api.module.reservation.service.command.CreateReservationCommand
import com.cleevio.cinemax.api.module.reservation.service.command.CreateReservationsForSeatsCommand
import com.cleevio.cinemax.api.module.reservation.service.command.CreateSimpleReservationCommand
import com.cleevio.cinemax.api.module.reservation.service.command.DeleteReservationCommand
import com.cleevio.cinemax.api.module.reservation.service.command.DeleteReservationsCommand
import com.cleevio.cinemax.api.module.reservation.service.command.MoveReservationCommand
import com.cleevio.cinemax.api.module.reservation.service.command.UpdateReservationOriginalIdCommand
import com.cleevio.cinemax.api.module.reservation.service.command.UpdateReservationStateCommand
import com.cleevio.cinemax.api.module.reservation.service.model.OnlineReservationModel
import com.cleevio.cinemax.api.module.screening.service.ScreeningJpaFinderService
import com.cleevio.cinemax.api.module.seat.service.SeatJpaFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import com.cleevio.library.lockinghandler.service.LockFieldParameters
import com.cleevio.library.lockinghandler.service.LockService
import jakarta.validation.Valid
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class ReservationService(
    private val reservationFinderRepository: ReservationFinderRepository,
    private val reservationJooqFinderService: ReservationJooqFinderService,
    private val reservationRepository: ReservationRepository,
    private val reservationMssqlFinderService: ReservationMssqlFinderService,
    private val seatJpaFinderService: SeatJpaFinderService,
    private val groupReservationJpaFinderService: GroupReservationJpaFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val screeningJpaFinderService: ScreeningJpaFinderService,

    @Suppress("SpringJavaInjectionPointsAutowiringInspection")
    private val lockService: LockService,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
) {
    private val log = logger()

    @Transactional
    @Lock(RESERVATION, CREATE_RESERVATION)
    fun createReservation(
        @Valid
        @LockFieldParameters([LockFieldParameter("screeningId"), LockFieldParameter("seatId")])
        command: CreateReservationCommand,
    ): Reservation {
        log.debug("Attempting to create seat reservation, provided command is: $command.")
        val originalIdsPair = validateReferencesAndGetOriginalIds(command.screeningId, command.seatId)

        reservationFinderRepository.findLatestByScreeningAndSeatId(command.screeningId, command.seatId)?.let {
            if (it.state in RESERVED_STATES) throw ReservationAlreadyExistsException()
        }

        originalIdsPair.second?.let {
            reservationMssqlFinderService.findByOriginalScreeningIdAndOriginalSeatId(
                originalScreeningId = originalIdsPair.first,
                originalSeatId = it
            )?.let { mssqlSeat ->
                if (mssqlSeat.delegace == UNAVAILABLE_MSSQL_STATE) {
                    log.error("Reservation for originalIdsPair=$originalIdsPair, command=$command already exists in MSSQL.")
                    applicationEventPublisher.publishEvent(ReservationAlreadyExistsInMssqlEvent(originalIdsPair.first))
                    throw ReservationAlreadyExistsInMssqlException()
                }
            }
        }

        val created = reservationRepository.save(mapToNewReservation(command))

        log.info("Created reservation ${created.id} (screening ${created.screeningId}, seat ${created.seatId}).")

        if (cinemaxConfigProperties.isBranchDeployment()) {
            applicationEventPublisher.publishEvent(ReservationCreatedEvent(created.id))
            applicationEventPublisher.publishEvent(
                mapToReservationStateChangedEvent(
                    reservationId = created.id,
                    state = command.state,
                    originalIdsPair = originalIdsPair,
                    updatedBy = created.updatedBy
                )
            )
        }

        return created
    }

    fun createSimpleReservation(command: CreateSimpleReservationCommand): Reservation = reservationRepository.save(
        Reservation(
            id = UUID.randomUUID(),
            screeningId = command.screeningId,
            seatId = command.seatId,
            state = ReservationState.UNAVAILABLE
        )
    )

    // no lock needed, all seats reservations are locked in parent when transactions starts
    fun createMultiSeatReservation(
        command: CreateOnlineMultiSeatReservationCommand,
    ): List<OnlineReservationModel> {
        return command.seatIds.map {
            createOnlineReservation(
                screeningId = command.screeningId,
                seatId = it,
                groupReservationId = command.groupReservationId
            )
        }
    }

    private fun createOnlineReservation(
        screeningId: UUID,
        seatId: UUID,
        groupReservationId: UUID,
    ): OnlineReservationModel {
        val originalIdsPair = validateReferencesAndGetOriginalIds(screeningId, seatId)

        reservationFinderRepository.findLatestByScreeningAndSeatId(screeningId, seatId)?.let {
            if (it.state in RESERVED_STATES) throw ReservationAlreadyExistsException()
        }

        val created = Reservation(
            id = UUID.randomUUID(),
            screeningId = screeningId,
            seatId = seatId,
            state = ReservationState.ONLINE_RESERVED,
            groupReservationId = groupReservationId
        ).also {
            reservationRepository.save(it)
        }

        log.info("Created online reservation ${created.id} (screening ${created.screeningId}, seat ${created.seatId}).")

        applicationEventPublisher.publishEvent(ReservationCreatedEvent(created.id))
        applicationEventPublisher.publishEvent(
            mapToReservationStateChangedEvent(
                reservationId = created.id,
                state = ReservationState.ONLINE_RESERVED,
                originalIdsPair = originalIdsPair,
                updatedBy = created.updatedBy
            )
        )

        return created.toOnlineReservationModel()
    }

    @Transactional
    @Lock(RESERVATION, UPDATE_OR_DELETE_RESERVATION)
    fun updateReservationState(
        @Valid
        @LockFieldParameter("reservationId")
        command: UpdateReservationStateCommand,
    ) {
        log.debug("Attempting to update seat reservation state, provided command is: $command.")
        val reservation = reservationJooqFinderService.getNonDeletedById(command.reservationId)
        val originalIdsPair = validateReferencesAndGetOriginalIds(reservation.screeningId, reservation.seatId)

        val updated = reservationRepository.save(
            mapToExistingReservation(
                reservation = reservation,
                state = command.state,
                groupReservationId = reservation.groupReservationId
            )
        )

        log.info("Updated reservation ${reservation.id} state to ${command.state}.")
        applicationEventPublisher.publishEvent(ReservationConfirmedEvent(reservation.id))

        applicationEventPublisher.publishEvent(
            mapToReservationStateChangedEvent(
                reservationId = reservation.id,
                state = command.state,
                originalIdsPair = originalIdsPair,
                updatedBy = updated.updatedBy
            )
        )
    }

    @Transactional
    @Lock(RESERVATION, UPDATE_OR_DELETE_RESERVATION)
    fun updateReservationOriginalId(
        @Valid
        @LockFieldParameter("reservationId")
        command: UpdateReservationOriginalIdCommand,
    ) {
        reservationJooqFinderService.getById(command.reservationId).also {
            reservationRepository.save(
                it.apply {
                    originalId = command.originalId
                }
            )
        }
    }

    @Transactional
    @Lock(RESERVATION, UPDATE_OR_DELETE_RESERVATION)
    fun deleteReservation(
        @Valid
        @LockFieldParameter("reservationId")
        command: DeleteReservationCommand,
    ) {
        log.debug("Attempting to delete seat reservation ${command.reservationId}.")

        val reservation = reservationFinderRepository.findNonDeletedById(command.reservationId)
        reservation?.let {
            if (command.nonGroupReservationOnly && reservation.groupReservationId != null) return

            val originalIdsPair = validateReferencesAndGetOriginalIds(it.screeningId, it.seatId)

            reservationRepository.save(
                it.apply {
                    state = ReservationState.DELETED
                    markDeleted()
                }
            ).also { deleted ->
                log.info("Deleted reservation $reservation -> $deleted.")
            }

            if (command.syncDeletionToMssql) {
                applicationEventPublisher.publishEvent(
                    mapToReservationStateChangedEvent(
                        reservationId = it.id,
                        state = ReservationState.DELETED,
                        originalIdsPair = originalIdsPair,
                        updatedBy = it.updatedBy
                    )
                )
            }
            applicationEventPublisher.publishEvent(ReservationDeletedEvent(it.id))
        } ?: throw ReservationNotFoundException()
    }

    @Transactional
    fun deleteReservations(@Valid command: DeleteReservationsCommand) {
        val reservations = reservationFinderRepository.findAllNonDeletedByIdIn(command.reservationIds)

        reservations.filter { !command.nonGroupReservationsOnly || it.groupReservationId == null }
            .forEach {
                lockService.obtainBlockingLock(
                    module = RESERVATION,
                    lockName = UPDATE_OR_DELETE_RESERVATION
                ).use { _ ->
                    val originalIdsPair = validateReferencesAndGetOriginalIds(it.screeningId, it.seatId)

                    applicationEventPublisher.publishEvent(
                        mapToReservationStateChangedEvent(
                            reservationId = it.id,
                            state = ReservationState.DELETED,
                            originalIdsPair = originalIdsPair,
                            updatedBy = it.updatedBy
                        )
                    )

                    reservationRepository.save(
                        it.apply {
                            state = ReservationState.DELETED
                            markDeleted()
                        }
                    )

                    applicationEventPublisher.publishEvent(ReservationDeletedEvent(it.id))
                }
            }
    }

    @Transactional
    @Lock(RESERVATION, CREATE_OR_UPDATE_RESERVATION)
    fun createOrUpdateReservation(
        @Valid
        @LockFieldParameter("originalId")
        command: CreateOrUpdateReservationCommand,
    ) {
        log.info("Attempting to create or update reservation, command: $command.")

        validateReferencesAndGetOriginalIds(
            screeningId = command.screeningId,
            seatId = command.seatId,
            groupReservationId = command.groupReservationId
        )
        log.info("Validated references and got originalIds.")

        val reservation = reservationFinderRepository.findLatestByOriginalId(command.originalId).also {
            it?.let {
                // existing reservation with state == 'FREE' and new reservation state == 'FREE' -> don't persist
                if (it.state == ReservationState.FREE && command.state == ReservationState.FREE) {
                    log.info(
                        "Reservation command=$command would update FREE reservation, while there was another " +
                            "existing FREE reservation for given Seat/Screening, skipping."
                    )
                    return
                }
            } ?: run {
                // no existing reservation and new reservation state = 'FREE' -> don't persist
                if (command.state == ReservationState.FREE) {
                    log.info(
                        "Reservation command=$command would create FREE reservation, while there were no prior " +
                            "existing reservations for given Seat/Screening, skipping."
                    )
                    return
                }
            }
        }

        reservation?.let {
            log.info("Updating reservation for ${command.originalId}.")
            reservationRepository.save(
                mapToExistingReservation(
                    reservation = it,
                    state = command.state,
                    originalId = command.originalId,
                    groupReservationId = command.groupReservationId
                )
            ).also { updated ->
                applicationEventPublisher.publishEvent(ReservationConfirmedEvent(updated.id))
                log.info("Updated reservation ${updated.id} (${updated.originalId}).")
            }
        } ?: run {
            log.info("Creating reservation for ${command.originalId}.")
            reservationRepository.save(
                mapToNewReservation(
                    id = command.id,
                    command = mapToCreateReservationCommand(command)
                ).apply {
                    originalId = command.originalId
                }
            ).also { created ->
                applicationEventPublisher.publishEvent(ReservationCreatedEvent(created.id))
                log.info("Created reservation ${created.id} (${created.originalId}).")
            }
        }
    }

    @Transactional
    fun createReservationsForSeats(@Valid command: CreateReservationsForSeatsCommand) {
        command.seatIdToReservationStateMap.forEach { (seatId, reservationState) ->
            lockService.obtainBlockingLock(
                module = RESERVATION,
                lockName = CREATE_OR_UPDATE_RESERVATION,
                command.screeningId.toString(),
                seatId.toString()
            ).use { _ ->
                mapToNewReservation(
                    command = CreateReservationCommand(
                        screeningId = command.screeningId,
                        seatId = seatId,
                        state = reservationState,
                        groupReservationId = command.groupReservationId
                    )
                ).let {
                    reservationRepository.save(it)

                    applicationEventPublisher.publishEvent(
                        mapToReservationStateChangedEvent(
                            reservationId = it.id,
                            state = reservationState,
                            originalIdsPair = processValidationWithWait {
                                validateReferencesAndGetOriginalIds(
                                    screeningId = it.screeningId,
                                    seatId = it.seatId
                                )
                            },
                            groupReservationId = command.groupReservationId,
                            updatedBy = it.updatedBy
                        )
                    )
                }
            }
        }
    }

    @Transactional
    @Lock(RESERVATION, CREATE_OR_UPDATE_RESERVATION)
    fun moveReservation(
        @Valid
        @LockFieldParameter("reservationId")
        command: MoveReservationCommand,
    ): Reservation {
        val pastReservation = reservationRepository.findByIdAndDeletedAtIsNull(command.reservationId)
            ?: throw ReservationNotFoundException()
        val pastReservationState = pastReservation.state

        deleteReservation(DeleteReservationCommand(pastReservation.id))

        return createReservation(
            CreateReservationCommand(
                screeningId = command.screeningId,
                seatId = command.seatId,
                state = pastReservationState
            )
        )
    }

    private fun mapToExistingReservation(
        reservation: Reservation,
        state: ReservationState,
        originalId: Int? = null,
        groupReservationId: UUID?,
    ): Reservation {
        return reservation.apply {
            this.state = state
            originalId?.let { this.originalId = originalId }
            this.groupReservationId = groupReservationId
        }
    }

    private fun mapToNewReservation(
        command: CreateReservationCommand,
        id: UUID? = null,
    ) = Reservation(
        id = id ?: UUID.randomUUID(),
        screeningId = command.screeningId,
        seatId = command.seatId,
        state = command.state,
        groupReservationId = command.groupReservationId
    )

    private fun processValidationWithWait(validationFunction: () -> Pair<Int, Int?>): Pair<Int, Int?> {
        var tries = 0

        while (tries < VALIDATION_WAIT_RETRY_LIMIT) {
            val result = runCatching { validationFunction() }
            if (result.isSuccess) return result.getOrThrow()

            tries++
            Thread.sleep(VALIDATION_WAIT_INTERVAL)
        }

        return validationFunction()
    }

    private fun validateReferencesAndGetOriginalIds(
        screeningId: UUID,
        seatId: UUID,
        groupReservationId: UUID? = null,
    ): Pair<Int, Int?> {
        groupReservationId?.let {
            groupReservationJpaFinderService.findNonDeletedById(groupReservationId)
                ?: throw GroupReservationForReservationNotFoundException()
        }

        // Need to fetch screening only with not null originalId otherwise JPA cache keeps first try result
        // for all retry iterations and do not fetch updated state from db (JPA optimization)
        val screening = screeningJpaFinderService.findNonDeletedAndOriginalIdIsNotNull(screeningId)
            ?: throw ScreeningForReservationNotFoundException()

        if (screening.originalId != null) {
            val seat = seatJpaFinderService.findById(seatId) ?: throw SeatForReservationNotFoundException()
            return Pair(
                requireNotNull(screening.originalId),
                seat.originalId
            )
        }

        error("OriginalId for screening id:$screeningId is not available.")
    }

    private fun mapToReservationStateChangedEvent(
        reservationId: UUID,
        state: ReservationState,
        originalIdsPair: Pair<Int, Int?>,
        groupReservationId: UUID? = null,
        updatedBy: String,
    ) = ReservationStateChangedEvent(
        reservationId = reservationId,
        newState = state,
        originalScreeningId = originalIdsPair.first,
        originalSeatId = originalIdsPair.second,
        groupReservationId = groupReservationId,
        updatedBy = updatedBy
    )

    private fun mapToCreateReservationCommand(
        createOrUpdatedCommand: CreateOrUpdateReservationCommand,
    ) = CreateReservationCommand(
        screeningId = createOrUpdatedCommand.screeningId,
        seatId = createOrUpdatedCommand.seatId,
        state = createOrUpdatedCommand.state,
        groupReservationId = createOrUpdatedCommand.groupReservationId
    )
}

private const val VALIDATION_WAIT_INTERVAL = 1000L
private const val VALIDATION_WAIT_RETRY_LIMIT = 5
