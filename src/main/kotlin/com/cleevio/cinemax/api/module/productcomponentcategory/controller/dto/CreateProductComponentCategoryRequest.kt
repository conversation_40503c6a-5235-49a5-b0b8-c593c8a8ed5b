package com.cleevio.cinemax.api.module.productcomponentcategory.controller.dto

import com.cleevio.cinemax.api.module.productcomponentcategory.service.command.CreateOrUpdateProductComponentCategoryCommand

data class CreateProductComponentCategoryRequest(
    val title: String,
    val taxRate: Int,
) {

    fun toCommand() = CreateOrUpdateProductComponentCategoryCommand(
        title = title,
        taxRate = taxRate
    )
}
