package com.cleevio.cinemax.api.module.ticket.event

import com.cleevio.cinemax.api.common.integration.pubsub.constant.MessageType
import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.util.toJsonString
import com.cleevio.cinemax.api.module.ticket.entity.Ticket
import java.util.UUID

data class AdminTicketUsedUpdatedEvent(
    val ticketId: UUID,
    val isUsed: Boolean,
) {
    fun toMessagePayload() = MessagePayload(
        id = UUID.randomUUID(),
        type = MessageType.TICKET_USED,
        data = this.toJsonString()
    )
}

fun Ticket.toMessagingEvent() = AdminTicketUsedUpdatedEvent(
    ticketId = id,
    isUsed = isUsed
)
