package com.cleevio.cinemax.api.module.basket.service.command

import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import jakarta.validation.constraints.Positive
import java.util.UUID

data class InitBasketForTableCommand(
    val tableId: UUID,
    val items: List<CreateBasketItemInput> = listOf(),
    val preferredPaymentType: PaymentType? = null,
    val initiatedInNewPos: Boolean,
)

data class CreateBasketItemInput(
    val type: BasketItemType,

    @field:Positive
    val quantity: Int,
    val product: CreateProductInput? = null,
    val ticket: CreateTicketInput? = null,
)

data class CreateProductInput(
    val productId: UUID,
    val productIsolatedWithId: UUID? = null,
    val discountCardId: UUID? = null,
)

data class CreateTicketInput(
    val screeningId: UUID,
    val ticketPrice: CreateTicketPriceInput,
    val reservation: CreateReservationInput,
    val primaryDiscount: CreateTicketDiscountInput? = null,
    val secondaryDiscount: CreateTicketDiscountInput? = null,
    val isGroupTicket: Boolean = false,
)
