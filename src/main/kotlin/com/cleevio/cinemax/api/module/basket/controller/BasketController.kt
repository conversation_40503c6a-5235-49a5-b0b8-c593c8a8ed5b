package com.cleevio.cinemax.api.module.basket.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.module.basket.controller.dto.BasketResponse
import com.cleevio.cinemax.api.module.basket.controller.dto.InitBasketRequest
import com.cleevio.cinemax.api.module.basket.controller.mapper.BasketResponseMapper
import com.cleevio.cinemax.api.module.basket.service.BasketFinderService
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basket.service.command.CreateBasketItemInput
import com.cleevio.cinemax.api.module.basket.service.command.DeleteBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitBasketForTableCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitiateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.UpdateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateBasketItemRequest
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "POS Baskets")
@RestController
@RequestMapping("/pos-app/baskets")
class BasketController(
    private val basketService: BasketService,
    private val basketFinderService: BasketFinderService,
    private val basketResponseMapper: BasketResponseMapper,
) {

    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @PostMapping("/init", produces = [ApiVersion.VERSION_1_JSON])
    fun initBasket(
        @AuthenticationPrincipal username: String,
        @RequestBody request: InitBasketRequest,
    ): BasketResponse {
        basketService.initBasket(
            InitBasketCommand(
                itemRequests = request.items
            )
        ).also {
            return basketResponseMapper.mapSingle(it)
        }
    }

    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @PostMapping("/init-for-table/{tableId}", produces = [ApiVersion.VERSION_1_JSON])
    fun initBasketForTable(
        @AuthenticationPrincipal username: String,
        @RequestBody request: InitBasketRequest,
        @PathVariable tableId: UUID,
    ): BasketResponse {
        basketService.initBasketForTable(
            InitBasketForTableCommand(
                tableId = tableId,
                itemRequests = request.items,
                initiatedInNewPos = true
            )
        ).also {
            return basketResponseMapper.mapSingle(it)
        }
    }

    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @DeleteMapping("/{basketId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun deleteBasket(
        @AuthenticationPrincipal username: String,
        @PathVariable basketId: UUID,
    ) {
        basketService.deleteBasket(
            DeleteBasketCommand(
                basketId = basketId
            )
        )
    }

    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @PostMapping("/{basketId}/payment/{paymentType}", produces = [ApiVersion.VERSION_1_JSON])
    fun initiateBasketPayment(
        @AuthenticationPrincipal username: String,
        @PathVariable basketId: UUID,
        @PathVariable paymentType: PaymentType,
        @RequestParam posConfigurationId: UUID,
    ): BasketResponse {
        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = basketId,
                posConfigurationId = posConfigurationId,
                paymentType = paymentType
            )
        ).also {
            return basketResponseMapper.mapSingle(it)
        }
    }

    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @PutMapping("/{basketId}/payment/{paymentType}", produces = [ApiVersion.VERSION_1_JSON])
    fun updateBasketPayment(
        @AuthenticationPrincipal username: String,
        @PathVariable basketId: UUID,
        @PathVariable paymentType: PaymentType,
    ): BasketResponse {
        basketService.updateBasketPayment(
            UpdateBasketPaymentCommand(
                basketId = basketId,
                paymentType = paymentType
            )
        ).also {
            return basketResponseMapper.mapSingle(it)
        }
    }

    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @GetMapping("/{basketId}", produces = [ApiVersion.VERSION_1_JSON])
    fun getBasket(@PathVariable basketId: UUID): BasketResponse {
        basketFinderService.getNonDeletedById(basketId).also {
            return basketResponseMapper.mapSingle(it)
        }
    }
}
