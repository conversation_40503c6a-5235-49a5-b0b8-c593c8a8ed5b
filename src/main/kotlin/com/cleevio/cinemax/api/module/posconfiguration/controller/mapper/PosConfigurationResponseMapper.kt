package com.cleevio.cinemax.api.module.posconfiguration.controller.mapper

import com.cleevio.cinemax.api.module.posconfiguration.controller.dto.PosConfigurationResponse
import com.cleevio.cinemax.api.module.posconfiguration.entity.PosConfiguration
import org.springframework.stereotype.Component

@Component
class PosConfigurationResponseMapper {

    fun mapSingle(posConfiguration: PosConfiguration): PosConfigurationResponse =
        PosConfigurationResponse(
            id = posConfiguration.id,
            macAddress = posConfiguration.macAddress,
            title = posConfiguration.title,
            receiptsDirectory = posConfiguration.receiptsDirectory,
            terminalDirectory = posConfiguration.terminalDirectory,
            terminalIpAddress = posConfiguration.terminalIpAddress,
            ticketSalesEnabled = posConfiguration.ticketSalesEnabled,
            productModes = posConfiguration.productModes,
            tablesType = posConfiguration.tablesType,
            seatsEnabled = posConfiguration.seatsEnabled
        )
}
