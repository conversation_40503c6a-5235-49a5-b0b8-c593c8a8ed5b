package com.cleevio.cinemax.api.module.basketitem.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
class ProductBasketItemWithInsufficientStockQuantityException : ApiException(
    Module.BASKET_ITEM,
    BasketItemErrorType.PRODUCT_BASKET_ITEM_WITH_INSUFFICIENT_STOCK_QUANTITY
)
