package com.cleevio.cinemax.api.module.discountcardusage.service

import com.cleevio.cinemax.api.common.service.JooqFinderRepository
import com.cleevio.cinemax.api.module.basket.util.MODIFIABLE_BASKET_STATES
import com.cleevio.cinemax.api.module.discountcardusage.entity.DiscountCardUsage
import com.cleevio.cinemax.psql.Tables.BASKET
import com.cleevio.cinemax.psql.Tables.DISCOUNT_CARD_USAGE
import org.jooq.DSLContext
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
class DiscountCardUsageFinderRepository(
    private val psqlDslContext: DSLContext,
) : JooqFinderRepository<DiscountCardUsage> {

    override fun findAll(): List<DiscountCardUsage> {
        return psqlDslContext
            .selectFrom(DISCOUNT_CARD_USAGE)
            .fetchInto(DiscountCardUsage::class.java)
    }

    override fun findById(id: UUID): DiscountCardUsage? {
        return psqlDslContext
            .selectFrom(DISCOUNT_CARD_USAGE)
            .where(DISCOUNT_CARD_USAGE.ID.eq(id))
            .fetchOneInto(DiscountCardUsage::class.java)
    }

    fun findNonDeletedById(id: UUID): DiscountCardUsage? {
        return psqlDslContext
            .selectFrom(DISCOUNT_CARD_USAGE)
            .where(DISCOUNT_CARD_USAGE.DELETED_AT.isNull)
            .and(DISCOUNT_CARD_USAGE.ID.eq(id))
            .fetchOneInto(DiscountCardUsage::class.java)
    }

    fun findAllNonDeletedByIdIn(ids: Set<UUID>): List<DiscountCardUsage> {
        return psqlDslContext
            .selectFrom(DISCOUNT_CARD_USAGE)
            .where(DISCOUNT_CARD_USAGE.DELETED_AT.isNull)
            .and(DISCOUNT_CARD_USAGE.ID.`in`(ids))
            .fetchInto(DiscountCardUsage::class.java)
    }

    fun findNonDeletedByDiscountCardIdAndBasketIsInModifiableState(discountCardId: UUID): DiscountCardUsage? {
        return psqlDslContext
            .select(DISCOUNT_CARD_USAGE)
            .from(DISCOUNT_CARD_USAGE)
            .innerJoin(BASKET)
            .on(DISCOUNT_CARD_USAGE.BASKET_ID.eq(BASKET.ID))
            .where(DISCOUNT_CARD_USAGE.DELETED_AT.isNull)
            .and(DISCOUNT_CARD_USAGE.DISCOUNT_CARD_ID.eq(discountCardId))
            .and(BASKET.STATE.`in`(MODIFIABLE_BASKET_STATES))
            .fetchOneInto(DiscountCardUsage::class.java)
    }

    fun findAllNonDeletedByDiscountCardIdInAndBasketInModifiableState(discountCardIds: Set<UUID>): List<DiscountCardUsage> {
        return psqlDslContext
            .select(DISCOUNT_CARD_USAGE)
            .from(DISCOUNT_CARD_USAGE)
            .innerJoin(BASKET)
            .on(DISCOUNT_CARD_USAGE.BASKET_ID.eq(BASKET.ID))
            .where(DISCOUNT_CARD_USAGE.DELETED_AT.isNull)
            .and(DISCOUNT_CARD_USAGE.DISCOUNT_CARD_ID.`in`(discountCardIds))
            .and(BASKET.STATE.`in`(MODIFIABLE_BASKET_STATES))
            .fetchInto(DiscountCardUsage::class.java)
    }

    fun findAllNonDeletedByBasketId(basketId: UUID): List<DiscountCardUsage> {
        return psqlDslContext
            .select(DISCOUNT_CARD_USAGE)
            .from(DISCOUNT_CARD_USAGE)
            .where(DISCOUNT_CARD_USAGE.BASKET_ID.eq(basketId))
            .and(DISCOUNT_CARD_USAGE.DELETED_AT.isNull)
            .fetchInto(DiscountCardUsage::class.java)
    }

    fun existsByDiscountCardIdAndScreeningId(discountCardId: UUID, screeningId: UUID): Boolean {
        return psqlDslContext.fetchExists(
            psqlDslContext
                .selectOne()
                .from(DISCOUNT_CARD_USAGE)
                .where(DISCOUNT_CARD_USAGE.DISCOUNT_CARD_ID.eq(discountCardId))
                .and(DISCOUNT_CARD_USAGE.SCREENING_ID.eq(screeningId))
                .and(DISCOUNT_CARD_USAGE.DELETED_AT.isNull)
        )
    }
}
