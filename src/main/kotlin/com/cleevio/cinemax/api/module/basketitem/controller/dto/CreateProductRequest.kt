package com.cleevio.cinemax.api.module.basketitem.controller.dto

import com.cleevio.cinemax.api.module.basket.service.command.CreateProductInput
import java.util.UUID

data class CreateProductRequest(
    val productId: UUID,
    val productIsolatedWithId: UUID? = null,
    val discountCardId: UUID? = null,
) {

    fun toInput() = CreateProductInput(
        productId = productId,
        productIsolatedWithId = productIsolatedWithId,
        discountCardId = discountCardId
    )
}
