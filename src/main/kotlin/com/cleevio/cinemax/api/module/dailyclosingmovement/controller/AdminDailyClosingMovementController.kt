package com.cleevio.cinemax.api.module.dailyclosingmovement.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.module.dailyclosingmovement.controller.dto.CreateOtherDailyClosingMovementRequest
import com.cleevio.cinemax.api.module.dailyclosingmovement.controller.dto.UpdateOtherDailyClosingMovementRequest
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.DailyClosingMovementService
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.command.CreateOrUpdateDeductionDailyClosingMovementCommand
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.command.DeleteOtherDailyClosingMovementCommand
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Manager Daily Closing Movements")
@RestController
@RequestMapping("/manager-app/daily-closings")
class AdminDailyClosingMovementController(
    private val dailyClosingMovementService: DailyClosingMovementService,
) {

    @PreAuthorize(Role.MANAGER)
    @PostMapping("/{dailyClosingId}/movements/other", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun createOtherDailyClosingMovement(
        @PathVariable dailyClosingId: UUID,
        @RequestBody request: CreateOtherDailyClosingMovementRequest,
    ): Unit = dailyClosingMovementService.createOtherMovement(request.toCommand(dailyClosingId))

    @PreAuthorize(Role.MANAGER)
    @PutMapping("/{dailyClosingId}/movements/other/{dailyClosingMovementId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun updateOtherDailyClosingMovement(
        @PathVariable dailyClosingId: UUID,
        @PathVariable dailyClosingMovementId: UUID,
        @RequestBody request: UpdateOtherDailyClosingMovementRequest,
    ): Unit = dailyClosingMovementService.updateOtherMovement(
        request.toCommand(
            dailyClosingId = dailyClosingId,
            dailyClosingMovementId = dailyClosingMovementId
        )
    )

    @PreAuthorize(Role.MANAGER)
    @DeleteMapping("/{dailyClosingId}/movements/other/{dailyClosingMovementId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun deleteOtherDailyClosingMovement(
        @PathVariable dailyClosingId: UUID,
        @PathVariable dailyClosingMovementId: UUID,
    ): Unit = dailyClosingMovementService.deleteOtherMovement(
        DeleteOtherDailyClosingMovementCommand(
            dailyClosingMovementId = dailyClosingMovementId,
            dailyClosingId = dailyClosingId
        )
    )

    @PreAuthorize(Role.MANAGER)
    @PostMapping("/{dailyClosingId}/movements/deduction", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun createDeductionDailyClosingMovement(
        @PathVariable dailyClosingId: UUID,
    ): Unit = dailyClosingMovementService.createOrUpdateDeductionMovement(
        CreateOrUpdateDeductionDailyClosingMovementCommand(dailyClosingId)
    )
}
