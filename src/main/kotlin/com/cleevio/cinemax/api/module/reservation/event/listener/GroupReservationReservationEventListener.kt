package com.cleevio.cinemax.api.module.reservation.event.listener

import com.cleevio.cinemax.api.common.util.ifTrue
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.module.groupreservation.event.GroupReservationDeletedEvent
import com.cleevio.cinemax.api.module.groupreservation.event.GroupReservationsCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.reservation.service.ReservationJpaFinderService
import com.cleevio.cinemax.api.module.reservation.service.ReservationMssqlSynchronizationService
import com.cleevio.cinemax.api.module.reservation.service.ReservationService
import com.cleevio.cinemax.api.module.reservation.service.command.DeleteReservationsCommand
import com.cleevio.cinemax.api.module.reservation.service.command.SynchronizeAllByOriginalGroupReservationIdsCommand
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    "features.reservation.group-reservation-listener.enabled",
    havingValue = "true",
    matchIfMissing = true
)
class GroupReservationReservationEventListener(
    private val reservationMssqlSynchronizationService: ReservationMssqlSynchronizationService,
    private val reservationService: ReservationService,
    private val reservationJpaFinderService: ReservationJpaFinderService,
) {

    @EventListener
    fun listenToGroupReservationsCreatedOrUpdatedEvent(event: GroupReservationsCreatedOrUpdatedEvent) {
        reservationMssqlSynchronizationService.synchronizeAllByOriginalGroupReservationIds(
            SynchronizeAllByOriginalGroupReservationIdsCommand(
                originalGroupReservationIds = event.originalGroupReservationIds
            )
        )
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToGroupReservationDeletedEvent(event: GroupReservationDeletedEvent) {
        val reservations = reservationJpaFinderService.findAllNonDeletedByGroupReservationId(event.groupReservationId)

        reservations.isNotEmpty().ifTrue {
            reservationService.deleteReservations(
                DeleteReservationsCommand(
                    reservationIds = reservations.mapToSet { it.id }
                )
            )
        }
    }
}
