package com.cleevio.cinemax.api.module.movie.exception

import com.cleevio.cinemax.api.common.exception.ErrorType

enum class MovieErrorType(
    override val code: String,
    override val message: String? = null,
) : ErrorType {
    MOVIE_NOT_FOUND("100", "Movie not found."),
    FAILED_TO_PARSE_MOVIE_TITLE("101", "Failed to parse movie title."),
    SCREENING_FOR_MOVIE_EXISTS("103", "Non deleted screening for movie exists."),
    MOVIE_CODE_ALREADY_EXISTS("104", "Movie code already exists."),
}
