package com.cleevio.cinemax.api.module.employee.exception

import com.cleevio.cinemax.api.common.exception.ErrorType

enum class EmployeeErrorType(
    override val code: String,
    override val message: String? = null,
) : ErrorType {
    EMPLOYEE_NOT_FOUND("100", "Employee not found."),
    AUTHENTICATION_FAILED("101", "Authentication failed."),
    PASSWORD_RESET_UNAVAILABLE("102", "Password reset unavailable for given employee."),
    EMPLOYEE_USERNAME_ALREADY_EXISTS("103", "Employee with given username already exists."),
    INVALID_PASSWORD_SIZE("104", "Password size must be between 6 and 10 characters."),
}
