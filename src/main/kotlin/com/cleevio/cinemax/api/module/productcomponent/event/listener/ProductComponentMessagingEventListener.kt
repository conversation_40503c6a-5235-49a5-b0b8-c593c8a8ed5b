package com.cleevio.cinemax.api.module.productcomponent.event.listener

import com.cleevio.cinemax.api.common.service.PublisherService
import com.cleevio.cinemax.api.module.productcomponent.event.AdminProductComponentCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.productcomponent.event.AdminProductComponentDeletedEvent
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    "spring.cloud.gcp.pubsub.enabled",
    havingValue = "true"
)
class ProductComponentMessagingEventListener(
    private val publisherService: PublisherService,
) {

    @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
    fun listenToAdminProductComponentCreatedOrUpdatedEvent(event: AdminProductComponentCreatedOrUpdatedEvent) {
        publisherService.publish(messagePayload = event.toMessagePayload())
    }

    @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
    fun listenToAdminProductComponentDeletedEvent(event: AdminProductComponentDeletedEvent) {
        publisherService.publish(messagePayload = event.toMessagePayload())
    }
}
