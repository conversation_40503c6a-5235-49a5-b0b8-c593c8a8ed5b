package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationJpaFinderService
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationMssqlFinderRepository
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables.REZERVACE
import org.jooq.DSLContext
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class GroupReservationDeletedEventProcessor(
    private val mssqlCinemaxDslContext: DSLContext,
    private val groupReservationJpaFinderService: GroupReservationJpaFinderService,
    private val groupReservationMssqlFinderRepository: GroupReservationMssqlFinderRepository,
) {
    private val log = logger()

    @Transactional(transactionManager = "mssqlCinemaxTransactionManager")
    fun process(event: OutboxEvent): Int {
        log.info("Starting to process OutboxEvent=$event.")

        groupReservationJpaFinderService.findById(event.entityId)?.let {
            it.originalId?.let { originalId ->
                if (!groupReservationMssqlFinderRepository.existsByOriginalId(originalId)) {
                    log.error("MSSQL rezervace record with originalId $originalId not found.")
                    return 0
                }
                return deleteMssqlGroupReservationRecord(originalId)
            } ?: run {
                log.error("MSSQL rezervace record was not created or its originalId could not be fetched.")
                return 0
            }
        } ?: run {
            log.error("Group reservation with id=${event.entityId} not found.")
            return 0
        }
    }

    private fun deleteMssqlGroupReservationRecord(
        groupReservationOriginalId: Int,
    ): Int {
        return mssqlCinemaxDslContext
            .delete(REZERVACE)
            .where(REZERVACE.REZERVACEID.eq(groupReservationOriginalId))
            .execute()
    }
}
