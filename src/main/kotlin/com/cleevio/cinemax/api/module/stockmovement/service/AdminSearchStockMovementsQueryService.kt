package com.cleevio.cinemax.api.module.stockmovement.service

import com.cleevio.cinemax.api.common.util.paginationAndSorting
import com.cleevio.cinemax.api.module.stockmovement.controller.dto.AdminSearchStockMovementsResponse
import com.cleevio.cinemax.api.module.stockmovement.controller.dto.ProductComponentCategoryResponse
import com.cleevio.cinemax.api.module.stockmovement.controller.dto.StockMovementProductComponentResponse
import com.cleevio.cinemax.api.module.stockmovement.controller.dto.StockMovementSupplierResponse
import com.cleevio.cinemax.api.module.stockmovement.service.query.AdminSearchStockMovementsQuery
import com.cleevio.cinemax.psql.Tables.STOCK_MOVEMENT
import jakarta.validation.Valid
import org.jooq.DSLContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class AdminSearchStockMovementsQueryService(
    private val psqlDslContext: DSLContext,
) {

    operator fun invoke(
        @Valid query: AdminSearchStockMovementsQuery,
    ): Page<AdminSearchStockMovementsResponse> {
        val count = psqlDslContext.fetchCount(STOCK_MOVEMENT, STOCK_MOVEMENT_SEARCH_CONDITIONS(query.filter))

        return psqlDslContext
            .select(STOCK_MOVEMENT_SEARCH_SELECT_FIELDS)
            .from(STOCK_MOVEMENT)
            .where(STOCK_MOVEMENT_SEARCH_CONDITIONS(query.filter))
            .paginationAndSorting(
                query.pageable,
                STOCK_MOVEMENT,
                STOCK_MOVEMENT.productComponent(),
                STOCK_MOVEMENT.supplier(),
                STOCK_MOVEMENT.productComponent().productComponentCategory()
            )
            .fetch()
            .map {
                AdminSearchStockMovementsResponse(
                    id = it[STOCK_MOVEMENT.ID],
                    type = it[STOCK_MOVEMENT.TYPE],
                    quantity = it[STOCK_MOVEMENT.QUANTITY],
                    price = it[STOCK_MOVEMENT.PRICE],
                    receiptNumber = it[STOCK_MOVEMENT.RECEIPT_NUMBER],
                    note = it[STOCK_MOVEMENT.NOTE],
                    supplier = it[STOCK_MOVEMENT.supplier().ID]?.let { supplierId ->
                        StockMovementSupplierResponse(
                            id = supplierId,
                            title = it[STOCK_MOVEMENT.supplier().TITLE]
                        )
                    },
                    productComponent = StockMovementProductComponentResponse(
                        id = it[STOCK_MOVEMENT.productComponent().ID],
                        title = it[STOCK_MOVEMENT.productComponent().TITLE],
                        originalCode = it[STOCK_MOVEMENT.productComponent().CODE],
                        unit = it[STOCK_MOVEMENT.productComponent().UNIT],
                        category = ProductComponentCategoryResponse(
                            id = it[STOCK_MOVEMENT.productComponent().productComponentCategory().ID],
                            title = it[STOCK_MOVEMENT.productComponent().productComponentCategory().TITLE],
                            taxRate = it[STOCK_MOVEMENT.productComponent().productComponentCategory().TAX_RATE]
                        )
                    ),
                    recordedAt = it[STOCK_MOVEMENT.RECORDED_AT],
                    createdAt = it[STOCK_MOVEMENT.CREATED_AT],
                    updatedAt = it[STOCK_MOVEMENT.UPDATED_AT]
                )
            }.let {
                PageImpl(it, query.pageable, count.toLong())
            }
    }
}
