package com.cleevio.cinemax.api.module.movie.service.query

import org.springframework.data.domain.Pageable
import java.time.LocalDate
import java.util.UUID

data class AdminSearchMoviesFilter(
    val premiereDateFrom: LocalDate? = null,
    val premiereDateTo: LocalDate? = null,
    val title: String? = null,
    val originalTitle: String? = null,
    val distributorTitle: String? = null,
    val movieIds: Set<UUID>? = null,
) {
    fun toQuery(pageable: Pageable) = AdminSearchMoviesQuery(pageable = pageable, filter = this)
}
