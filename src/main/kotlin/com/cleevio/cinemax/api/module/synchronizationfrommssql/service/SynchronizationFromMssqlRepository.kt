package com.cleevio.cinemax.api.module.synchronizationfrommssql.service

import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.Optional
import java.util.UUID

@Repository
interface SynchronizationFromMssqlRepository : JpaRepository<SynchronizationFromMssql, UUID> {

    fun findByType(type: SynchronizationFromMssqlType): Optional<SynchronizationFromMssql>
}
