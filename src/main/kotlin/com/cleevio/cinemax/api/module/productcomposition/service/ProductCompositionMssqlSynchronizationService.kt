package com.cleevio.cinemax.api.module.productcomposition.service

import com.cleevio.cinemax.api.common.constant.PRODUCT_COMPOSITION
import com.cleevio.cinemax.api.common.service.AbstractMssqlSynchronizationService
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.setNullIfZero
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.service.ProductJooqFinderService
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentJpaFinderService
import com.cleevio.cinemax.api.module.productcomposition.constant.ProductCompositionLockValues.SYNCHRONIZE_FROM_MSSQL
import com.cleevio.cinemax.api.module.productcomposition.service.command.CreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlService
import com.cleevio.cinemax.mssql.rp_bufete.dbo.Tables.RPOLM
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Rpolm
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class ProductCompositionMssqlSynchronizationService(
    private val productCompositionService: ProductCompositionService,
    private val productCompositionMssqlFinderRepository: ProductCompositionMssqlFinderRepository,
    private val productJooqFinderService: ProductJooqFinderService,
    private val productComponentFinderService: ProductComponentJpaFinderService,
    private val synchronizationFromMssqlFinderService: SynchronizationFromMssqlFinderService,
    synchronizationFromMssqlService: SynchronizationFromMssqlService,
) : AbstractMssqlSynchronizationService<Rpolm>(
    mssqlEntityName = RPOLM.name,
    synchronizationFromMssqlService = synchronizationFromMssqlService,
    synchronizationType = SynchronizationFromMssqlType.PRODUCT_COMPOSITION
) {
    private val log = logger()

    @TryLock(PRODUCT_COMPOSITION, SYNCHRONIZE_FROM_MSSQL)
    fun synchronizeAll() = synchronizeAll {
        productCompositionMssqlFinderRepository.findAllByUpdatedAtGt(
            synchronizationFromMssqlFinderService.findByType(SynchronizationFromMssqlType.PRODUCT_COMPOSITION)
        )
    }

    override fun validateAndPersist(mssqlEntity: Rpolm) {
        val product = productJooqFinderService.findNonDeletedByOriginalId(mssqlEntity.rmenuid) ?: run {
            log.warn(
                "Product with originalId=${mssqlEntity.rmenuid} not found. " +
                    "Skipping sync of ProductComposition originalId=${mssqlEntity.rpolmid}."
            )
            return
        }

        val productInProduct = (mssqlEntity.rmenu2id ?: 0).setNullIfZero()?.let {
            productJooqFinderService.findNonDeletedByOriginalId(it) ?: run {
                log.warn(
                    "Product in product with originalId=$it not found. " +
                        "Skipping sync of ProductComposition originalId=${mssqlEntity.rpolmid}."
                )
                return
            }
        }

        val productComponent = mssqlEntity.rzboziid.setNullIfZero()?.let {
            productComponentFinderService.findNonDeletedByOriginalId(it) ?: run {
                log.warn(
                    "ProductComponent with originalId=$it not found. " +
                        "Skipping sync of ProductComposition originalId=${mssqlEntity.rpolmid}."
                )
                return
            }
        }

        val packagingDeposit = mssqlEntity.rzbozirid.setNullIfZero()?.let {
            productJooqFinderService.findNonDeletedByOriginalId(-it) ?: run {
                log.warn(
                    "Packaging deposit with originalId=${-it} not found. " +
                        "Skipping sync of ProductComposition originalId=${mssqlEntity.rpolmid}."
                )
                return
            }
        }

        productCompositionService.createOrUpdateProductComposition(
            mapToCreateOrUpdateProductCompositionCommand(
                mssqlComposition = mssqlEntity,
                productId = product.id,
                productComponentId = productComponent?.id,
                productInProduct = productInProduct,
                packagingDepositId = packagingDeposit?.id
            )
        )
    }

    private fun mapToCreateOrUpdateProductCompositionCommand(
        mssqlComposition: Rpolm,
        productId: UUID,
        productComponentId: UUID?,
        productInProduct: Product?,
        packagingDepositId: UUID?,
    ) = CreateOrUpdateProductCompositionCommand(
        originalId = mssqlComposition.rpolmid,
        productId = productId,
        productComponentId = productComponentId,
        productInProductId = productInProduct?.id ?: packagingDepositId,
        amount = mssqlComposition.mnozs.toBigDecimal(),
        productInProductPrice = productInProduct?.price
    )
}
