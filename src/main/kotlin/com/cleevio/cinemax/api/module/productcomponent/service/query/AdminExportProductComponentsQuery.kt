package com.cleevio.cinemax.api.module.productcomponent.service.query

import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.service.query.UnpagedQuery

data class AdminExportProductComponentsQuery(
    override val filter: AdminExportProductComponentsFilter,
    val exportFormat: ExportFormat,
    val username: String,
) : UnpagedQuery<AdminExportProductComponentsFilter>
