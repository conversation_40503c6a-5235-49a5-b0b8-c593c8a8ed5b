package com.cleevio.cinemax.api.module.reservation.event.listener

import com.cleevio.cinemax.api.module.reservation.event.ReservationAlreadyExistsInMssqlEvent
import com.cleevio.cinemax.api.module.reservation.service.ReservationMssqlSynchronizationService
import com.cleevio.cinemax.api.module.reservation.service.command.SynchronizeAllByOriginalScreeningIdCommand
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    "features.reservation.reservation-listener.enabled",
    havingValue = "true",
    matchIfMissing = true
)
class ReservationReservationEventListener(
    private val reservationMssqlSynchronizationService: ReservationMssqlSynchronizationService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_ROLLBACK)
    fun listenToReservationAlreadyExistsInMssqlEvent(event: ReservationAlreadyExistsInMssqlEvent) {
        reservationMssqlSynchronizationService.synchronizeAllByOriginalScreeningId(
            SynchronizeAllByOriginalScreeningIdCommand(
                originalScreeningId = event.originalScreeningId
            )
        )
    }
}
