package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.TaxRateType
import com.cleevio.cinemax.api.common.util.toPercentage
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchTicketBasketItemsSummaryResponse
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminSearchTicketBasketItemsQuery
import com.cleevio.cinemax.psql.Tables.DISCOUNT_CARD
import com.cleevio.cinemax.psql.Tables.DISCOUNT_CARD_USAGE
import com.cleevio.cinemax.psql.Tables.PRICE_CATEGORY_ITEM
import com.cleevio.cinemax.psql.tables.BasketItem.BASKET_ITEM
import jakarta.validation.Valid
import org.jooq.DSLContext
import org.jooq.impl.DSL.count
import org.jooq.impl.DSL.field
import org.jooq.impl.DSL.round
import org.jooq.impl.DSL.select
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class AdminSearchTicketBasketItemSummaryQueryService(
    private val psqlDslContext: DSLContext,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
) {

    operator fun invoke(
        @Valid query: AdminSearchTicketBasketItemsQuery,
    ): AdminSearchTicketBasketItemsSummaryResponse {
        val ticketBasketItemConditions = createTicketBasketItemFilterConditions(query.filter)
        val screeningCountConditions = createScreeningsCountConditions(query.filter)

        val netSalesCoefficient = 1.toBigDecimal() + cinemaxConfigProperties.getTaxRate(TaxRateType.STANDARD).toPercentage()
        val taxAmount = field(
            select(
                NOT_CANCELLED_ITEMS_SUM - round(NOT_CANCELLED_ITEMS_SUM / netSalesCoefficient, 2)
            )
        )

        val screeningTypesByScreeningCte = createScreeningTypesByScreeningCte(query.filter.screeningTypeIds)
        val screeningTypesScreeningId =
            screeningTypesByScreeningCte.field(SCREENING_TYPES_SCREENING_ID, UUID::class.java)!!
        val screeningTypesScreeningTypeId =
            screeningTypesByScreeningCte.field(SCREENING_TYPES_SCREENING_TYPE_ID, UUID::class.java)!!

        val discountResponses = psqlDslContext.fetch(
            createDiscountCountsQuery(
                ticketBasketItemConditions = ticketBasketItemConditions,
                screeningTypesByScreeningCte = screeningTypesByScreeningCte,
                screeningTypeIds = query.filter.screeningTypeIds
            )
        ).map {
            AdminSearchTicketBasketItemsSummaryResponse.DiscountResponse(
                title = it[TICKET_DISCOUNT_TITLE_FIELD] ?: "", // active ticket discounts with null title exist in DB
                count = it[count(BASKET_ITEM.ID)]
            )
        }

        return psqlDslContext.with(
            screeningTypesByScreeningCte
        ).select(
            SCREENINGS_COUNT_FIELD(screeningCountConditions),
            NOT_CANCELLED_ITEMS_COUNT,
            NOT_CANCELLED_ITEMS_SUM,
            TICKETS_USED_COUNT,
            SALES_CASH_SUM,
            SALES_CASHLESS_SUM,
            PRO_DEDUCTION,
            FILM_FOND_DEDUCTION,
            DISTRIBUTOR_DEDUCTION,
            taxAmount,
            CANCELLED_ITEMS_COUNT,
            CANCELLED_ITEMS_EXPENSE,
            GENERAL_SERVICE_FEE_SUM,
            GENERAL_SERVICE_FEE_COUNT,
            VIP_SERVICE_FEE_SUM,
            VIP_SURCHARGE_SUM,
            VIP_COUNT,
            DBOX_SURCHARGE_SUM,
            DBOX_COUNT,
            PP_SERVICE_FEE_SUM,
            PP_SURCHARGE_SUM,
            PP_COUNT,
            IMAX_SERVICE_FEE_SUM,
            IMAX_SURCHARGE_SUM,
            IMAX_COUNT,
            ULTRA_X_SERVICE_FEE_SUM,
            ULTRA_X_SURCHARGE_SUM,
            ULTRA_X_COUNT,
            NOT_DISCOUNTED_TICKETS_COUNT
        )
            .from(BASKET_ITEM)
            .leftJoin(PRICE_CATEGORY_ITEM).on(
                BASKET_ITEM.ticket().screening().priceCategory().ID.eq(PRICE_CATEGORY_ITEM.PRICE_CATEGORY_ID)
                    .and(BASKET_ITEM.ticket().ticketPrice().BASE_PRICE_ITEM_NUMBER.eq(PRICE_CATEGORY_ITEM.NUMBER))
            )
            .leftJoin(screeningTypesByScreeningCte).on(BASKET_ITEM.ticket().screening().ID.eq(screeningTypesScreeningId))
            .leftJoin(DISCOUNT_CARD_USAGE).on(BASKET_ITEM.ID.eq(DISCOUNT_CARD_USAGE.TICKET_BASKET_ITEM_ID))
            .leftJoin(DISCOUNT_CARD).on(DISCOUNT_CARD.ID.eq(DISCOUNT_CARD_USAGE.DISCOUNT_CARD_ID))
            .where(
                ticketBasketItemConditions.plus(
                    query.filter.screeningTypeIds?.let { screeningTypesScreeningTypeId.`in`(it) }
                )
            )
            .fetchOne()
            ?.map {
                AdminSearchTicketBasketItemsSummaryResponse(
                    sales = AdminSearchTicketBasketItemsSummaryResponse.SalesResponse(
                        screeningsCount = it[SCREENINGS_COUNT_FIELD(screeningCountConditions)],
                        ticketsCount = it[NOT_CANCELLED_ITEMS_COUNT],
                        ticketsUsedCount = it[TICKETS_USED_COUNT],
                        salesCash = it[SALES_CASH_SUM],
                        salesCashless = it[SALES_CASHLESS_SUM],
                        grossSales = it[NOT_CANCELLED_ITEMS_SUM],
                        netSales = it[NOT_CANCELLED_ITEMS_SUM]
                            .minus(it[PRO_DEDUCTION])
                            .minus(it[FILM_FOND_DEDUCTION])
                            .minus(it[DISTRIBUTOR_DEDUCTION])
                            .minus(it[taxAmount]),
                        proDeduction = it[PRO_DEDUCTION],
                        filmFondDeduction = it[FILM_FOND_DEDUCTION],
                        distributorDeduction = it[DISTRIBUTOR_DEDUCTION],
                        taxAmount = it[taxAmount],
                        cancelledTicketsCount = it[CANCELLED_ITEMS_COUNT],
                        cancelledTicketsExpense = it[CANCELLED_ITEMS_EXPENSE]
                    ),
                    technologies = AdminSearchTicketBasketItemsSummaryResponse.TechnologiesResponse(
                        serviceFees = AdminSearchTicketBasketItemsSummaryResponse.ServiceFeesResponse(
                            general = it[GENERAL_SERVICE_FEE_SUM],
                            vip = it[VIP_SERVICE_FEE_SUM],
                            premiumPlus = it[PP_SERVICE_FEE_SUM],
                            imax = it[IMAX_SERVICE_FEE_SUM],
                            dolbyAtmos = it[ULTRA_X_SERVICE_FEE_SUM],
                            total = it[GENERAL_SERVICE_FEE_SUM] +
                                it[VIP_SERVICE_FEE_SUM] +
                                it[PP_SERVICE_FEE_SUM] +
                                it[IMAX_SERVICE_FEE_SUM] +
                                it[ULTRA_X_SERVICE_FEE_SUM]
                        ),
                        surcharges = AdminSearchTicketBasketItemsSummaryResponse.SurchargesResponse(
                            vip = it[VIP_SURCHARGE_SUM],
                            dBox = it[DBOX_SURCHARGE_SUM],
                            premiumPlus = it[PP_SURCHARGE_SUM],
                            imax = it[IMAX_SURCHARGE_SUM],
                            dolbyAtmos = it[ULTRA_X_SURCHARGE_SUM],
                            total = it[VIP_SURCHARGE_SUM] +
                                it[DBOX_SURCHARGE_SUM] +
                                it[PP_SURCHARGE_SUM] +
                                it[IMAX_SURCHARGE_SUM] +
                                it[ULTRA_X_SURCHARGE_SUM]
                        ),
                        counts = AdminSearchTicketBasketItemsSummaryResponse.CountsResponse(
                            general = it[GENERAL_SERVICE_FEE_COUNT],
                            vip = it[VIP_COUNT],
                            dBox = it[DBOX_COUNT],
                            premiumPlus = it[PP_COUNT],
                            imax = it[IMAX_COUNT],
                            dolbyAtmos = it[ULTRA_X_COUNT],
                            total = it[GENERAL_SERVICE_FEE_COUNT] +
                                it[VIP_COUNT] +
                                it[DBOX_COUNT] +
                                it[PP_COUNT] +
                                it[IMAX_COUNT] +
                                it[ULTRA_X_COUNT]
                        )
                    ),
                    discounts = listOf(
                        AdminSearchTicketBasketItemsSummaryResponse.DiscountResponse(
                            title = NO_DISCOUNT_TITLE,
                            count = it[NOT_DISCOUNTED_TICKETS_COUNT]
                        )
                    ) + discountResponses.sortedBy { discount -> discount.title }
                )
            } ?: error("Result for ticket basket item summary could not be fetched.")
    }
}

const val NO_DISCOUNT_TITLE = "Bez slevy"
