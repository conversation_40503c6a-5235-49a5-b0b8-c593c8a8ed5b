package com.cleevio.cinemax.api.module.table.service

import com.cleevio.cinemax.api.common.util.buildGreaterThanDateTimeMssqlCondition
import com.cleevio.cinemax.mssql.rp_bufete.dbo.Tables.STOLY
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Stoly
import org.jooq.DSLContext
import org.jooq.impl.DSL.condition
import org.jooq.impl.DSL.ltrim
import org.jooq.impl.DSL.rtrim
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class TableMssqlFinderRepository(
    private val mssqlBuffetDslContext: DSLContext,
) {

    fun findAll(): List<Stoly> {
        return mssqlBuffetDslContext
            .selectFrom(STOLY)
            .fetchInto(Stoly::class.java)
    }

    fun findAllByUpdatedAtGt(updatedAt: LocalDateTime? = null): List<Stoly> {
        return mssqlBuffetDslContext
            .selectFrom(STOLY)
            .where(buildGreaterThanDateTimeMssqlCondition(updatedAt, STOLY.ZCAS))
            .fetchInto(Stoly::class.java)
    }

    fun findByOriginalId(originalId: Int): Stoly? {
        return mssqlBuffetDslContext
            .selectFrom(STOLY)
            .where(STOLY.STOLYID.eq(originalId))
            .fetchOneInto(Stoly::class.java)
    }

    fun findAllVipSeats(): List<Stoly> {
        return mssqlBuffetDslContext
            .selectFrom(STOLY)
            .where(STOLY.TALETPRODEJ.isTrue)
            .and(condition(ltrim(rtrim(STOLY.MAC)).toString().isNotBlank()))
            .fetchInto(Stoly::class.java)
    }
}
