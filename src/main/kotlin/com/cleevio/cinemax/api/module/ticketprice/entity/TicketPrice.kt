package com.cleevio.cinemax.api.module.ticketprice.entity

import com.cleevio.cinemax.api.common.entity.UpdatableNonAuditableEntity
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import jakarta.persistence.Column
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.math.BigDecimal
import java.util.UUID
import jakarta.persistence.Entity as JpaEntity

@JpaEntity
class TicketPrice(
    id: UUID = UUID.randomUUID(),

    @Column(name = "screening_id", nullable = false)
    var screeningId: UUID,

    @Column(name = "seat_id", nullable = false)
    var seatId: UUID,

    @Column(name = "base_price", nullable = false)
    var basePrice: BigDecimal,

    @Column(name = "base_price_item_number", nullable = false)
    @Enumerated(EnumType.STRING)
    var basePriceItemNumber: PriceCategoryItemNumber,

    @Column(name = "base_price_before_discount", nullable = false)
    var basePriceBeforeDiscount: BigDecimal,

    @Column(name = "seat_surcharge")
    var seatSurcharge: BigDecimal? = null,

    @Column(name = "seat_surcharge_type")
    @Enumerated(EnumType.STRING)
    var seatSurchargeType: SeatSurchargeType? = null,

    @Column(name = "auditorium_surcharge")
    var auditoriumSurcharge: BigDecimal? = null,

    @Column(name = "auditorium_surcharge_type")
    @Enumerated(EnumType.STRING)
    var auditoriumSurchargeType: AuditoriumSurchargeType? = null,

    @Column(name = "seat_service_fee")
    var seatServiceFee: BigDecimal? = null,

    @Column(name = "seat_service_fee_type")
    @Enumerated(EnumType.STRING)
    var seatServiceFeeType: SeatServiceFeeType? = null,

    @Column(name = "auditorium_service_fee")
    var auditoriumServiceFee: BigDecimal? = null,

    @Column(name = "auditorium_service_fee_type")
    @Enumerated(EnumType.STRING)
    var auditoriumServiceFeeType: AuditoriumServiceFeeType? = null,

    @Column(name = "service_fee_general")
    var serviceFeeGeneral: BigDecimal? = null,

    @Column(name = "primary_discount_percentage")
    var primaryDiscountPercentage: Int? = null,

    @Column(name = "primary_discount_amount")
    var primaryDiscountAmount: BigDecimal? = null,

    @Column(name = "secondary_discount_percentage")
    var secondaryDiscountPercentage: Int? = null,

    @Column(name = "secondary_discount_amount")
    var secondaryDiscountAmount: BigDecimal? = null,

    @Column(name = "free_ticket")
    var freeTicket: Boolean? = null,

    @Column(name = "zero_fees")
    var zeroFees: Boolean? = null,

    @Column(name = "total_price", nullable = false)
    var totalPrice: BigDecimal,
) : UpdatableNonAuditableEntity(id) {

    fun getSurchargeAndFeeSum() = listOfNotNull(
        seatSurcharge,
        auditoriumSurcharge,
        seatServiceFee,
        auditoriumServiceFee,
        serviceFeeGeneral
    ).sumOf { it }

    fun getSurchargeSumExceptDBox(): BigDecimal {
        if (zeroFees == true) return BigDecimal.ZERO

        return listOfNotNull(
            seatSurchargeType?.let {
                if (it != SeatSurchargeType.DBOX) seatSurcharge else BigDecimal.ZERO
            },
            auditoriumSurcharge
        ).sumOf { it }
    }

    fun getFeeSum(): BigDecimal {
        if (zeroFees == true) return BigDecimal.ZERO

        return listOfNotNull(
            seatServiceFee,
            auditoriumServiceFee,
            serviceFeeGeneral
        ).sumOf { it }
    }

    fun getSeatFeeByType(type: SeatServiceFeeType): BigDecimal {
        if (zeroFees == true) return BigDecimal.ZERO
        return if (seatServiceFeeType == type) seatServiceFee ?: BigDecimal.ZERO else BigDecimal.ZERO
    }

    fun getAuditoriumFeeByType(type: AuditoriumServiceFeeType): BigDecimal {
        if (zeroFees == true) return BigDecimal.ZERO
        return if (auditoriumServiceFeeType == type) auditoriumServiceFee ?: BigDecimal.ZERO else BigDecimal.ZERO
    }

    fun getSeatSurchargeByType(type: SeatSurchargeType): BigDecimal {
        if (zeroFees == true) return BigDecimal.ZERO
        return if (seatSurchargeType == type) seatSurcharge ?: BigDecimal.ZERO else BigDecimal.ZERO
    }

    fun getAuditoriumSurchargeByType(type: AuditoriumSurchargeType): BigDecimal {
        if (zeroFees == true) return BigDecimal.ZERO
        return if (auditoriumSurchargeType == type) auditoriumSurcharge ?: BigDecimal.ZERO else BigDecimal.ZERO
    }
}
