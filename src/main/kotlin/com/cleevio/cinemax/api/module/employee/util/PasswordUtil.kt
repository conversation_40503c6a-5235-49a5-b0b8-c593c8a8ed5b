package com.cleevio.cinemax.api.module.employee.util

fun remove<PERSON>ciiControlCharacters(password: String): String = password.replace(Regex("\\p{Cntrl}"), "")

fun decodePassword(password: String) = password.mapIndexed { index, char -> (char.code + index + 1).toChar() }.joinToString("")

fun encodePassword(password: String) = password.mapIndexed { index, char -> (char.code - index - 1).toChar() }.joinToString("")
