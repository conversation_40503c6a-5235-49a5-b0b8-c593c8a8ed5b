package com.cleevio.cinemax.api.module.pricecategory.service

import com.cleevio.cinemax.api.common.constant.PRICE_CATEGORY
import com.cleevio.cinemax.api.common.util.ifTrue
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.module.pricecategory.constant.PriceCategoryLockValues.CREATE_OR_UPDATE_PRICE_CATEGORY
import com.cleevio.cinemax.api.module.pricecategory.entity.PriceCategory
import com.cleevio.cinemax.api.module.pricecategory.event.listener.PriceCategoryCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.pricecategory.exception.InvalidOnlinePriceCategoryItemException
import com.cleevio.cinemax.api.module.pricecategory.service.command.AdminCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.module.pricecategory.service.command.SyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.module.pricecategory.service.command.UpdatePriceCategoryOriginalIdCommand
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.exception.DuplicatePriceCategoryItemNumberException
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.module.pricecategoryitem.service.command.AdminCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.module.pricecategoryitem.service.command.DeleteAndCreatePriceCategoryItemsCommand
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import jakarta.validation.Valid
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class PriceCategoryService(
    private val priceCategoryJpaFinderService: PriceCategoryJpaFinderService,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    private val log = logger()

    @Transactional
    @Lock(PRICE_CATEGORY, CREATE_OR_UPDATE_PRICE_CATEGORY)
    fun syncCreateOrUpdatePriceCategory(
        @Valid
        @LockFieldParameter("originalId")
        command: SyncCreateOrUpdatePriceCategoryCommand,
    ): PriceCategory? {
        val priceCategory = priceCategoryJpaFinderService.findByOriginalId(command.originalId)
        if (priceCategory != null && priceCategory.isDeleted()) {
            log.warn("Price category with originalId ${priceCategory.originalId} has been already deleted. Skipping.")
            return null
        }

        priceCategory?.let {
            val updated = priceCategoryRepository.save(mapToExistingPriceCategory(it, command))
            log.debug("Updated price category ${updated.id} (${updated.originalId}).")
            return updated
        } ?: run {
            val created = priceCategoryRepository.save(mapToNewPriceCategory(command))
            log.debug("Created price category ${created.id} (${created.originalId}).")
            return created
        }
    }

    @Transactional
    @Lock(module = PRICE_CATEGORY, lockName = CREATE_OR_UPDATE_PRICE_CATEGORY)
    fun adminCreateOrUpdatePriceCategory(
        @Valid
        @LockFieldParameter("id")
        command: AdminCreateOrUpdatePriceCategoryCommand,
    ): UUID {
        containsDuplicateItemNumbers(command.items).ifTrue { throw DuplicatePriceCategoryItemNumberException() }
        verifyOnlinePriceCategoryItems(command.items)

        val priceCategory = command.id?.let { priceCategoryJpaFinderService.getNonDeletedById(it) }

        return priceCategoryRepository.save(
            priceCategory?.let {
                mapToExistingPriceCategory(priceCategory = it, command = command)
            } ?: run {
                mapToNewPriceCategory(command)
            }
        ).id.also {
            deleteAndCreatePriceCategoryItems(priceCategoryId = it, items = command.items)
            applicationEventPublisher.publishEvent(PriceCategoryCreatedOrUpdatedEvent(it))
        }
    }

    private fun verifyOnlinePriceCategoryItems(items: List<AdminCreateOrUpdatePriceCategoryItemCommand>) {
        items.firstOrNull { it.number == PriceCategoryItemNumber.PRICE_19 && !it.discounted }?.let {
            throw InvalidOnlinePriceCategoryItemException()
        }
        items.firstOrNull { it.number == PriceCategoryItemNumber.PRICE_20 && it.discounted }?.let {
            throw InvalidOnlinePriceCategoryItemException()
        }
    }

    @Transactional
    @Lock(PRICE_CATEGORY, CREATE_OR_UPDATE_PRICE_CATEGORY)
    fun updatePriceCategoryOriginalId(
        @Valid
        @LockFieldParameter("priceCategoryId")
        command: UpdatePriceCategoryOriginalIdCommand,
    ) {
        priceCategoryJpaFinderService.getNonDeletedById(command.priceCategoryId).also {
            priceCategoryRepository.save(
                it.apply {
                    originalId = command.originalId
                }
            )
        }
    }

    private fun deleteAndCreatePriceCategoryItems(
        priceCategoryId: UUID,
        items: List<AdminCreateOrUpdatePriceCategoryItemCommand>,
    ) = priceCategoryItemService.deleteAndCreatePriceCategoryItems(
        DeleteAndCreatePriceCategoryItemsCommand(
            priceCategoryId = priceCategoryId,
            items = items
        )
    )

    private fun containsDuplicateItemNumbers(items: List<AdminCreateOrUpdatePriceCategoryItemCommand>): Boolean =
        items.mapToSet { it.number }.size != items.map { it.number }.size

    private fun mapToExistingPriceCategory(
        priceCategory: PriceCategory,
        command: SyncCreateOrUpdatePriceCategoryCommand,
    ) = priceCategory.apply {
        originalCode = command.originalCode
        title = command.title
        active = command.active
    }

    private fun mapToNewPriceCategory(command: SyncCreateOrUpdatePriceCategoryCommand) = PriceCategory(
        id = command.id ?: UUID.randomUUID(),
        originalId = command.originalId,
        originalCode = command.originalCode,
        title = command.title,
        active = command.active
    )

    private fun mapToExistingPriceCategory(
        priceCategory: PriceCategory,
        command: AdminCreateOrUpdatePriceCategoryCommand,
    ) = priceCategory.apply {
        title = command.title
        active = command.active
    }

    private fun mapToNewPriceCategory(command: AdminCreateOrUpdatePriceCategoryCommand) = PriceCategory(
        id = command.id ?: UUID.randomUUID(),
        originalId = null,
        originalCode = null,
        title = command.title,
        active = command.active
    )
}
