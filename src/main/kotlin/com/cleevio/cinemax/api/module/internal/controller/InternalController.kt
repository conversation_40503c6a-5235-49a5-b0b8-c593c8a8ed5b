package com.cleevio.cinemax.api.module.internal.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.module.basket.service.BasketMessagingService
import com.cleevio.cinemax.api.module.internal.controller.dto.InternalSynchronizeMessagingBasketsRequest
import com.cleevio.cinemax.api.module.internal.controller.dto.InternalSynchronizeMessagingMoviesRequest
import com.cleevio.cinemax.api.module.internal.controller.dto.InternalSynchronizeMessagingScreeningsRequest
import com.cleevio.cinemax.api.module.internal.controller.dto.InternalSynchronizeMssqlProductSalesRequest
import com.cleevio.cinemax.api.module.internal.controller.dto.InternalSynchronizeMssqlTicketSalesRequest
import com.cleevio.cinemax.api.module.internal.controller.dto.InternalSynchronizeMssqlTicketsRequest
import com.cleevio.cinemax.api.module.internal.controller.dto.InternalSynchronizeTicketsUsedRequest
import com.cleevio.cinemax.api.module.movie.service.MovieMessagingService
import com.cleevio.cinemax.api.module.movie.service.MovieUnificationService
import com.cleevio.cinemax.api.module.product.service.ProductMessagingService
import com.cleevio.cinemax.api.module.product.service.ProductSaleMssqlSynchronizationService
import com.cleevio.cinemax.api.module.screening.service.ScreeningMessagingService
import com.cleevio.cinemax.api.module.ticket.service.TicketMssqlSynchronizationService
import com.cleevio.cinemax.api.module.ticket.service.TicketSaleMssqlSynchronizationService
import com.cleevio.cinemax.api.module.ticket.service.TicketUsedSynchronizationService
import io.swagger.v3.oas.annotations.Hidden
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController

@Hidden
@RestController
@RequestMapping("/internal")
class InternalController(
    private val productSaleMssqlSynchronizationService: ProductSaleMssqlSynchronizationService,
    private val ticketSaleMssqlSynchronizationService: TicketSaleMssqlSynchronizationService,
    private val ticketMssqlSynchronizationService: TicketMssqlSynchronizationService,
    private val movieUnificationService: MovieUnificationService,
    private val movieMessagingService: MovieMessagingService,
    private val screeningMessagingService: ScreeningMessagingService,
    private val basketMessagingService: BasketMessagingService,
    private val productMessagingService: ProductMessagingService,
    private val ticketUsedSynchronizationService: TicketUsedSynchronizationService,
) {

    @PostMapping("/synchronize-mssql-product-sales", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun synchronizeMssqlProductSales(
        @RequestBody request: InternalSynchronizeMssqlProductSalesRequest,
    ): Unit = productSaleMssqlSynchronizationService.synchronizeAll(
        syncStartDate = request.syncStartDate,
        syncEndDate = request.syncEndDate,
        batchDaySpan = request.batchDaySpan
    )

    @PostMapping("/synchronize-mssql-ticket-sales", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun synchronizeMssqlTicketSales(
        @RequestBody request: InternalSynchronizeMssqlTicketSalesRequest,
    ): Unit = ticketSaleMssqlSynchronizationService.synchronizeAll(
        syncStartDate = request.syncStartDate,
        syncEndDate = request.syncEndDate,
        batchDaySpan = request.batchDaySpan
    )

    @PostMapping("/synchronize-mssql-tickets", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun synchronizeMssqlTickets(
        @RequestBody request: InternalSynchronizeMssqlTicketsRequest,
    ): Unit = ticketMssqlSynchronizationService.synchronizeAllByUpdatedAtIn(
        updatedAtFrom = request.updatedAtFrom,
        updatedAtTo = request.updatedAtTo
    )

    @PostMapping("/deduplicate-movies", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun deduplicateMovies(): Unit = movieUnificationService.deduplicateMovies()

    @PostMapping("/update-movies-messaging-codes", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun updateMoviesMessagingCodes(): Unit = movieUnificationService.updateMoviesMessagingCodes()

    @PostMapping("/sync-movies-to-branches", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun syncMoviesToBranches(
        @RequestBody request: InternalSynchronizeMessagingMoviesRequest,
    ): Unit = movieMessagingService.syncMoviesToBranches(
        premiereDateFrom = request.premiereDateFrom,
        premiereDateTo = request.premiereDateTo
    )

    @PostMapping("/sync-screenings-to-headquarters", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun syncScreeningsToHeadquarters(
        @RequestBody request: InternalSynchronizeMessagingScreeningsRequest,
    ): Unit = screeningMessagingService.syncScreeningsToHeadquarters(
        dateFrom = request.dateFrom,
        dateTo = request.dateTo
    )

    @PostMapping("/sync-baskets-to-headquarters", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun syncBasketsToHeadquarters(
        @RequestBody request: InternalSynchronizeMessagingBasketsRequest,
    ): Unit = basketMessagingService.syncBasketsToHeadquarters(
        paidAtFrom = request.paidAtFrom,
        paidAtTo = request.paidAtTo
    )

    @PostMapping("/sync-products-to-branches", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun syncProductsToBranches(): Unit = productMessagingService.syncProductsToBranches()

    @PostMapping("/sync-tickets-used", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun syncTicketsUsed(
        @RequestBody request: InternalSynchronizeTicketsUsedRequest,
    ): Unit = ticketUsedSynchronizationService.synchronizeTicketUsedForTimeRange(
        updatedAtFrom = request.updatedAtFrom,
        updatedAtTo = request.updatedAtTo
    )
}
