package com.cleevio.cinemax.api.module.basket.model

import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import java.util.UUID

data class BasketModel(
    val basket: Basket,
    val basketItems: List<BasketItem> = listOf(),
)

fun BasketModel.ticketItems() = this.basketItems.filter { it.type == BasketItemType.TICKET }
fun BasketModel.ticketItemIds() = this.basketItems.filter { it.type == BasketItemType.TICKET }
    .mapNotNull { it.ticketId }.toSet()
fun BasketModel.productItems() = this.basketItems.filter { it.type == BasketItemType.PRODUCT }
fun BasketModel.productDiscountItems() = this.basketItems.filter { it.type == BasketItemType.PRODUCT_DISCOUNT }
fun BasketModel.productAndDiscountItems() = this.basketItems.filter { PRODUCT_BASKET_ITEM_TYPES.contains(it.type) }
fun BasketModel.productAndDiscountItemIds() = this.basketItems.filter { PRODUCT_BASKET_ITEM_TYPES.contains(it.type) }
    .mapNotNull { it.productId }.toSet()
fun BasketModel.containsCancelledItems() = this.basketItems.any { it.isCancellationItem() }
fun BasketModel.isCancellationItem(ticketId: UUID) =
    this.basketItems.firstOrNull { it.ticketId == ticketId }?.isCancellationItem() ?: false

val PRODUCT_BASKET_ITEM_TYPES = setOf(BasketItemType.PRODUCT, BasketItemType.PRODUCT_DISCOUNT)
