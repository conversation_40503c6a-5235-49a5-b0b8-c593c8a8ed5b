package com.cleevio.cinemax.api.module.basket.service.command

import com.cleevio.cinemax.api.common.constant.PaymentType
import java.util.UUID

data class CreateVipBasketCommand(
    val tableId: UUID,
    val preferredPaymentType: PaymentType,
    val discountCardId: UUID?,
    val basketItems: List<CreateVipBasketItemInput>,
)

data class CreateVipBasketItemInput(
    val productId: UUID,
    val quantity: Int,
)
