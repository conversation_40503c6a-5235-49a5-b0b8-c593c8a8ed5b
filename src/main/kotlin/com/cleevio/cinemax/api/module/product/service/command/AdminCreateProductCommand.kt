package com.cleevio.cinemax.api.module.product.service.command

import com.cleevio.cinemax.api.common.validation.ValidTaxRate
import com.cleevio.cinemax.api.module.product.constant.ProductType
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.PositiveOrZero
import jakarta.validation.constraints.Size
import java.math.BigDecimal
import java.util.UUID

data class AdminCreateProductCommand(

    @field:NotBlank
    @field:Size(max = 40)
    val title: String,

    val active: Boolean,
    val type: ProductType,
    val productCategoryId: UUID,
    val imageFileId: UUID? = null,

    @field:PositiveOrZero
    val price: BigDecimal,

    @field:PositiveOrZero
    val flagshipPrice: BigDecimal? = null,

    val soldInBuffet: Boolean,
    val soldInCafe: Boolean,
    val soldInVip: Boolean,

    @field:Positive
    val order: Int? = null,

    @field:PositiveOrZero
    val tabletOrder: Int? = null,
    val isPackagingDeposit: Boolean? = null,

    @field:PositiveOrZero
    val stockQuantityThreshold: Int? = null,

    @field:ValidTaxRate
    val taxRate: Int? = null,

    @field:PositiveOrZero
    val discountAmount: BigDecimal? = null,

    @field:PositiveOrZero
    val discountPercentage: Int? = null,
    val productComposition: List<@Valid AdminCreateProductProductCompositionCommand>,
) {

    data class AdminCreateProductProductCompositionCommand(
        override val productComponentId: UUID? = null,
        override val productInProductId: UUID? = null,

        @field:PositiveOrZero
        override val productInProductPrice: BigDecimal? = null,

        @field:PositiveOrZero
        override val productInProductFlagshipPrice: BigDecimal? = null,

        @field:Positive
        override val quantity: BigDecimal,
    ) : CreateOrUpdateProductCompositionCommand()
}
