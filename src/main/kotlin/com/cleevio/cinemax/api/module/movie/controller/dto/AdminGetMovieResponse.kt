package com.cleevio.cinemax.api.module.movie.controller.dto

import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class AdminGetMovieResponse(
    val id: UUID,
    val rawTitle: String? = null,
    val originalTitle: String? = null,
    val title: String,
    val code: String,
    val disfilmCode: String? = null,
    val releaseYear: Int? = null,
    val premiereDate: LocalDate? = null,
    val duration: Int? = null,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val distributor: GetMovieDistributorResponse,
    val production: GetMovieDescriptorMssqlResponse? = null,
    val primaryGenre: GetMovieDescriptorMssqlResponse? = null,
    val secondaryGenre: GetMovieDescriptorMssqlResponse? = null,
    val rating: GetMovieDescriptorMssqlResponse? = null,
    val technology: GetMovieDescriptorMssqlResponse? = null,
    val language: GetMovieDescriptorMssqlResponse? = null,
    val tmsLanguage: GetMovieDescriptorMssqlResponse? = null,
    val jsos: List<GetMovieDescriptorMssqlResponse>,
) {

    data class GetMovieDistributorResponse(
        val id: UUID,
        val title: String,
    )

    data class GetMovieDescriptorMssqlResponse(
        val id: UUID,
        val title: String,
    )
}
