package com.cleevio.cinemax.api.module.basketitem.model

import com.cleevio.cinemax.api.common.constant.ExportableBoolean
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalTime

data class TicketBasketItemExportRecordModel(
    val basketPaidAtDate: LocalDate,
    val basketPaidAtTime: LocalTime,
    val receiptNumber: String?,
    val paymentPosConfigurationTitle: String?,
    val basketUpdatedBy: String,
    val auditoriumCode: String,
    val branchName: String,
    val distributorTitle: String,
    val screeningDate: LocalDate,
    val screeningTime: LocalTime,
    val movieRawTitle: String,
    val isUsed: ExportableBoolean,
    val paymentType: PaymentType,
    val seatRow: String,
    val seatNumber: String,
    val basePrice: BigDecimal,
    val surchargeVip: BigDecimal,
    val surchargePremium: BigDecimal,
    val surchargeImax: BigDecimal,
    val surchargeUltraX: BigDecimal,
    val surchargeDBox: BigDecimal,
    val serviceFeeVip: BigDecimal,
    val serviceFeePremium: BigDecimal,
    val serviceFeeImax: BigDecimal,
    val serviceFeeUltraX: BigDecimal,
    val serviceFeeGeneral: BigDecimal,
    val isCancelled: ExportableBoolean,
    val isPriceCategoryItemDiscounted: ExportableBoolean,
    val primaryTicketDiscountCode: String? = null,
    val secondaryTicketDiscountCode: String? = null,
    val discountCardType: DiscountCardType? = null,
    val discountCardTitle: String? = null,
    val discountCardCode: String? = null,
    val proDeduction: BigDecimal,
    val filmFondDeduction: BigDecimal,
    val distributorDeduction: BigDecimal,
    val taxAmount: BigDecimal,
    val includes3dGlasses: ExportableBoolean,
)
