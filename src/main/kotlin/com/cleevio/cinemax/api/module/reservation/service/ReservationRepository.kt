package com.cleevio.cinemax.api.module.reservation.service

import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.entity.Reservation
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface ReservationRepository : JpaRepository<Reservation, UUID> {

    fun findByIdAndDeletedAtIsNull(id: UUID): Reservation?

    fun findAllByScreeningIdAndSeatIdInAndStateIsInAndDeletedAtIsNull(
        screeningId: UUID,
        seatIds: Set<UUID>,
        reservationStates: Set<ReservationState>,
    ): List<Reservation>

    fun findAllByGroupReservationIdAndDeletedAtIsNull(groupReservationId: UUID): List<Reservation>

    fun findAllByIdInAndDeletedAtIsNull(ids: Set<UUID>): List<Reservation>

    fun findByOriginalId(originalId: Int): Reservation?

    fun findFirstByOriginalIdOrderByUpdatedAtDesc(originalId: Int): Reservation?
}
