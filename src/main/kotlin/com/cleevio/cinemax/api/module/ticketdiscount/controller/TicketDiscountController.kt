package com.cleevio.cinemax.api.module.ticketdiscount.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.common.dto.SimplePage
import com.cleevio.cinemax.api.common.service.query.SearchQueryDeprecated
import com.cleevio.cinemax.api.common.util.SimplePageMapper
import com.cleevio.cinemax.api.module.ticketdiscount.controller.dto.TicketDiscountSearchResponse
import com.cleevio.cinemax.api.module.ticketdiscount.controller.mapper.TicketDiscountResponseMapper
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountJooqFinderService
import com.cleevio.cinemax.api.module.ticketdiscount.service.query.TicketDiscountFilter
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.data.domain.PageRequest
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "POS Ticket Discounts")
@RestController
@RequestMapping("/pos-app/ticket-discounts")
class TicketDiscountController(
    private val ticketDiscountJooqFinderService: TicketDiscountJooqFinderService,
    private val ticketDiscountResponseMapper: TicketDiscountResponseMapper,
) {

    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
    fun searchTicketDiscounts(
        @RequestBody filter: TicketDiscountFilter,
        @RequestParam(required = false) basketId: UUID? = null,
    ): SimplePage<TicketDiscountSearchResponse> {
        val ticketDiscountPage = ticketDiscountJooqFinderService.search(SearchQueryDeprecated(NO_SORT_PAGE_REQUEST, filter))
        val ticketDiscountResponses = ticketDiscountResponseMapper.mapList(
            ticketDiscounts = ticketDiscountPage.content,
            basketId = basketId,
            includeVoucherOnly = filter.includeVoucherOnly
        )
        return SimplePageMapper.mapToSimplePage(ticketDiscountPage, ticketDiscountResponses)
    }
}

private val NO_SORT_PAGE_REQUEST = PageRequest.of(0, 2000)
