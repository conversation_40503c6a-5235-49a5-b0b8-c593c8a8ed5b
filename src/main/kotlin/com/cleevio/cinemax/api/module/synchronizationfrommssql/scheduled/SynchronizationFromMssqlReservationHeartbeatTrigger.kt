package com.cleevio.cinemax.api.module.synchronizationfrommssql.scheduled

import com.cleevio.cinemax.api.module.reservation.service.SynchronizationFromMssqlHeartbeatService
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class SynchronizationFromMssqlReservationHeartbeatTrigger(
    private val synchronizationFromMssqlHeartbeatService: SynchronizationFromMssqlHeartbeatService,
) {

    @Scheduled(cron = "\${features.synchronization-from-mssql.reservation-heartbeat}")
    fun checkLastReservationHeartbeat() {
        synchronizationFromMssqlHeartbeatService.checkLastReservationHeartbeat()
    }
}
