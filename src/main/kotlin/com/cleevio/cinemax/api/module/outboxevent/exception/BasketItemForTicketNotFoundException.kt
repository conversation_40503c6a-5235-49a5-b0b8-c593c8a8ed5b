package com.cleevio.cinemax.api.module.outboxevent.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
class BasketItemForTicketNotFoundException : ApiException(
    Module.OUTBOX_EVENT,
    OutboxEventErrorType.BASKET_ITEM_FOR_TICKET_NOT_FOUND
)
