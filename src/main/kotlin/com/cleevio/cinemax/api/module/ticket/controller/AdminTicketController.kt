package com.cleevio.cinemax.api.module.ticket.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.module.ticket.controller.dto.AdminMoveTicketsRequest
import com.cleevio.cinemax.api.module.ticket.service.TicketService
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController

@Tag(name = "Manager Tickets")
@RestController
@RequestMapping("/manager-app/tickets")
class AdminTicketController(
    private val ticketService: TicketService,
) {

    @PreAuthorize(Role.MANAGER)
    @PostMapping("/move", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun moveTickets(@RequestBody request: AdminMoveTicketsRequest) =
        ticketService.moveTickets(request.toCommand())
}
