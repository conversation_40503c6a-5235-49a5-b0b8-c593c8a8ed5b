package com.cleevio.cinemax.api.module.ticketprice.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import com.cleevio.cinemax.api.module.ticketprice.constant.TicketPriceErrorType
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
class SeatForTicketPriceNotFoundException : ApiException(
    Module.TICKET_PRICE,
    TicketPriceErrorType.SEAT_FOR_TICKET_PRICE_NOT_FOUND
)
