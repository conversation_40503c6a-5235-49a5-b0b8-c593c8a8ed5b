package com.cleevio.cinemax.api.module.dailyclosing.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.NOT_FOUND)
class DailyClosingNotFoundException : ApiException(
    Module.DAILY_CLOSING,
    DailyClosingErrorType.DAILY_CLOSING_NOT_FOUND
)
