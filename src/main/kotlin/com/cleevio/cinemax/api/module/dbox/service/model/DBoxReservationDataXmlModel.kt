package com.cleevio.cinemax.api.module.dbox.service.model

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement

@JacksonXmlRootElement(localName = "D-BOX")
data class DBoxReservationDataXmlModel(
    @field:JacksonXmlProperty(localName = "Result")
    val result: Result,

    @field:JacksonXmlProperty(localName = "Reservations")
    val reservations: Reservations,
)

data class Reservations(
    @field:JacksonXmlElementWrapper(useWrapping = false)
    @field:JacksonXmlProperty(localName = "Reservation")
    val reservationList: List<Reservation>,
)

data class Reservation(
    @field:JacksonXmlProperty(localName = "PresentationId", isAttribute = true)
    val presentationId: String,

    @field:JacksonXmlProperty(localName = "SeatId", isAttribute = true)
    val seatId: String,

    @field:JacksonXmlProperty(localName = "SeatRow", isAttribute = true)
    val seatRow: String,

    @field:JacksonXmlProperty(localName = "SeatCol", isAttribute = true)
    val seatCol: String,

    @field:JacksonXmlProperty(localName = "State", isAttribute = true)
    val state: String,
)
