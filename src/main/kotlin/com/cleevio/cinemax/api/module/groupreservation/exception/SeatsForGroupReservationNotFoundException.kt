package com.cleevio.cinemax.api.module.groupreservation.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus
import java.util.UUID

@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
class SeatsForGroupReservationNotFoundException(seatIds: Set<UUID>) : ApiException(
    module = Module.GROUP_RESERVATION,
    errorType = GroupReservationErrorType.SEATS_NOT_FOUND
) {

    override val message: String = seatIds.joinToString(", ")
}
