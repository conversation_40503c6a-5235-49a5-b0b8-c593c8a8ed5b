package com.cleevio.cinemax.api.module.auditorium.service

import com.cleevio.cinemax.api.common.util.buildGreaterThanDateTimeMssqlCondition
import com.cleevio.cinemax.api.module.auditoriumlayout.service.DEFAULT_AUDITORIUM_LAYOUT_CODE
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables.RKIN
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables.RUPRAVA
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Rkin
import org.jooq.DSLContext
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class AuditoriumMssqlFinderRepository(
    private val mssqlCinemaxDslContext: DSLContext,
) {

    fun findAll(): List<Rkin> {
        return mssqlCinemaxDslContext
            .selectFrom(RKIN)
            .fetchInto(Rkin::class.java)
    }

    fun findAllByUpdatedAtGt(updatedAt: LocalDateTime? = null): List<Rkin> {
        return mssqlCinemaxDslContext
            .selectFrom(RKIN)
            .where(buildGreaterThanDateTimeMssqlCondition(updatedAt, RKIN.ZCAS))
            .fetchInto(Rkin::class.java)
    }

    fun findByOriginalId(originalId: Int): Rkin? {
        return mssqlCinemaxDslContext
            .selectFrom(RKIN)
            .where(RKIN.RKINID.eq(originalId))
            .fetchOneInto(Rkin::class.java)
    }

    fun existsDefaultAuditoriumLayout(originalCode: String): Boolean {
        return mssqlCinemaxDslContext
            .fetchExists(
                mssqlCinemaxDslContext
                    .selectOne()
                    .from(RKIN)
                    .where(
                        RKIN.CSALU.`in`(
                            mssqlCinemaxDslContext.select(RUPRAVA.CSALU).from(RUPRAVA).where(
                                RUPRAVA.CSALU.eq(originalCode).and(RUPRAVA.CISLO.eq(DEFAULT_AUDITORIUM_LAYOUT_CODE))
                            )
                        )
                    )
            )
    }
}
