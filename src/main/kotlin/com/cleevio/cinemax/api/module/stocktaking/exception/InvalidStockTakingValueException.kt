package com.cleevio.cinemax.api.module.stocktaking.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.BAD_REQUEST)
class InvalidStockTakingValueException : ApiException(
    Module.STOCK_TAKING,
    StockTakingErrorType.INVALID_STOCK_QUANTITY_VALUE
)
