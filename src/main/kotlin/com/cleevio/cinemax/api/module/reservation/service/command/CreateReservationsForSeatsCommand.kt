package com.cleevio.cinemax.api.module.reservation.service.command

import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import jakarta.validation.constraints.NotEmpty
import java.util.UUID

data class CreateReservationsForSeatsCommand(
    val screeningId: UUID,

    @field:NotEmpty
    val seatIdToReservationStateMap: Map<UUID, ReservationState>,
    val groupReservationId: UUID? = null,
)
