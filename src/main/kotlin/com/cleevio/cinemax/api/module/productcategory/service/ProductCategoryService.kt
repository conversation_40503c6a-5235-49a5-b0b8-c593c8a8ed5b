package com.cleevio.cinemax.api.module.productcategory.service

import com.cleevio.cinemax.api.common.constant.PRODUCT_CATEGORY
import com.cleevio.cinemax.api.common.service.CodeGenerator
import com.cleevio.cinemax.api.common.util.ifFalse
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.file.exception.FileNotFoundException
import com.cleevio.cinemax.api.module.file.service.FileJpaFinderService
import com.cleevio.cinemax.api.module.product.service.ProductJpaFinderService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryLockValues.CREATE_OR_UPDATE_PRODUCT_CATEGORY
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryLockValues.DELETE_PRODUCT_CATEGORY
import com.cleevio.cinemax.api.module.productcategory.entity.ProductCategory
import com.cleevio.cinemax.api.module.productcategory.event.ProductCategoryCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.productcategory.event.toMessagingDeleteEvent
import com.cleevio.cinemax.api.module.productcategory.event.toMessagingEvent
import com.cleevio.cinemax.api.module.productcategory.exception.ProductForProductCategoryExistsException
import com.cleevio.cinemax.api.module.productcategory.service.command.CreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.module.productcategory.service.command.DeleteProductCategoryCommand
import com.cleevio.cinemax.api.module.productcategory.service.command.MessagingCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.module.productcategory.service.command.MessagingDeleteProductCategoryCommand
import com.cleevio.cinemax.api.module.productcategory.service.command.UpdateProductCategoryOriginalIdCommand
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import jakarta.validation.Valid
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.util.Optional
import java.util.UUID
import kotlin.jvm.optionals.getOrNull

@Service
@Validated
class ProductCategoryService(
    private val fileJpaFinderService: FileJpaFinderService,
    private val productCategoryRepository: ProductCategoryRepository,
    private val productCategoryJpaFinderService: ProductCategoryJpaFinderService,
    private val productJpaFinderService: ProductJpaFinderService,
    private val codeGenerator: CodeGenerator,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    private val log = logger()

    @Transactional
    @Lock(PRODUCT_CATEGORY, CREATE_OR_UPDATE_PRODUCT_CATEGORY)
    fun syncCreateOrUpdateProductCategory(
        @Valid
        @LockFieldParameter("code")
        command: CreateOrUpdateProductCategoryCommand,
    ) {
        requireNotNull(command.originalId) { "Attribute 'originalId' must not be null." }
        requireNotNull(command.code) { "Attribute 'code' must not be null." }

        val productCategory = productCategoryRepository.findByCode(command.code)
        if (productCategory != null && productCategory.isDeleted()) {
            log.warn("Product category with code ${productCategory.code} has been already deleted. Skipping.")
            return
        }

        internalCreateOrUpdateProductCategory(
            productCategory = productCategory,
            command = command
        )
    }

    @Transactional
    @Lock(PRODUCT_CATEGORY, CREATE_OR_UPDATE_PRODUCT_CATEGORY)
    fun adminCreateOrUpdateProductCategory(
        @Valid
        @LockFieldParameter("id")
        command: CreateOrUpdateProductCategoryCommand,
    ) {
        internalCreateOrUpdateProductCategory(
            productCategory = command.id?.let { productCategoryJpaFinderService.getNonDeletedById(it) },
            command = command
        ).also {
            applicationEventPublisher.publishEvent(ProductCategoryCreatedOrUpdatedEvent(it.id))
            applicationEventPublisher.publishEvent(
                it.toMessagingEvent(
                    imageFileOriginalName = it.imageFileId?.let { fileId ->
                        Optional.ofNullable(fileJpaFinderService.getById(fileId).originalName)
                    }
                )
            )
        }
    }

    @Transactional
    @Lock(PRODUCT_CATEGORY, CREATE_OR_UPDATE_PRODUCT_CATEGORY)
    fun updateProductCategoryOriginalId(
        @Valid
        @LockFieldParameter("productCategoryId")
        command: UpdateProductCategoryOriginalIdCommand,
    ) {
        productCategoryJpaFinderService.getNonDeletedById(command.productCategoryId).also {
            productCategoryRepository.save(
                it.apply {
                    originalId = command.originalId
                }
            )
        }
    }

    @Transactional
    @Lock(PRODUCT_CATEGORY, DELETE_PRODUCT_CATEGORY)
    fun deleteProductCategory(
        @Valid
        @LockFieldParameter("productCategoryId")
        command: DeleteProductCategoryCommand,
    ) {
        productCategoryJpaFinderService.getNonDeletedById(command.productCategoryId).let {
            if (productJpaFinderService.existsNonDeletedByProductCategoryId(command.productCategoryId)) {
                throw ProductForProductCategoryExistsException()
            }
            productCategoryRepository.save(
                it.apply { markDeleted() }
            )

            applicationEventPublisher.publishEvent(it.toMessagingDeleteEvent())
        }
    }

    @Transactional
    @Lock(PRODUCT_CATEGORY, CREATE_OR_UPDATE_PRODUCT_CATEGORY)
    fun messagingCreateOrUpdateProductCategory(
        @Valid
        @LockFieldParameter("code")
        command: MessagingCreateOrUpdateProductCategoryCommand,
    ) {
        val imageFile = command.imageFileOriginalName?.getOrNull()?.let {
            fileJpaFinderService.findByOriginalName(it)
        }
        val productCategory = productCategoryJpaFinderService.findNonDeletedByCode(command.code)?.let {
            productCategoryRepository.save(
                mapToExistingMessagingProductCategory(
                    productCategory = it,
                    command = command,
                    fileId = imageFile?.id
                )
            )
        } ?: run {
            productCategoryRepository.save(
                mapToNewMessagingProductCategory(
                    command = command,
                    fileId = imageFile?.id
                )
            )
        }

        applicationEventPublisher.publishEvent(ProductCategoryCreatedOrUpdatedEvent(productCategory.id))
    }

    @Transactional
    @Lock(PRODUCT_CATEGORY, DELETE_PRODUCT_CATEGORY)
    fun messagingDeleteProductCategory(
        @Valid
        @LockFieldParameter("code")
        command: MessagingDeleteProductCategoryCommand,
    ) {
        productCategoryJpaFinderService.getNonDeletedByCode(command.code).let {
            productCategoryRepository.save(it.apply { markDeleted() })
        }
    }

    private fun internalCreateOrUpdateProductCategory(
        productCategory: ProductCategory?,
        command: CreateOrUpdateProductCategoryCommand,
    ): ProductCategory {
        command.imageFileId?.getOrNull()?.let {
            fileJpaFinderService.existsById(it).ifFalse { throw FileNotFoundException() }
        }

        productCategory?.let {
            return productCategoryRepository.save(mapToExistingProductCategory(it, command))
        } ?: run {
            return productCategoryRepository.save(mapToNewProductCategory(command))
        }
    }

    private fun mapToExistingProductCategory(
        productCategory: ProductCategory,
        command: CreateOrUpdateProductCategoryCommand,
    ) = productCategory.apply {
        title = command.title
        type = command.type
        command.order?.let { order = it.getOrNull() }
        taxRate = command.taxRate
        hexColorCode = command.hexColorCode
        command.imageFileId?.let { imageFileId = it.getOrNull() }
    }

    private fun mapToNewProductCategory(command: CreateOrUpdateProductCategoryCommand) = ProductCategory(
        id = command.id ?: UUID.randomUUID(),
        originalId = command.originalId,
        code = command.code ?: codeGenerator.generateCode(
            ProductCategory::class.java,
            productCategoryRepository::existsByCode
        ),
        title = command.title,
        type = command.type,
        order = command.order?.getOrNull(),
        taxRate = command.taxRate,
        hexColorCode = command.hexColorCode,
        imageFileId = command.imageFileId?.getOrNull()
    )

    private fun mapToNewMessagingProductCategory(
        command: MessagingCreateOrUpdateProductCategoryCommand,
        fileId: UUID?,
    ) = ProductCategory(
        originalId = null,
        code = command.code,
        title = command.title,
        type = command.type,
        order = command.order?.getOrNull(),
        taxRate = command.taxRate,
        hexColorCode = command.hexColorCode,
        imageFileId = fileId
    )

    private fun mapToExistingMessagingProductCategory(
        productCategory: ProductCategory,
        command: MessagingCreateOrUpdateProductCategoryCommand,
        fileId: UUID?,
    ) = productCategory.apply {
        code = command.code
        title = command.title
        type = command.type
        command.order?.let { order = it.getOrNull() }
        taxRate = command.taxRate
        hexColorCode = command.hexColorCode
        imageFileId = fileId
    }
}
