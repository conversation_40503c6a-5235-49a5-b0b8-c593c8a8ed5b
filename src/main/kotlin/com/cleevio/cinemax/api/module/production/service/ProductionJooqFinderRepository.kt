package com.cleevio.cinemax.api.module.production.service

import com.cleevio.cinemax.api.common.service.JooqFinderRepository
import com.cleevio.cinemax.api.module.production.entity.Production
import com.cleevio.cinemax.psql.Tables.PRODUCTION
import org.jooq.DSLContext
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
class ProductionJooqFinderRepository(
    private val psqlDslContext: DSLContext,
) : JooqFinderRepository<Production> {

    override fun findAll(): List<Production> {
        return psqlDslContext
            .selectFrom(PRODUCTION)
            .fetchInto(Production::class.java)
    }

    override fun findById(id: UUID): Production? {
        return psqlDslContext
            .selectFrom(PRODUCTION)
            .where(PRODUCTION.ID.eq(id))
            .fetchOneInto(Production::class.java)
    }
}
