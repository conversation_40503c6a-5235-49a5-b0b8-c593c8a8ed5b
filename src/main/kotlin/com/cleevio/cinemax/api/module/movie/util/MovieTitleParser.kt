package com.cleevio.cinemax.api.module.movie.util

import com.cleevio.cinemax.api.module.movie.service.model.ParsedTitleResult
import com.cleevio.cinemax.api.module.screening.constant.MovieFormat
import com.cleevio.cinemax.api.module.screening.constant.MovieLanguage
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology

fun parseMovieTitle(title: String): ParsedTitleResult {
    val technology = findElementAndIndex(title, MovieTechnology.getMssqlValues())
    val language = findElementAndIndex(title, MovieLanguage.getMssqlValues())
    val format = findElementAndIndex(title, MovieFormat.getMssqlValues())

    val indices = setOf(technology, language, format).mapNotNull { it.second }
    if (indices.isEmpty()) return ParsedTitleResult(title = title)
    val lowestIndex = indices.minBy { it }

    return ParsedTitleResult(
        title = title.substring(0, lowestIndex).replace("(", "").trimEnd(),
        format = format.first?.let { MovieFormat.fromMssqlValue(it) } ?: MovieFormat.FORMAT_2D,
        technology = technology.first?.let { MovieTechnology.fromMssqlValue(it) },
        language = language.first?.let { MovieLanguage.fromMssqlValue(it) }
    )
}

private fun findElementAndIndex(title: String, enumValues: Set<String>): Pair<String?, Int?> {
    val valueAsWord = enumValues.find { title.contains(" $it ") }?.let { title.indexOf(" $it ") }
    val valueInParentheses = enumValues.find { title.contains("($it)") }?.let { title.indexOf("($it)") }
    val valueEndsWith = enumValues.find { title.endsWith(it) }?.let { title.indexOf(it) }

    val enumValue = enumValues.find { title.contains(" $it ") || title.contains("($it)") || title.endsWith(it) }
    return Pair(enumValue, setOf(valueAsWord, valueInParentheses, valueEndsWith).find { it != null })
}
