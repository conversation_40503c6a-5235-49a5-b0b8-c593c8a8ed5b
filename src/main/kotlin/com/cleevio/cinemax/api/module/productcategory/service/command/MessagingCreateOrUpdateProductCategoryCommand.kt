package com.cleevio.cinemax.api.module.productcategory.service.command

import com.cleevio.cinemax.api.common.service.CreateOrUpdateCommand
import com.cleevio.cinemax.api.common.validation.ValidTaxRate
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import jakarta.validation.constraints.NotBlank
import java.util.Optional

data class MessagingCreateOrUpdateProductCategoryCommand(

    @field:NotBlank
    val code: String,

    @field:NotBlank
    val title: String,
    val type: ProductCategoryType,
    val order: Optional<Int>? = null,

    @field:ValidTaxRate
    val taxRate: Int,

    @field:NotBlank
    val hexColorCode: String,
    val imageFileOriginalName: Optional<String>? = null,
) : CreateOrUpdateCommand()
