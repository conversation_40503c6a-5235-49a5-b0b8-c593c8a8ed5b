package com.cleevio.cinemax.api.module.basket.service

import com.cleevio.cinemax.api.module.basket.service.command.CreateBasketItemInput
import com.cleevio.cinemax.api.module.basket.service.command.CreateProductInput
import com.cleevio.cinemax.api.module.basket.service.command.CreateVipBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.CreateVipBasketItemInput
import com.cleevio.cinemax.api.module.basket.service.command.InitBasketForTableCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateBasketItemRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateProductRequest
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardJpaFinderService
import com.cleevio.cinemax.api.module.product.exception.ProductNotFoundException
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class CreateVipBasketService(
    private val basketService: BasketService,
    private val discountCardJpaFinderService: DiscountCardJpaFinderService,
) {

    operator fun invoke(@Valid command: CreateVipBasketCommand) {

        val discountBasketItem = command.discountCardId?.let {
            discountCardJpaFinderService.findById(command.discountCardId)?.run {
                CreateBasketItemInput(
                    type = BasketItemType.PRODUCT_DISCOUNT,
                    quantity = 1,
                    product = CreateProductInput(
                        // TODO Tome toto mam dobre moc som to nepochopila z tasku
                        productId = this.productDiscountId!!,
                        productIsolatedWithId = null,
                        discountCardId = this.id,
                    ),
                    ticket = null
                )
            }
        }

        with(command) {
            // mapped items from command and discount basket item only if exists
            val listOfBasketItems = basketItems.map { it.toCreateBasketItemInput() } + listOfNotNull(discountBasketItem)

            basketService.initBasketForTable(
                InitBasketForTableCommand(
                    tableId = tableId,
                    preferredPaymentType = preferredPaymentType,
                    initiatedInNewPos = false,
                    items = listOfBasketItems
                )
            )
        }
    }
}

fun CreateVipBasketItemInput.toCreateBasketItemInput() = CreateBasketItemInput(
    type = BasketItemType.PRODUCT,
    quantity = quantity,
    product = CreateProductInput(
        productId = productId,
        productIsolatedWithId = null,
        discountCardId = null,
    ),
    ticket = null
)
