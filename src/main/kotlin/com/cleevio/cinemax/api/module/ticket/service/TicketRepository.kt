package com.cleevio.cinemax.api.module.ticket.service

import com.cleevio.cinemax.api.module.ticket.entity.Ticket
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface TicketRepository : JpaRepository<Ticket, UUID> {

    fun findByIdAndDeletedAtIsNull(id: UUID): Ticket?

    fun findByOriginalIdAndDeletedAtIsNull(originalId: Int): Ticket?

    fun existsByOriginalIdAndDeletedAtIsNull(originalId: Int): Boolean

    fun existsByReceiptNumberAndDeletedAtIsNull(receiptNumber: String): Boolean

    fun findAllByIdInAndDeletedAtIsNull(ids: Set<UUID>): List<Ticket>

    @Query(
        """
            SELECT count(t.id) > 0
            FROM ticket t
            JOIN basket_item bi ON bi.ticket_id = t.id
            JOIN basket b ON bi.basket_id = b.id
            JOIN reservation r ON r.id = t.reservation_id
            WHERE bi.deleted_at IS NULL
                AND b.deleted_at IS NULL
                AND t.deleted_at IS NULL
                AND r.deleted_at IS NULL
                AND b.state IN ('PAID', 'PAID_ONLINE')
                AND r.group_reservation_id = :groupReservationId
        """,
        nativeQuery = true
    )
    fun existsNonDeletedPaidTicketByGroupReservationId(groupReservationId: UUID): Boolean

    @Modifying
    @Query(
        """
            UPDATE ticket SET is_used = 'true' WHERE original_id IN (:originalIds)
        """,
        nativeQuery = true
    )
    fun updateTicketsUsed(originalIds: Set<Int>)
}
