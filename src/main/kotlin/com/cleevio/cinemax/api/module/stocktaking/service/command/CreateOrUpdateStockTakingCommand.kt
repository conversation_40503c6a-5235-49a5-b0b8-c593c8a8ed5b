package com.cleevio.cinemax.api.module.stocktaking.service.command

import com.cleevio.cinemax.api.common.service.CreateOrUpdateCommand
import jakarta.validation.constraints.PositiveOrZero
import java.math.BigDecimal
import java.util.UUID

data class CreateOrUpdateStockTakingCommand(
    override val id: UUID? = null,
    val originalId: Int,
    val productComponentId: UUID,

    @field:PositiveOrZero
    val stockQuantity: BigDecimal,

    @field:PositiveOrZero
    val stockQuantityActual: BigDecimal,
    val stockQuantityDifference: BigDecimal,

    @field:PositiveOrZero
    val purchasePrice: BigDecimal,
    val purchasePriceDifference: BigDecimal,
) : CreateOrUpdateCommand()
