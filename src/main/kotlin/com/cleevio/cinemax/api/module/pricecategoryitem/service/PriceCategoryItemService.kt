package com.cleevio.cinemax.api.module.pricecategoryitem.service

import com.cleevio.cinemax.api.common.constant.PRICE_CATEGORY_ITEM
import com.cleevio.cinemax.api.common.util.ifFalse
import com.cleevio.cinemax.api.module.pricecategory.exception.PriceCategoryNotFoundException
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryJpaFinderService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemLockValues.CREATE_OR_UPDATE_PRICE_CATEGORY_ITEM
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemLockValues.DELETE_AND_CREATE_PRICE_CATEGORY_ITEMS
import com.cleevio.cinemax.api.module.pricecategoryitem.entity.PriceCategoryItem
import com.cleevio.cinemax.api.module.pricecategoryitem.service.command.DeleteAndCreatePriceCategoryItemsCommand
import com.cleevio.cinemax.api.module.pricecategoryitem.service.command.SyncCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import com.cleevio.library.lockinghandler.service.LockFieldParameters
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class PriceCategoryItemService(
    private val priceCategoryItemJpaFinderService: PriceCategoryItemJpaFinderService,
    private val priceCategoryItemRepository: PriceCategoryItemRepository,
    private val priceCategoryJpaFinderService: PriceCategoryJpaFinderService,
) {

    @Transactional
    @Lock(PRICE_CATEGORY_ITEM, CREATE_OR_UPDATE_PRICE_CATEGORY_ITEM)
    fun createOrUpdatePriceCategoryItem(
        @Valid
        @LockFieldParameters([LockFieldParameter("priceCategoryId"), LockFieldParameter("number")])
        command: SyncCreateOrUpdatePriceCategoryItemCommand,
    ): PriceCategoryItem {
        priceCategoryJpaFinderService.existsNonDeletedById(command.priceCategoryId).ifFalse {
            throw PriceCategoryNotFoundException()
        }

        val priceCategoryItem = priceCategoryItemJpaFinderService.findByPriceCategoryIdAndNumber(
            priceCategoryId = command.priceCategoryId,
            number = command.number
        )
        return priceCategoryItem?.let {
            priceCategoryItemRepository.save(mapToExistingPriceCategoryItem(it, command))
        } ?: run {
            priceCategoryItemRepository.save(mapToNewPriceCategoryItem(command))
        }
    }

    @Transactional
    @Lock(PRICE_CATEGORY_ITEM, DELETE_AND_CREATE_PRICE_CATEGORY_ITEMS)
    fun deleteAndCreatePriceCategoryItems(
        @Valid
        @LockFieldParameter("priceCategoryId")
        command: DeleteAndCreatePriceCategoryItemsCommand,
    ) {
        priceCategoryJpaFinderService.existsNonDeletedById(command.priceCategoryId).ifFalse {
            throw PriceCategoryNotFoundException()
        }

        priceCategoryItemJpaFinderService.findAllIdsByPriceCategoryId(command.priceCategoryId).let {
            priceCategoryItemRepository.deleteAllById(it)
            priceCategoryItemRepository.flush()
        }

        command.items.map {
            PriceCategoryItem(
                priceCategoryId = command.priceCategoryId,
                number = it.number,
                title = it.title,
                price = it.price,
                discounted = it.discounted
            )
        }.let { priceCategoryItemRepository.saveAll(it) }
    }

    private fun mapToExistingPriceCategoryItem(
        priceCategoryItem: PriceCategoryItem,
        command: SyncCreateOrUpdatePriceCategoryItemCommand,
    ) = priceCategoryItem.apply {
        priceCategoryId = command.priceCategoryId
        number = command.number
        title = command.title
        price = command.price
    }

    private fun mapToNewPriceCategoryItem(command: SyncCreateOrUpdatePriceCategoryItemCommand) = PriceCategoryItem(
        id = command.id ?: UUID.randomUUID(),
        priceCategoryId = command.priceCategoryId,
        number = command.number,
        title = command.title,
        price = command.price,
        discounted = command.discounted
    )
}
