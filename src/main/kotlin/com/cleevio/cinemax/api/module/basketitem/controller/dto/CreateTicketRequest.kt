package com.cleevio.cinemax.api.module.basketitem.controller.dto

import com.cleevio.cinemax.api.module.basket.service.command.CreateTicketInput
import java.util.UUID

data class CreateTicketRequest(
    val screeningId: UUID,
    val ticketPrice: CreateTicketPriceRequest,
    val reservation: CreateReservationRequest,
    val primaryDiscount: CreateTicketDiscountRequest? = null,
    val secondaryDiscount: CreateTicketDiscountRequest? = null,
    val isGroupTicket: Boolean = false,
) {
    fun toInput() = CreateTicketInput(
        screeningId = screeningId,
        ticketPrice = ticketPrice.toInput(),
        reservation = reservation.toInput(),
        primaryDiscount = primaryDiscount?.toInput(),
        secondaryDiscount = secondaryDiscount?.toInput(),
        isGroupTicket = isGroupTicket
    )
}
