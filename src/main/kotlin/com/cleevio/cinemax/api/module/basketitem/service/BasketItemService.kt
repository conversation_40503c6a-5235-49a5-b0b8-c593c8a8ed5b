package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.common.constant.BASKET_ITEM
import com.cleevio.cinemax.api.common.util.ifFalse
import com.cleevio.cinemax.api.common.util.ifTrue
import com.cleevio.cinemax.api.common.util.isGreaterThanOrEqual
import com.cleevio.cinemax.api.common.util.isWholeNumber
import com.cleevio.cinemax.api.common.util.letThree
import com.cleevio.cinemax.api.common.util.letTwo
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basket.event.BasketItemModifiedEvent
import com.cleevio.cinemax.api.module.basket.event.BasketModifiedInPaymentInProgressStateEvent
import com.cleevio.cinemax.api.module.basket.event.MessagingBasketItemWithDiscountCardCreatedEvent
import com.cleevio.cinemax.api.module.basket.event.MssqlBasketItemWithDiscountCardCreatedEvent
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemAddedToBasketEvent
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemDeletedFromBasketEvent
import com.cleevio.cinemax.api.module.basket.event.ProductBasketItemQuantityModifiedEvent
import com.cleevio.cinemax.api.module.basket.service.BasketDiscountCardService
import com.cleevio.cinemax.api.module.basket.service.BasketFinderService
import com.cleevio.cinemax.api.module.basket.service.command.CreateMssqlTicketBasketItemCommand
import com.cleevio.cinemax.api.module.basket.service.command.DeleteDiscountCardUsagesWhenDeletingBasketItemCommand
import com.cleevio.cinemax.api.module.basket.service.command.DeleteDiscountCardUsagesWhenPatchingBasketItemCommand
import com.cleevio.cinemax.api.module.basket.service.command.MessagingCreateBasketItemCommand
import com.cleevio.cinemax.api.module.basket.service.command.MessagingCreateCancelledBasketItemCommand
import com.cleevio.cinemax.api.module.basket.service.command.RecalculateBasketProductDiscountPricesCommand
import com.cleevio.cinemax.api.module.basket.util.validateModifiableBasketState
import com.cleevio.cinemax.api.module.basket.util.validateOpenOnlineBasketState
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemLockValues.CREATE_UPDATE_DELETE_BASKET_ITEM
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.event.BasketItemWithDiscountCardUpdatedEvent
import com.cleevio.cinemax.api.module.basketitem.event.BasketItemWithPackagingDepositUpdatedEvent
import com.cleevio.cinemax.api.module.basketitem.event.ProductWithPackagingDepositFoundEvent
import com.cleevio.cinemax.api.module.basketitem.event.TableBasketItemDeletedEvent
import com.cleevio.cinemax.api.module.basketitem.event.TableBasketItemUpdatedEvent
import com.cleevio.cinemax.api.module.basketitem.event.toMessagingCancelEvent
import com.cleevio.cinemax.api.module.basketitem.exception.BasketItemIsAlreadyCancelledException
import com.cleevio.cinemax.api.module.basketitem.exception.BasketItemNotFoundException
import com.cleevio.cinemax.api.module.basketitem.exception.InvalidBasketItemCommandException
import com.cleevio.cinemax.api.module.basketitem.exception.InvalidBasketItemStateException
import com.cleevio.cinemax.api.module.basketitem.exception.ProductBasketItemWithInsufficientStockQuantityException
import com.cleevio.cinemax.api.module.basketitem.exception.ProductDiscountAlreadyAppliedException
import com.cleevio.cinemax.api.module.basketitem.exception.SecondDiscountCardDiscountAppliedOnTicketBasketItemException
import com.cleevio.cinemax.api.module.basketitem.exception.TicketDiscountApplicableToCountExceeded
import com.cleevio.cinemax.api.module.basketitem.exception.TicketDiscountCardApplicableToBasketLimitExceeded
import com.cleevio.cinemax.api.module.basketitem.exception.TicketDiscountCardApplicableToScreeningLimitExceeded
import com.cleevio.cinemax.api.module.basketitem.exception.TicketDiscountCardScreeningsPerDayLimitExceededException
import com.cleevio.cinemax.api.module.basketitem.exception.TicketDiscountVoucherApplicableToBasketLimitExceeded
import com.cleevio.cinemax.api.module.basketitem.exception.TwoDiscountCardsAppliedOnTicketBasketItemException
import com.cleevio.cinemax.api.module.basketitem.service.command.CancelBasketItemsCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateBasketItemsFromGroupReservationCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateOnlineTicketBasketItemsCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreatePackagingDepositBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateProductSalesBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateTicketBasketItemsCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.DeleteBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.DeleteBasketItemsCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.PatchBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.UpdateBasketItemReceiptNumberCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.UpdatePackagingDepositBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.isDeleteAction
import com.cleevio.cinemax.api.module.basketitem.util.validateCreateBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.util.validateProductDiscountBasketItemQuantity
import com.cleevio.cinemax.api.module.basketitem.util.validateTicketBasketItemQuantity
import com.cleevio.cinemax.api.module.branch.service.BranchJpaFinderService
import com.cleevio.cinemax.api.module.discountcard.entity.DiscountCard
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardJooqFinderService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageRepository
import com.cleevio.cinemax.api.module.groupreservation.exception.GroupReservationNotFoundException
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationJpaFinderService
import com.cleevio.cinemax.api.module.outboxevent.event.BasketItemsCancelledEvent
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.service.ProductDiscountPriceService
import com.cleevio.cinemax.api.module.product.service.ProductJooqFinderService
import com.cleevio.cinemax.api.module.product.service.ProductJpaFinderService
import com.cleevio.cinemax.api.module.product.service.command.DetermineProductDiscountPriceCommand
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJooqFinderService
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.exception.InvalidReservationStateException
import com.cleevio.cinemax.api.module.reservation.service.ReservationJpaFinderService
import com.cleevio.cinemax.api.module.screening.service.ScreeningJpaFinderService
import com.cleevio.cinemax.api.module.seat.service.SeatJpaFinderService
import com.cleevio.cinemax.api.module.table.event.TableBasketItemModifiedEvent
import com.cleevio.cinemax.api.module.ticket.service.TicketJooqFinderService
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketService
import com.cleevio.cinemax.api.module.ticket.service.command.CreateMessagingTicketCommand
import com.cleevio.cinemax.api.module.ticket.service.command.CreateMssqlTicketCommand
import com.cleevio.cinemax.api.module.ticket.service.command.CreateTicketCommand
import com.cleevio.cinemax.api.module.ticket.service.command.DeleteTicketCommand
import com.cleevio.cinemax.api.module.ticket.service.command.DeleteTicketsCommand
import com.cleevio.cinemax.api.module.ticket.service.command.UpdateTicketCommand
import com.cleevio.cinemax.api.module.ticketdiscount.entity.TicketDiscount
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountJooqFinderService
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import com.cleevio.library.lockinghandler.service.LockService
import jakarta.validation.Valid
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.math.BigDecimal
import java.util.UUID

@Service
@Validated
class BasketItemService(
    private val basketItemRepository: BasketItemRepository,
    private val basketItemJpaFinderService: BasketItemJpaFinderService,
    private val basketFinderService: BasketFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val productJooqFinderService: ProductJooqFinderService,
    private val productJpaFinderService: ProductJpaFinderService,
    private val productCategoryJooqFinderService: ProductCategoryJooqFinderService,
    private val ticketService: TicketService,
    private val ticketJooqFinderService: TicketJooqFinderService,
    private val ticketDiscountJooqFinderService: TicketDiscountJooqFinderService,
    private val basketDiscountCardService: BasketDiscountCardService,
    private val discountCardJooqFinderService: DiscountCardJooqFinderService,
    private val screeningJpaFinderService: ScreeningJpaFinderService,
    private val productDiscountPriceService: ProductDiscountPriceService,
    private val reservationJpaFinderService: ReservationJpaFinderService,
    private val groupReservationJpaFinderService: GroupReservationJpaFinderService,
    private val seatJpaFinderService: SeatJpaFinderService,
    private val branchFinderService: BranchJpaFinderService,
    private val ticketRepository: TicketRepository,
    private val ticketPriceRepository: TicketPriceRepository,
    private val discountCardUsageRepository: DiscountCardUsageRepository,

    @Suppress("SpringJavaInjectionPointsAutowiringInspection")
    private val lockService: LockService,
) {
    private val log = logger()

    @Transactional
    @Lock(BASKET_ITEM, CREATE_UPDATE_DELETE_BASKET_ITEM)
    fun createBasketItem(
        @Valid
        @LockFieldParameter("basketId")
        command: CreateBasketItemCommand,
    ): BasketItem {
        val basket = basketFinderService.getNonDeletedById(command.basketId).also {
            validateModifiableBasketState(it)
            validateCreateBasketItemCommand(command)
        }

        val createdItem = when (command.type) {
            BasketItemType.TICKET -> letThree(
                command.reservationSeatId,
                command.priceCategoryItemNumber,
                command.screeningId
            ) { seatId, priceCategoryItemNumber, screeningId ->
                createTicketBasketItem(
                    command = command,
                    seatId = seatId,
                    screeningId = screeningId,
                    priceCategoryItemNumber = priceCategoryItemNumber
                )
            }

            BasketItemType.PRODUCT -> command.productId?.let {
                createProductBasketItem(
                    command = command,
                    productId = it,
                    productIsolatedWithId = command.productIsolatedWithId
                )
            }

            BasketItemType.PRODUCT_DISCOUNT -> command.productId?.let {
                createProductDiscountBasketItem(
                    command = command,
                    productId = it
                )
            }
        } ?: throw InvalidBasketItemCommandException()

        determineNewlyAppliedDiscountCardId(
            primaryTicketDiscountCardId = command.primaryTicketDiscountCardId,
            secondaryTicketDiscountCardId = command.secondaryTicketDiscountCardId,
            productDiscountCardId = command.productDiscountCardId,
            basketItemType = createdItem.type
        )?.let {
            applicationEventPublisher.publishEvent(
                BasketItemWithDiscountCardUpdatedEvent(
                    discountCardId = it,
                    basketId = basket.id,
                    basketItemId = createdItem.id
                )
            )
        }

        publishBasketItemModifiedEvents(basket = basket)
        return basketItemRepository.save(createdItem)
    }

    @Transactional
    @Lock(BASKET_ITEM, CREATE_UPDATE_DELETE_BASKET_ITEM)
    fun createMssqlTicketBasketItem(
        @Valid
        @LockFieldParameter("basketId")
        command: CreateMssqlTicketBasketItemCommand,
    ): BasketItem {
        val ticketModel = ticketService.createMssqlTicket(
            CreateMssqlTicketCommand(
                screeningId = command.screeningId,
                seatId = command.seatId,
                reservationId = command.reservationId,
                originalId = command.originalId,
                priceCategoryItemNumber = command.priceCategoryItemNumber,
                ticketDiscountPrimaryId = command.ticketDiscountPrimaryId,
                ticketDiscountSecondaryId = command.ticketDiscountSecondaryId,
                receiptNumber = command.receiptNumber,
                isUsed = command.isUsed,
                includes3dGlasses = command.includes3dGlasses,
                basePrice = command.basePrice,
                basePriceBeforeDiscount = command.basePriceBeforeDiscount,
                seatSurcharge = command.seatSurcharge,
                seatSurchargeType = command.seatSurchargeType,
                auditoriumSurcharge = command.auditoriumSurcharge,
                auditoriumSurchargeType = command.auditoriumSurchargeType,
                seatServiceFee = command.seatServiceFee,
                seatServiceFeeType = command.seatServiceFeeType,
                auditoriumServiceFee = command.auditoriumServiceFee,
                auditoriumServiceFeeType = command.auditoriumServiceFeeType,
                serviceFeeGeneral = command.serviceFeeGeneral,
                freeTicket = command.freeTicket,
                totalPrice = command.totalPrice
            )
        )

        return basketItemRepository.save(
            BasketItem(
                basketId = command.basketId,
                ticketId = ticketModel.ticket.id,
                branchId = command.branchId,
                type = BasketItemType.TICKET,
                price = ticketModel.ticketPrice.totalPrice,
                quantity = 1,
                isCancelled = false, // possible future cancellation depends on Ticket/TicketSale MSSQL sync service
                originalId = command.originalId
            )
        ).also { basketItem ->
            command.discountCardId?.let {
                applicationEventPublisher.publishEvent(
                    MssqlBasketItemWithDiscountCardCreatedEvent(
                        discountCardId = it,
                        screeningId = command.screeningId,
                        basketId = basketItem.basketId,
                        ticketBasketItemId = basketItem.id
                    )
                )
            }
        }
    }

    @Transactional
    @Lock(BASKET_ITEM, CREATE_UPDATE_DELETE_BASKET_ITEM)
    fun createProductSalesBasketItem(
        @Valid
        @LockFieldParameter("basketId")
        command: CreateProductSalesBasketItemCommand,
    ): BasketItem {
        return basketItemRepository.saveAndFlush(
            BasketItem(
                basketId = command.basketId,
                productId = command.productId,
                type = command.type,
                price = command.price,
                quantity = command.quantity,
                productReceiptNumber = command.receiptNumber,
                isCancelled = command.isCancelled,
                branchId = command.branchId
            )
        )
    }

    @Transactional
    @Lock(BASKET_ITEM, CREATE_UPDATE_DELETE_BASKET_ITEM)
    fun messagingCreateBasketItem(
        @Valid
        @LockFieldParameter("basketId")
        command: MessagingCreateBasketItemCommand,
    ) {
        if (basketItemJpaFinderService.existsById(command.id)) {
            log.warn("Attempt to create duplicate messaging BasketItem ${command.id}. Skipping.")
            return
        }

        val branch = branchFinderService.getByCode(command.branchCode)
        when (command.type) {
            BasketItemType.TICKET -> {
                val ticketData = command.ticket ?: error("Ticket data must not be null for type=TICKET.")
                val screening = screeningJpaFinderService.getNonDeletedById(ticketData.screeningId)
                val seat = seatJpaFinderService.getByOriginalId(ticketData.seatOriginalId)

                val ticketModel = ticketService.createMessagingTicket(
                    CreateMessagingTicketCommand(
                        ticketId = ticketData.id,
                        screeningId = screening.id,
                        seatId = seat.id,
                        priceCategoryItemNumber = ticketData.basePriceItemNumber,
                        ticketDiscountPrimaryCode = ticketData.ticketDiscountPrimaryCode,
                        ticketDiscountSecondaryCode = ticketData.ticketDiscountSecondaryCode,
                        receiptNumber = ticketData.ticketReceiptNumber,
                        isGroupTicket = ticketData.isGroupTicket,
                        isUsed = ticketData.isUsed,
                        includes3dGlasses = ticketData.includes3dGlasses
                    )
                )

                basketItemRepository.save(
                    BasketItem(
                        id = command.id,
                        basketId = command.basketId,
                        ticketId = ticketModel.ticket.id,
                        type = BasketItemType.TICKET,
                        price = ticketModel.ticketPrice.totalPrice,
                        quantity = 1,
                        isCancelled = command.isCancelled,
                        branchId = branch.id
                    )
                )
            }

            BasketItemType.PRODUCT, BasketItemType.PRODUCT_DISCOUNT -> {
                val productData = command.product ?: error("Product data must not be null for types=[PRODUCT, PRODUCT_DISCOUNT].")
                val product = productJpaFinderService.getNonDeletedByCode(code = productData.productCode)

                basketItemRepository.save(
                    BasketItem(
                        id = command.id,
                        basketId = command.basketId,
                        productId = product.id,
                        type = command.type,
                        price = command.price,
                        quantity = command.quantity,
                        productReceiptNumber = productData.productReceiptNumber,
                        isCancelled = command.isCancelled,
                        branchId = branch.id
                    )
                )
            }
        }.also { basketItem ->
            command.discountCardCode?.let {
                // TODO import discount cards on HQ
                discountCardJooqFinderService.findByCode(it)?.let { discountCard ->
                    applicationEventPublisher.publishEvent(
                        MessagingBasketItemWithDiscountCardCreatedEvent(
                            discountCardId = discountCard.id,
                            basketId = basketItem.basketId,
                            basketItemId = basketItem.id
                        )
                    )
                } ?: log.warn("DiscountCard code $it for Basket messaging item not found, skipping.")
            }
        }
    }

    @Transactional
    @Lock(BASKET_ITEM, CREATE_UPDATE_DELETE_BASKET_ITEM)
    fun messagingCreateCancelledBasketItem(
        @Valid
        @LockFieldParameter("basketId")
        command: MessagingCreateCancelledBasketItemCommand,
    ) {
        val originalBasketItem = basketItemRepository.findByIdAndBasketIdAndDeletedAtIsNull(
            id = command.cancelledBasketItemId,
            basketId = command.basketId
        ) ?: error(
            "BasketItem id=${command.cancelledBasketItemId} for basket id=${command.basketId} not found " +
                "when attempting to cancel it."
        )

        createCancelledBasketItem(originalBasketItem)
        basketItemRepository.save(originalBasketItem.apply { isCancelled = true })
    }

    @Transactional
    @Lock(BASKET_ITEM, CREATE_UPDATE_DELETE_BASKET_ITEM)
    fun deleteBasketItem(
        @Valid
        @LockFieldParameter("basketId")
        command: DeleteBasketItemCommand,
    ) {
        val basket = basketFinderService.getNonDeletedById(command.basketId).also {
            validateModifiableBasketState(it)
        }
        val basketItem = basketItemJpaFinderService.getNonDeletedById(command.basketItemId)

        basketDiscountCardService.deleteDiscountCardUsagesWhenDeletingBasketItem(
            DeleteDiscountCardUsagesWhenDeletingBasketItemCommand(
                basketId = command.basketId,
                basketItemId = command.basketItemId,
                basketItemType = basketItem.type
            )
        )

        when (basketItem.type) {
            BasketItemType.TICKET -> basketItem.ticketId?.let {
                ticketService.deleteTicket(DeleteTicketCommand(it))
            }

            BasketItemType.PRODUCT -> basketItem.productId?.let {
                basketItem.originalId?.let {
                    applicationEventPublisher.publishEvent(TableBasketItemDeletedEvent(basketItem.id))
                }
                applicationEventPublisher.publishEvent(
                    ProductBasketItemDeletedFromBasketEvent(
                        productId = it,
                        quantity = -basketItem.quantity
                    )
                )

                updatePackagingDepositBasketItemIfNeeded(
                    productId = it,
                    basketId = basketItem.basketId,
                    basketItemQuantityDifference = -basketItem.quantity
                )
            }

            BasketItemType.PRODUCT_DISCOUNT -> {}
        } ?: throw InvalidBasketItemStateException()

        basketItemRepository.save(
            basketItem.apply { markDeleted() }
        )

        publishBasketItemModifiedEvents(basket = basket, isDeleteAction = true)
        basketItem.originalId?.let {
            applicationEventPublisher.publishEvent(TableBasketItemDeletedEvent(basketItemId = basketItem.id))
        }
    }

    @Transactional
    @Lock(BASKET_ITEM, CREATE_UPDATE_DELETE_BASKET_ITEM)
    fun deleteBasketItems(
        @Valid
        @LockFieldParameter("basketId")
        command: DeleteBasketItemsCommand,
    ) {
        val basket = basketFinderService.getNonDeletedById(command.basketId).also {
            validateModifiableBasketState(it)
        }

        val basketItems = basketItemJpaFinderService.findAllNonDeletedByIdIn(command.basketItemIds).onEach {
            it.markDeleted()

            // TODO change to batch version when stockQuantity handling is implemented in manager app
            if (it.type == BasketItemType.PRODUCT) {
                applicationEventPublisher.publishEvent(
                    ProductBasketItemDeletedFromBasketEvent(
                        productId = it.productId!!,
                        quantity = -it.quantity
                    )
                )
            }
        }.let {
            basketItemRepository.saveAll(it)
        }

        val ticketIds = basketItems.mapNotNull { it.ticketId }.toSet()
        ticketService.deleteTickets(
            DeleteTicketsCommand(
                basketId = command.basketId,
                ticketIds = ticketIds
            )
        )

        publishBasketItemModifiedEvents(basket = basket, isDeleteAction = true)
    }

    @Transactional
    @Lock(BASKET_ITEM, CREATE_UPDATE_DELETE_BASKET_ITEM)
    fun patchBasketItem(
        @Valid
        @LockFieldParameter("basketId")
        command: PatchBasketItemCommand,
    ) {
        val basket = if (command.patchDeletedBasketItems) {
            basketFinderService.getById(command.basketId)
        } else {
            basketFinderService.getNonDeletedById(command.basketId)
        }.also {
            validateModifiableBasketState(it)
        }
        val basketItem = basketItemJpaFinderService.getNonDeletedById(command.basketItemId).also {
            if (command.quantity == it.quantity) {
                log.info("Existing BasketItem and PatchBasketItemCommand have the same quantity, skipping execution.")
                return
            }
        }
        if (command.isEmpty()) {
            log.info("All PatchBasketItemCommand attributes are null, skipping execution.")
            return
        }

        val patchedItem = when (basketItem.type) {
            BasketItemType.TICKET -> basketItem.ticketId?.let {
                basketDiscountCardService.deleteDiscountCardUsagesWhenPatchingBasketItem(
                    DeleteDiscountCardUsagesWhenPatchingBasketItemCommand(
                        basketId = command.basketId,
                        basketItemId = command.basketItemId,
                        ticketId = it,
                        primaryTicketDiscountId = command.primaryTicketDiscountId,
                        secondaryTicketDiscountId = command.secondaryTicketDiscountId
                    )
                )
                patchTicketBasketItem(
                    ticketId = it,
                    basketItem = basketItem,
                    command = command
                )
            }

            BasketItemType.PRODUCT -> letTwo(
                basketItem.productId,
                command.quantity
            ) { productId, commandQuantity ->
                patchProductBasketItem(productId, basketItem, commandQuantity)
            }

            BasketItemType.PRODUCT_DISCOUNT -> {
                log.warn("Trying to patch BasketItem of type=${BasketItemType.PRODUCT_DISCOUNT}, command=$command, skipping.")
                return
            }
        } ?: throw InvalidBasketItemCommandException()

        basketItemRepository.save(patchedItem)
        publishBasketItemModifiedEvents(basket = basket)
    }

    @Transactional
    @Lock(BASKET_ITEM, CREATE_UPDATE_DELETE_BASKET_ITEM)
    fun updateBasketItemReceiptNumber(
        @Valid
        @LockFieldParameter("basketId")
        command: UpdateBasketItemReceiptNumberCommand,
    ) {
        basketItemRepository.getById(command.basketItemId).also {
            basketItemRepository.save(
                it.apply {
                    productReceiptNumber = command.receiptNumber
                }
            )
        }
    }

    @Transactional
    @Lock(BASKET_ITEM, CREATE_UPDATE_DELETE_BASKET_ITEM)
    fun recalculateBasketProductDiscountPrices(
        @Valid
        @LockFieldParameter("basketId")
        command: RecalculateBasketProductDiscountPricesCommand,
    ) {
        val basketItems = basketItemJpaFinderService.findAllNonDeletedByBasketId(command.basketId)
        val productDiscountIdToProductDiscount = productJooqFinderService.findAllNonDeletedByIdIn(
            basketItems.filter { it.type == BasketItemType.PRODUCT_DISCOUNT }.mapNotNull { it.productId }.toSet()
        ).associateBy { it.id }

        val (nonIsolatedBasketItems, isolatedBasketItems) = basketItems.partition { !it.belongsToIsolatedGroup() }
        val nonIsolatedBasketProductDiscountItems =
            nonIsolatedBasketItems.filter { it.type == BasketItemType.PRODUCT_DISCOUNT }
        val isolatedBasketProductDiscountItems =
            isolatedBasketItems.filter { it.type == BasketItemType.PRODUCT_DISCOUNT }

        // regular product discount prices are calculated from sum of all non-isolated product prices
        nonIsolatedBasketProductDiscountItems.forEach {
            recalculateBasketProductDiscountPrice(
                basketItem = it,
                determinePriceFromBasketItems = nonIsolatedBasketItems,
                productDiscountIdToProductDiscounts = productDiscountIdToProductDiscount
            )
        }

        // isolated product discount prices are calculated only from the product they're in isolated group with
        val isolatedProductIdToBasketItem = isolatedBasketItems.associateBy { it.productId }
        isolatedBasketProductDiscountItems.forEach {
            recalculateBasketProductDiscountPrice(
                basketItem = it,
                determinePriceFromBasketItems = isolatedProductIdToBasketItem[it.productIsolatedWithId]?.let { basketItem ->
                    listOf(basketItem)
                } ?: listOf(),
                productDiscountIdToProductDiscounts = productDiscountIdToProductDiscount
            )
        }
    }

    @Transactional
    fun cancelBasketItems(@Valid command: CancelBasketItemsCommand): Set<UUID> {
        val basketItems = basketItemJpaFinderService.findAllNonDeletedByIdIn(command.basketItemIds).also {
            if (command.basketItemIds.size != it.size) throw BasketItemNotFoundException()
        }
        basketItemJpaFinderService.existByCancelledBasketItemIdIn(command.basketItemIds).ifTrue {
            throw BasketItemIsAlreadyCancelledException()
        }

        basketItems.forEach {
            lockService.obtainBlockingLock(
                module = BASKET_ITEM,
                lockName = CREATE_UPDATE_DELETE_BASKET_ITEM,
                it.basketId.toString()
            ).use { _ ->
                createCancelledBasketItem(it)
                basketItemRepository.save(
                    it.apply { isCancelled = true }
                )

                applicationEventPublisher.publishEvent(
                    it.toMessagingCancelEvent()
                )
            }
        }

        return basketItems.map { it.basketId }.toSet().also {
            if (command.publishEvent) {
                applicationEventPublisher.publishEvent(
                    BasketItemsCancelledEvent(
                        basketIds = it,
                        printReceipt = command.printReceipt,
                        posConfigurationId = command.posConfigurationId
                    )
                )
            }
        }
    }

    @Transactional
    @Lock(BASKET_ITEM, CREATE_UPDATE_DELETE_BASKET_ITEM)
    fun createTicketBasketItems(
        @Valid
        @LockFieldParameter("basketId")
        command: CreateTicketBasketItemsCommand,
    ) {
        val basket = basketFinderService.getNonDeletedById(command.basketId).also {
            validateModifiableBasketState(it)
        }

        command.seatIds.map {
            createTicketBasketItem(
                CreateBasketItemCommand(
                    basketId = command.basketId,
                    type = BasketItemType.TICKET,
                    quantity = 1,
                    screeningId = command.screeningId,
                    primaryTicketDiscountId = command.primaryTicketDiscountId,
                    secondaryTicketDiscountId = command.secondaryTicketDiscountId,
                    isGroupTicket = command.isGroupTicket
                ),
                seatId = it,
                screeningId = command.screeningId,
                priceCategoryItemNumber = command.priceCategoryItemNumber
            )
        }.let { basketItemRepository.saveAll(it) }

        publishBasketItemModifiedEvents(basket = basket)
    }

    @Transactional
    @Lock(BASKET_ITEM, CREATE_UPDATE_DELETE_BASKET_ITEM)
    fun createOnlineTicketBasketItems(
        @Valid
        @LockFieldParameter("basketId")
        command: CreateOnlineTicketBasketItemsCommand,
    ) {
        val basket = basketFinderService.getNonDeletedById(command.basketId).also {
            validateOpenOnlineBasketState(it)
        }

        command.onlineTicketBasketItems.map {
            val basketItem = createTicketBasketItem(
                command = CreateBasketItemCommand(
                    basketId = basket.id,
                    type = BasketItemType.TICKET,
                    quantity = 1,
                    screeningId = command.screeningId,
                    priceCategoryItemNumber = it.priceCategoryItemNumber,
                    primaryTicketDiscountId = it.primaryTicketDiscountId,
                    productDiscountCardId = it.discountCardId,
                    isGroupTicket = false

                ),
                seatId = it.seatId,
                screeningId = command.screeningId,
                priceCategoryItemNumber = it.priceCategoryItemNumber,
                reservationId = it.reservationId
            )
            // if there is used discount for ticket create discountCardUsage
            if (it.discountCardId != null) {
                applicationEventPublisher.publishEvent(
                    MssqlBasketItemWithDiscountCardCreatedEvent(
                        discountCardId = it.discountCardId,
                        screeningId = command.screeningId,
                        basketId = basketItem.basketId,
                        ticketBasketItemId = basketItem.id
                    )
                )
            }
            basketItem
        }.let { basketItemRepository.saveAll(it) }
    }

    @Transactional
    @Lock(BASKET_ITEM, CREATE_UPDATE_DELETE_BASKET_ITEM)
    fun createBasketItemsFromGroupReservation(
        @Valid
        @LockFieldParameter("basketId")
        command: CreateBasketItemsFromGroupReservationCommand,
    ) {
        val basket = basketFinderService.getNonDeletedById(command.basketId).also {
            validateModifiableBasketState(it)
        }

        groupReservationJpaFinderService.existsNonDeletedById(command.groupReservationId).ifFalse {
            throw GroupReservationNotFoundException()
        }

        val reservations = reservationJpaFinderService.findAllNonDeletedByGroupReservationId(
            command.groupReservationId
        )
            .also { reservations ->
                if (reservations.any { it.state != ReservationState.GROUP_RESERVED }) throw InvalidReservationStateException()
            }

        reservations.map {
            createTicketBasketItem(
                CreateBasketItemCommand(
                    basketId = command.basketId,
                    type = BasketItemType.TICKET,
                    quantity = 1
                ),
                seatId = it.seatId,
                screeningId = it.screeningId,
                priceCategoryItemNumber = command.priceCategoryItemNumber,
                reservationId = it.id
            )
        }.let { basketItemRepository.saveAll(it) }

        publishBasketItemModifiedEvents(basket = basket)
    }

    @Transactional
    @Lock(BASKET_ITEM, CREATE_UPDATE_DELETE_BASKET_ITEM)
    fun createPackagingDepositBasketItem(
        @Valid
        @LockFieldParameter("basketId")
        command: CreatePackagingDepositBasketItemCommand,
    ) {
        val basket = basketFinderService.getNonDeletedById(command.basketId).also {
            validateModifiableBasketState(it)
        }

        basketItemRepository.findPackagingDepositBasketItem(basketId = command.basketId)?.let { packagingDepositItem ->
            updatePackagingDepositBasketItem(
                UpdatePackagingDepositBasketItemCommand(
                    basketId = basket.id,
                    basketItemId = packagingDepositItem.id,
                    quantity = packagingDepositItem.quantity + command.quantity
                )
            )
            return
        }

        productJooqFinderService.validateExistsAndIsPackagingDeposit(command.productId)

        createProductBasketItem(
            command = CreateBasketItemCommand(
                basketId = command.basketId,
                type = BasketItemType.PRODUCT,
                quantity = command.quantity,
                productId = command.productId
            ),
            productId = command.productId
        ).also { basketItemRepository.save(it) }

        publishBasketItemModifiedEvents(basket = basket)
    }

    @Transactional
    @Lock(BASKET_ITEM, CREATE_UPDATE_DELETE_BASKET_ITEM)
    fun updatePackagingDepositBasketItem(
        @Valid
        @LockFieldParameter("basketId")
        command: UpdatePackagingDepositBasketItemCommand,
    ) {
        val basket = basketFinderService.getNonDeletedById(command.basketId).also {
            validateModifiableBasketState(it)
        }

        val basketItem = basketItemJpaFinderService.getNonDeletedById(command.basketItemId)
        val product = basketItem.productId?.let {
            productJooqFinderService.validateExistsAndIsPackagingDeposit(it)
        } ?: error("Packaging deposit basketItem id=${basketItem.id} has null productId.")

        if (command.isDeleteAction()) {
            basketItem.apply {
                markDeleted()
            }.also { basketItemRepository.save(it) }
        } else {
            basketItem.apply {
                price = product.price.times(command.quantity.toBigDecimal())
                quantity = command.quantity
            }.also { basketItemRepository.save(it) }
        }
        publishBasketItemModifiedEvents(basket = basket, isDeleteAction = command.isDeleteAction())
    }

    @Transactional
    fun refreshBranchSalesOverview() = basketItemRepository.refreshBranchSalesOverview()

    @Transactional
    fun hardDeleteTicketBasketItem(basketItemId: UUID) {
        basketItemRepository.getTicketBasketItemIdModel(basketItemId)?.let {
            it.discountCardUsageId?.let { usage -> discountCardUsageRepository.deleteById(usage) }
            basketItemRepository.deleteById(it.basketItemId)
            ticketRepository.deleteById(it.ticketId)
            ticketPriceRepository.deleteById(it.ticketPriceId)
        }
    }

    @Transactional
    fun hardDeleteTicketBasketItemDuplicates() {
        val ticketBasketItems = basketItemRepository.findBasketItemsWithDuplicateTickets().groupBy {
            it.receiptNumber
        }

        ticketBasketItems.forEach { entry ->
            val olderModel = entry.value.maxByOrNull { it.basketItemCreatedAt } ?: return@forEach

            log.info("Deleting BasketItem (${olderModel.basketItemId}) duplicate receiptNumber=${olderModel.receiptNumber}.")
            hardDeleteTicketBasketItem(olderModel.basketItemId)
        }
    }

    private fun patchTicketBasketItem(
        ticketId: UUID,
        basketItem: BasketItem,
        command: PatchBasketItemCommand,
    ): BasketItem {
        command.quantity?.let { validateTicketBasketItemQuantity(it) }
        validateTicketDiscountCardApplication(ticketId, basketItem, command)

        val freeTicket = validateBasketTicketDiscountsAndDetermineFreeTicket(
            basketId = basketItem.basketId,
            primaryTicketDiscountId = command.primaryTicketDiscountId?.orElse(null),
            secondaryTicketDiscountId = command.secondaryTicketDiscountId?.orElse(null),
            screeningId = ticketJooqFinderService.getById(ticketId).screeningId
        )

        val ticketModel = ticketService.updateTicket(
            UpdateTicketCommand(
                ticketId = ticketId,
                priceCategoryItemNumber = command.priceCategoryItemNumber,
                primaryTicketDiscountId = command.primaryTicketDiscountId,
                secondaryTicketDiscountId = command.secondaryTicketDiscountId,
                freeTicket = freeTicket
            )
        )

        determineNewlyAppliedDiscountCardId(
            primaryTicketDiscountCardId = command.primaryTicketDiscountCardId,
            secondaryTicketDiscountCardId = command.secondaryTicketDiscountCardId
        )?.let {
            applicationEventPublisher.publishEvent(
                BasketItemWithDiscountCardUpdatedEvent(
                    discountCardId = it,
                    basketId = basketItem.basketId,
                    basketItemId = basketItem.id
                )
            )
        }

        return basketItem.apply {
            price = ticketModel.ticketPrice.totalPrice
            fixedPrice = ticketModel.fixedPrice
        }
    }

    private fun recalculateBasketProductDiscountPrice(
        basketItem: BasketItem,
        determinePriceFromBasketItems: List<BasketItem>,
        productDiscountIdToProductDiscounts: Map<UUID, Product>,
    ) {
        basketItem.productId?.let {
            val discountPrice = productDiscountIdToProductDiscounts[it]?.let { product ->
                productDiscountPriceService.determineProductDiscountPrice(
                    DetermineProductDiscountPriceCommand(
                        basketItems = determinePriceFromBasketItems,
                        productDiscountPercentage = product.discountPercentage,
                        productDiscountTitle = product.title,
                        productDiscountPrice = product.price
                    )
                )
            } ?: error("Failed to determine product discount price.")

            basketItemRepository.save(
                basketItem.apply {
                    price = discountPrice
                }
            )
        }
    }

    private fun mapToNewBasketItem(
        command: CreateBasketItemCommand,
        price: BigDecimal,
        fixedPrice: BigDecimal,
        ticketId: UUID? = null,
        productId: UUID? = null,
        productIsolatedWithId: UUID? = null,
    ) = BasketItem(
        originalId = command.originalId,
        basketId = command.basketId,
        ticketId = ticketId,
        productId = productId,
        productIsolatedWithId = productIsolatedWithId,
        type = command.type,
        price = price,
        fixedPrice = fixedPrice,
        quantity = command.quantity,
        isCancelled = false
    )

    private fun createTicketBasketItem(
        command: CreateBasketItemCommand,
        seatId: UUID,
        screeningId: UUID,
        priceCategoryItemNumber: PriceCategoryItemNumber,
        reservationId: UUID? = null,
    ): BasketItem {
        validateTicketBasketItemQuantity(command.quantity)

        val freeTicket = validateBasketTicketDiscountsAndDetermineFreeTicket(
            basketId = command.basketId,
            primaryTicketDiscountId = command.primaryTicketDiscountId,
            secondaryTicketDiscountId = command.secondaryTicketDiscountId,
            screeningId = screeningId
        )

        val ticketModel = ticketService.createTicket(
            CreateTicketCommand(
                screeningId = screeningId,
                seatId = seatId,
                priceCategoryItemNumber = priceCategoryItemNumber,
                ticketDiscountPrimaryId = command.primaryTicketDiscountId,
                ticketDiscountSecondaryId = command.secondaryTicketDiscountId,
                freeTicket = freeTicket,
                isGroupTicket = command.isGroupTicket ?: false,
                reservationId = reservationId
            )
        )

        return mapToNewBasketItem(
            command = command,
            ticketId = ticketModel.ticket.id,
            price = ticketModel.ticketPrice.totalPrice,
            fixedPrice = ticketModel.fixedPrice
        )
    }

    private fun createProductBasketItem(
        command: CreateBasketItemCommand,
        productId: UUID,
        productIsolatedWithId: UUID? = null,
    ): BasketItem {
        val product = productJooqFinderService.getNonDeletedById(productId).also {
            validateProductStockQuantity(product = it)
        }

        addPackagingDepositBasketItemIfNeeded(command = command, product = product)

        applicationEventPublisher.publishEvent(
            ProductBasketItemAddedToBasketEvent(
                productId = productId,
                quantity = command.quantity
            )
        )

        return mapToNewBasketItem(
            command = command,
            productId = productId,
            productIsolatedWithId = productIsolatedWithId,
            price = product.price.times(command.quantity.toBigDecimal()),
            fixedPrice = 0.toBigDecimal()
        )
    }

    private fun addPackagingDepositBasketItemIfNeeded(command: CreateBasketItemCommand, product: Product) {
        if (product.type == ProductType.PRODUCT) {
            productJooqFinderService.findRelatedPackagingDepositProduct(productId = product.id)
                ?.let { (packagingDepositProductId, amount) ->
                    applicationEventPublisher.publishEvent(
                        ProductWithPackagingDepositFoundEvent(
                            basketId = command.basketId,
                            packagingDepositProductId = packagingDepositProductId,
                            quantity = if (amount.isWholeNumber()) {
                                amount.toInt()
                            } else {
                                error("Packaging deposit amount must be a whole number.")
                            }
                        )
                    )
                }
        }
    }

    private fun createProductDiscountBasketItem(command: CreateBasketItemCommand, productId: UUID): BasketItem {
        validateProductDiscountBasketItemQuantity(command.quantity)

        val productDiscount = productJooqFinderService.getNonDeletedById(productId)
        val basketItems = basketItemJpaFinderService.findAllNonDeletedByBasketId(command.basketId)
        val basketItemIsolatedWith = basketItems.firstOrNull {
            it.productId == command.productIsolatedWithId && it.productId != null
        }
        basketItems.filter { it.type == BasketItemType.PRODUCT_DISCOUNT }.also {
            validateProductDiscounts(productDiscount, it)
        }

        return mapToNewBasketItem(
            command = command,
            productId = productId,
            productIsolatedWithId = command.productIsolatedWithId,
            price = productDiscountPriceService.determineProductDiscountPrice(
                DetermineProductDiscountPriceCommand(
                    basketItems = basketItemIsolatedWith?.let { listOf(it) } ?: listOf(),
                    productDiscountPercentage = productDiscount.discountPercentage,
                    productDiscountTitle = productDiscount.title,
                    productDiscountPrice = productDiscount.price
                )
            ),
            fixedPrice = 0.toBigDecimal()
        )
    }

    private fun patchProductBasketItem(
        productId: UUID,
        basketItem: BasketItem,
        commandQuantity: Int,
    ): BasketItem {
        val product = productJooqFinderService.getNonDeletedById(productId).also {
            validateProductStockQuantity(it)
        }
        val quantityDifference = commandQuantity.minus(basketItem.quantity)
        applicationEventPublisher.publishEvent(
            ProductBasketItemQuantityModifiedEvent(
                productId = productId,
                quantityDifference = quantityDifference
            )
        )
        basketItem.originalId?.let {
            applicationEventPublisher.publishEvent(TableBasketItemUpdatedEvent(basketItemId = basketItem.id))
        }

        updatePackagingDepositBasketItemIfNeeded(
            productId = productId,
            basketId = basketItem.basketId,
            basketItemQuantityDifference = quantityDifference
        )

        val newQuantity = basketItem.quantity.plus(quantityDifference)
        return basketItem.apply {
            price = product.price.times(newQuantity.toBigDecimal())
            quantity = newQuantity
        }
    }

    private fun updatePackagingDepositBasketItemIfNeeded(
        productId: UUID,
        basketId: UUID,
        basketItemQuantityDifference: Int,
    ) {
        basketItemJpaFinderService.findRelatedPackagingDepositBasketItemModel(
            productId = productId,
            basketId = basketId
        )?.let { model ->
            if (!model.getProductCompositionAmount().isWholeNumber()) {
                error("Packaging deposit amount must be a whole number.")
            }

            val newPackagingBasketItemQuantity =
                model.getBasketItemQuantity() + (basketItemQuantityDifference * model.getProductCompositionAmount().toInt())

            applicationEventPublisher.publishEvent(
                BasketItemWithPackagingDepositUpdatedEvent(
                    basketId = basketId,
                    packagingDepositItemId = model.getBasketItemId(),
                    packagingDepositItemQuantity = newPackagingBasketItemQuantity
                )
            )
        }
    }

    private fun validateBasketTicketDiscountsAndDetermineFreeTicket(
        basketId: UUID,
        primaryTicketDiscountId: UUID?,
        secondaryTicketDiscountId: UUID?,
        screeningId: UUID,
    ): Boolean {
        val (primaryDiscount, secondaryDiscount) = ticketDiscountJooqFinderService.validateAndGetDiscounts(
            primaryDiscountId = primaryTicketDiscountId,
            secondaryDiscountId = secondaryTicketDiscountId
        )

        setOfNotNull(primaryTicketDiscountId, secondaryTicketDiscountId).forEach {
            validateDiscountCardTicketDiscountUsageCountLimits(it, basketId, screeningId)
        }

        val tickets = ticketJooqFinderService.findAllNonDeletedByBasketId(basketId)
        val primaryTicketDiscountCounts =
            tickets.mapNotNull { it.ticketDiscountPrimaryId }.groupingBy { it }.eachCount()
        val secondaryTicketDiscountCounts =
            tickets.mapNotNull { it.ticketDiscountSecondaryId }.groupingBy { it }.eachCount()

        val freeTicketPrimary = primaryDiscount?.let {
            validateDiscountWithinBasketAndDetermineFreeTicket(
                ticketDiscount = it,
                ticketDiscountIdToBasketCount = primaryTicketDiscountCounts,
                ticketDiscountCardLimit = determineTicketDiscountLimitFromAppliedDiscountCard(it.id, basketId)
            )
        }
        val freeTicketSecondary = secondaryDiscount?.let {
            validateDiscountWithinBasketAndDetermineFreeTicket(
                ticketDiscount = it,
                ticketDiscountIdToBasketCount = secondaryTicketDiscountCounts,
                ticketDiscountCardLimit = determineTicketDiscountLimitFromAppliedDiscountCard(it.id, basketId)
            )
        }

        return setOf(freeTicketPrimary, freeTicketSecondary).filterNotNull().any { it }
    }

    private fun validateDiscountWithinBasketAndDetermineFreeTicket(
        ticketDiscount: TicketDiscount,
        ticketDiscountIdToBasketCount: Map<UUID, Int>,
        ticketDiscountCardLimit: Int,
    ): Boolean {
        ticketDiscountIdToBasketCount[ticketDiscount.id].also {
            if (ticketDiscountCardLimit > 0) {
                if (it.isGreaterThanOrEqual(ticketDiscountCardLimit)) throw TicketDiscountVoucherApplicableToBasketLimitExceeded()
            } else {
                if (it.isGreaterThanOrEqual(ticketDiscount.applicableToCount)) throw TicketDiscountApplicableToCountExceeded()
            }

            return isFreeTicket(ticketDiscount.applicableToCount, ticketDiscount.freeCount, it)
        }
    }

    private fun validateProductDiscounts(productDiscount: Product, basketProductDiscounts: List<BasketItem>) {
        productCategoryJooqFinderService.getNonDeletedById(productDiscount.productCategoryId)
        // FIXME PROD issue - product discount on VIP card is of ProductCategoryType.PRODUCT, not .DISCOUNT, thus throwing ex
        // .also {
        //     if (it.type != ProductCategoryType.DISCOUNT) throw InvalidProductDiscountCategoryException()
        // }

        basketProductDiscounts.filter { it.id == productDiscount.id }.also {
            if (it.isNotEmpty()) throw ProductDiscountAlreadyAppliedException()
        }
    }

    private fun isFreeTicket(applicableToCount: Int?, freeCount: Int?, basketCount: Int?): Boolean {
        applicableToCount?.let {
            return freeCount?.let {
                if (applicableToCount > freeCount) {
                    // first, (applicableToCount - freeCount) discounted or full tickets are added, free tickets follow after
                    if (basketCount == null) false else basketCount >= (applicableToCount - freeCount)
                } else {
                    // applicableToCount <= freeCount
                    // all tickets are for free until applicableToCount is reached
                    (basketCount == null) || basketCount < applicableToCount
                }
                // applicableToCount > 0 && freeCount == null -> no free tickets
            } ?: false
        } ?: return freeCount?.let {
            // applicableToCount == null && freeCount > 0
            // first n tickets (n = freeCount) are for free, infinitely many following tickets have discount applied
            (basketCount == null) || basketCount < freeCount
            // applicableToCount == null && freeCount == null -> no free tickets
        } ?: false
    }

    // TODO refactor - use new discount card usage logic
    //  Note: application of new DCU logic here is blocked by https://cleevio.atlassian.net/browse/CMX-776
    private fun validateDiscountCardTicketDiscountUsageCountLimits(
        ticketDiscountId: UUID,
        basketId: UUID,
        screeningId: UUID,
    ) {
        val discountCard = determineCurrentlyAppliedDiscountCard(ticketDiscountId, basketId).let {
            if (it == null) return else return@let it
        }

        discountCardJooqFinderService.findAllByTicketDiscountIdAndBasketIdAndScreeningId(
            ticketDiscountId = ticketDiscountId,
            basketId = basketId
        ).onEach {
            if (it.id == discountCard.id) throw TicketDiscountCardApplicableToBasketLimitExceeded()
        }

        discountCardJooqFinderService.findAllByTicketDiscountIdAndBasketIdAndScreeningId(
            ticketDiscountId = ticketDiscountId,
            basketId = basketId,
            screeningId = screeningId
        ).onEach {
            if (it.id == discountCard.id) throw TicketDiscountCardApplicableToScreeningLimitExceeded()
        }

        discountCard.applicableToScreeningsPerDay?.let {
            val screening = screeningJpaFinderService.getNonDeletedById(screeningId)
            val ticketScreeningsPerDayCount = ticketJooqFinderService.findAllByDiscountCardIdAndScreeningDateCount(
                discountCardId = discountCard.id,
                screeningDate = screening.date
            )
            if (ticketScreeningsPerDayCount >= it) {
                throw TicketDiscountCardScreeningsPerDayLimitExceededException()
            }
        }
    }

    private fun validateProductStockQuantity(product: Product) {
        if (product.type == ProductType.PRODUCT &&
            !productJooqFinderService.existsNonDeletedByIdAndStockQuantityGreaterThanOrEqualOne(product.id)
        ) {
            val stockQuantity =
                productJooqFinderService.findAllNonDeletedActiveProductStockQuantitiesByIdIn(setOf(product.id))[product.id]

            log.error("Product id=${product.id} has insufficient stock quantity=$stockQuantity, throwing exception.")
            throw ProductBasketItemWithInsufficientStockQuantityException()
        }
    }

    private fun determineCurrentlyAppliedDiscountCard(ticketDiscountId: UUID, basketId: UUID): DiscountCard? {
        val discountCardsAppliedOnBasket = discountCardJooqFinderService.findAllByTicketDiscountIdAndBasketId(
            ticketDiscountId = ticketDiscountId,
            basketId = basketId
        ).toSet()

        val discountCardsAppliedOnTicketsInBasket =
            discountCardJooqFinderService.findAllByTicketDiscountIdAndBasketIdUsingTicketTable(
                ticketDiscountId = ticketDiscountId,
                basketId = basketId
            ).toSet()

        return (discountCardsAppliedOnBasket subtract discountCardsAppliedOnTicketsInBasket).firstOrNull()
    }

    private fun publishBasketItemModifiedEvents(basket: Basket, isDeleteAction: Boolean = false) {
        applicationEventPublisher.publishEvent(BasketItemModifiedEvent(basket.id))

        basket.tableId?.let {
            applicationEventPublisher.publishEvent(TableBasketItemModifiedEvent(basketId = basket.id, tableId = it))
        }

        if (basket.state == BasketState.PAYMENT_IN_PROGRESS && !isDeleteAction) {
            applicationEventPublisher.publishEvent(BasketModifiedInPaymentInProgressStateEvent(basket.id))
        }
    }

    private fun determineTicketDiscountLimitFromAppliedDiscountCard(ticketDiscountId: UUID, basketId: UUID): Int {
        return discountCardJooqFinderService.findAllByTicketDiscountIdAndBasketId(
            ticketDiscountId = ticketDiscountId,
            basketId = basketId
        ).sumOf { it.applicableToBasket ?: 0 }
    }

    private fun validateTicketDiscountCardApplication(
        ticketId: UUID,
        basketItem: BasketItem,
        command: PatchBasketItemCommand,
    ) {
        if (command.primaryTicketDiscountCardId != null && command.secondaryTicketDiscountCardId != null) {
            throw TwoDiscountCardsAppliedOnTicketBasketItemException()
        }

        val ticket = ticketJooqFinderService.getNonDeletedById(ticketId)
        val appliedDiscountCard =
            discountCardJooqFinderService.findAppliedOnBasketItem(basketItem.id, BasketItemType.TICKET)
        val discountCardDiscount = appliedDiscountCard?.ticketDiscountId?.let {
            ticketDiscountJooqFinderService.findNonDeletedById(it)
        }

        val isPrimaryDiscountFromDiscountCard = discountCardDiscount?.let {
            ticket.ticketDiscountPrimaryId == discountCardDiscount.id
        } ?: false
        val isSecondaryDiscountFromDiscountCard = discountCardDiscount?.let {
            ticket.ticketDiscountSecondaryId == discountCardDiscount.id
        } ?: false

        val appliedPrimaryTicketDiscountFromCard = command.primaryTicketDiscountCardId != null
        val appliedSecondaryTicketDiscountFromCard = command.secondaryTicketDiscountCardId != null

        val twoCardsCombined = (isPrimaryDiscountFromDiscountCard && appliedSecondaryTicketDiscountFromCard) ||
            (isSecondaryDiscountFromDiscountCard && appliedPrimaryTicketDiscountFromCard)

        if (twoCardsCombined) throw SecondDiscountCardDiscountAppliedOnTicketBasketItemException()
    }

    private fun createCancelledBasketItem(
        originalBasketItem: BasketItem,
    ) = basketItemRepository.save(
        BasketItem(
            originalId = null,
            basketId = originalBasketItem.basketId,
            ticketId = originalBasketItem.ticketId,
            productId = originalBasketItem.productId,
            productIsolatedWithId = originalBasketItem.productIsolatedWithId,
            productReceiptNumber = originalBasketItem.productReceiptNumber,
            type = originalBasketItem.type,
            price = originalBasketItem.price,
            quantity = originalBasketItem.quantity,
            cancelledBasketItemId = originalBasketItem.id,
            isCancelled = false
        )
    )

    private fun determineNewlyAppliedDiscountCardId(
        primaryTicketDiscountCardId: UUID? = null,
        secondaryTicketDiscountCardId: UUID? = null,
        productDiscountCardId: UUID? = null,
        basketItemType: BasketItemType = BasketItemType.TICKET,
    ) = when (basketItemType) {
        BasketItemType.TICKET -> setOfNotNull(primaryTicketDiscountCardId, secondaryTicketDiscountCardId).firstOrNull()
        BasketItemType.PRODUCT -> productDiscountCardId
        BasketItemType.PRODUCT_DISCOUNT -> productDiscountCardId
    }
}
