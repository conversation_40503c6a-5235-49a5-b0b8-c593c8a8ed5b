package com.cleevio.cinemax.api.module.productcomponentcategory.event

import com.cleevio.cinemax.api.common.integration.pubsub.constant.MessageType
import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.util.toJsonString
import com.cleevio.cinemax.api.module.productcomponentcategory.entity.ProductComponentCategory
import java.util.UUID

data class AdminProductComponentCategoryCreatedOrUpdatedEvent(
    val code: String,
    val title: String,
    val taxRate: Int,
) {
    fun toMessagePayload() = MessagePayload(
        id = UUID.randomUUID(),
        type = MessageType.PRODUCT_COMPONENT_CATEGORY_CREATED_OR_UPDATED,
        data = this.toJsonString()
    )
}

fun ProductComponentCategory.toMessagingEvent() = AdminProductComponentCategoryCreatedOrUpdatedEvent(
    code = code,
    title = title,
    taxRate = taxRate
)
