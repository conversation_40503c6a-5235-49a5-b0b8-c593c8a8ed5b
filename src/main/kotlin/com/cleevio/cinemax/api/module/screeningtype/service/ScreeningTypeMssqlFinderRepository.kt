package com.cleevio.cinemax.api.module.screeningtype.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.DeploymentType
import com.cleevio.cinemax.api.common.util.buildGreaterThanDateTimeMssqlCondition
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables.CTYPPR
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Ctyppr
import org.jooq.DSLContext
import org.jooq.impl.DSL.table
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class ScreeningTypeMssqlFinderRepository(
    private val mssqlCinemaxDslContext: DSLContext,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
) {

    fun findAll(): List<Ctyppr> {
        return mssqlCinemaxDslContext
            .selectFrom(CTYPPR)
            .fetchInto(Ctyppr::class.java)
    }

    fun findAllByUpdatedAtGt(updatedAt: LocalDateTime? = null): List<Ctyppr> {
        return when (cinemaxConfigProperties.deploymentType) {
            DeploymentType.HEADQUARTERS -> {
                mssqlCinemaxDslContext
                    .select(table(CTYPPR.name).asterisk().except(CTYPPR.PRIPLATEK))
                    .from(CTYPPR.name)
                    .where(buildGreaterThanDateTimeMssqlCondition(updatedAt, CTYPPR.ZCAS))
                    .fetchInto(Ctyppr::class.java)
            }

            DeploymentType.BRANCH -> {
                mssqlCinemaxDslContext
                    .selectFrom(CTYPPR)
                    .where(buildGreaterThanDateTimeMssqlCondition(updatedAt, CTYPPR.ZCAS))
                    .fetchInto(Ctyppr::class.java)
            }
        }
    }

    fun findByOriginalId(originalId: Int): Ctyppr? {
        return mssqlCinemaxDslContext
            .selectFrom(CTYPPR)
            .where(CTYPPR.CTYPPRID.eq(originalId))
            .fetchOneInto(Ctyppr::class.java)
    }
}
