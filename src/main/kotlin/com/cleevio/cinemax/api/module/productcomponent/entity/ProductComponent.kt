package com.cleevio.cinemax.api.module.productcomponent.entity

import com.cleevio.cinemax.api.common.entity.DeletableEntity
import com.cleevio.cinemax.api.common.util.addTax
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import jakarta.persistence.Column
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.math.BigDecimal
import java.util.UUID
import jakarta.persistence.Entity as JpaEntity

@JpaEntity
class ProductComponent(
    id: UUID = UUID.randomUUID(),

    @Column(name = "original_id")
    var originalId: Int?,

    @Column(name = "code", nullable = false)
    var code: String,

    @Column(name = "product_component_category_id", nullable = false)
    var productComponentCategoryId: UUID,

    @Column(name = "title", nullable = false)
    var title: String,

    @Column(name = "unit", nullable = false)
    @Enumerated(EnumType.STRING)
    var unit: ProductComponentUnit,

    @Column(name = "purchase_price", nullable = false)
    var purchasePrice: BigDecimal,

    @Column(name = "stock_quantity", nullable = false)
    var stockQuantity: BigDecimal,

    @Column(name = "active", nullable = false)
    var active: Boolean,

    @Column(name = "tax_rate_override")
    var taxRateOverride: Int? = null,

    @Column(name = "purchase_price_override")
    var purchasePriceOverride: BigDecimal? = null,
) : DeletableEntity(id) {

    init {
        taxRateOverride?.let { purchasePriceOverride = purchasePrice.addTax(it) }
    }
}
