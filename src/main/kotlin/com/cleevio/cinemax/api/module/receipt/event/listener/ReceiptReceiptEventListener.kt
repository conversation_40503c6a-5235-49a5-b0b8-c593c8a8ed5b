package com.cleevio.cinemax.api.module.receipt.event.listener

import com.cleevio.cinemax.api.module.receipt.event.PrintStateDeterminedEvent
import com.cleevio.cinemax.api.module.receipt.service.ReceiptService
import com.cleevio.cinemax.api.module.receipt.service.command.UpdateLastPrintStateCommand
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    "features.receipt.update-print-state-listener.enabled",
    havingValue = "true",
    matchIfMissing = true
)
class ReceiptReceiptEventListener(
    private val receiptService: ReceiptService,
) {

    @Async
    @EventListener
    fun listenToPrintStateDeterminedEvent(event: PrintStateDeterminedEvent) {
        receiptService.updateLastPrintState(
            UpdateLastPrintStateCommand(
                receiptId = event.receiptId,
                lastPrintState = event.printState
            )
        )
    }
}
