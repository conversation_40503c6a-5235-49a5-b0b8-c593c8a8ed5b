package com.cleevio.cinemax.api.module.terminalpayment.service

import com.cleevio.cinemax.api.common.util.calculateLRC
import com.cleevio.cinemax.api.common.util.toCents
import com.cleevio.cinemax.api.module.terminalpayment.constant.TerminalConnectionType
import java.math.BigDecimal

/**
 * Basket.totalPrice: EUR 2.60
 *
 * Example SERIAL request: <02>S260<03>d
 *     - <02> is a special byte (0x02) indicating start of text
 *     - S260 is the message, meaning 'S' as in sale, and 260 as in amount of cents
 *     - <03> is a special byte (0x03) indicating end of text
 *     - d is LRC byte (longitudinal redundancy check, commonly used error-detection method, calculated from whole message
 *       excluding STX and including final ETX)
 *
 * Example ETHERNET request: <02>S260<1c>i978<1c>V1.00<1c>zCINEMAX<03>D
 *     - start of the request is identical to SERIAL, but has some special constant flags appended
 *     - <1c> is a special byte (0x1c) indicating field separator
 *     - i978 is the currency code for Euro
 *     - V1.00 is the version of ECR protocol
 *     - zCINEMAX is integrator specific data (ID)
 *     - x1 is a flag indicating that both customer and merchant receipts should be printed
 *     - <03> is a special byte (0x03) indicating end of text
 *     - D is LRC byte (longitudinal redundancy check, commonly used error-detection method, calculated from whole message
 *       excluding STX and including final ETX)
 */
fun generateTerminalSaleRequest(
    price: BigDecimal,
    terminalConnectionType: TerminalConnectionType = TerminalConnectionType.SERIAL,
): ByteArray {
    val amountCents = price.toCents().toString().replace(".", "").trimStart('0')
    val saleRequest = "$SALE_FLAG$amountCents".toByteArray()
    val lrcSequence = when (terminalConnectionType) {
        TerminalConnectionType.SERIAL -> saleRequest + ETX
        TerminalConnectionType.ETHERNET -> {
            saleRequest + FS + EURO_CURRENCY_CODE + FS + VERSION + FS + INTEGRATOR_ID + FS + PRINT_RECEIPT + ETX
        }
    }

    return byteArrayOf(STX) + lrcSequence + lrcSequence.calculateLRC()
}

private const val SALE_FLAG = "S"
private const val STX: Byte = 0x02 // Start of text byte
private const val ETX: Byte = 0x03 // End of text byte
private const val FS: Byte = 0x1c // Field separator byte

private val EURO_CURRENCY_CODE = "i978".toByteArray()
private val VERSION = "V1.00".toByteArray()
private val INTEGRATOR_ID = "zCINEMAX".toByteArray()
private val PRINT_RECEIPT = "x1".toByteArray()
