package com.cleevio.cinemax.api.module.terminalpayment.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.NOT_FOUND)
class TerminalPaymentNotFoundException : ApiException(
    Module.TERMINAL_PAYMENT,
    TerminalPaymentErrorType.TERMINAL_PAYMENT_NOT_FOUND
)
