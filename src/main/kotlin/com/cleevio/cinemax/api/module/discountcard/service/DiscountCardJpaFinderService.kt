package com.cleevio.cinemax.api.module.discountcard.service

import com.cleevio.cinemax.api.module.discountcard.entity.DiscountCard
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountCardNotFoundException
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class DiscountCardJpaFinderService(
    private val repository: DiscountCardRepository,
) {

    fun existsByTicketDiscountId(ticketDiscountId: UUID): Boolean = repository.existsByTicketDiscountId(ticketDiscountId)

    fun findById(id: UUID): DiscountCard? = repository.findByIdOrNull(id)

    fun findAllByIdIn(ids: Set<UUID>): List<DiscountCard> = repository.findAllById(ids)

    fun findByCode(code: String): DiscountCard? = repository.findByCode(code)

    fun getByCode(code: String): DiscountCard = repository.findByCode(code) ?: throw DiscountCardNotFoundException()

    fun findAllByCodeIn(codes: Set<String>): List<DiscountCard> = repository.findAllByCodeIn(codes)

    fun getById(id: UUID): DiscountCard = findById(id) ?: throw DiscountCardNotFoundException()
}
