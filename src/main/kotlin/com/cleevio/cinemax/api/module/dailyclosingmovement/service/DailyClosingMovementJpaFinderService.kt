package com.cleevio.cinemax.api.module.dailyclosingmovement.service

import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DAILY_CLOSING_BASE_MOVEMENT_ITEM_SUBTYPES
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemSubtype
import com.cleevio.cinemax.api.module.dailyclosingmovement.entity.DailyClosingMovement
import com.cleevio.cinemax.api.module.dailyclosingmovement.exception.DailyClosingMovementNotFoundException
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class DailyClosingMovementJpaFinderService(
    private val repository: DailyClosingMovementRepository,
) {

    fun findAllNonDeletedBaseMovementsByDailyClosingId(dailyClosingId: UUID): List<DailyClosingMovement> =
        repository.findAllByDailyClosingIdAndItemSubtypeInAndDeletedAtIsNull(
            dailyClosingId = dailyClosingId,
            itemSubtypes = DAILY_CLOSING_BASE_MOVEMENT_ITEM_SUBTYPES
        )

    fun getNonDeletedByIdAndDailyClosingId(id: UUID, dailyClosingId: UUID): DailyClosingMovement =
        repository.findByIdAndDailyClosingIdAndDeletedAtIsNull(
            id = id,
            dailyClosingId = dailyClosingId
        ) ?: throw DailyClosingMovementNotFoundException()

    fun findAllNonDeletedCashMovementsByDailyClosingIds(dailyClosingIds: Set<UUID>): List<DailyClosingMovement> =
        repository.findAllNonDeletedCashMovementsByDailyClosingIds(dailyClosingIds)

    fun findAllDailyClosingIdsWithEmptyCashMovements(dailyClosingIds: Set<UUID>): List<UUID> =
        repository.findAllDailyClosingIdsWithEmptyCashMovements(dailyClosingIds)

    fun existsNonDeletedDeductionMovement(dailyClosingId: UUID) =
        repository.existsByDailyClosingIdAndItemSubtypeAndDeletedAtIsNull(
            dailyClosingId = dailyClosingId,
            itemSubtype = DailyClosingMovementItemSubtype.DEDUCTION
        )
}
