package com.cleevio.cinemax.api.module.tms.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.service.model.toStreamingResponseEntity
import com.cleevio.cinemax.api.module.tms.service.TmsService
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody

@Tag(name = "TMS App Schedule")
@RestController
@RequestMapping("/tms-app")
class TmsController(
    private val tmsService: TmsService,
) {

    @GetMapping("/schedule", produces = [ApiVersion.VERSION_1_XML])
    fun getTmsData(): ResponseEntity<StreamingResponseBody> =
        tmsService.getTmsScheduleData().toStreamingResponseEntity(
            exportTitle = "TmsSystemScheduleData",
            exportFormat = ExportFormat.XML
        )
}
