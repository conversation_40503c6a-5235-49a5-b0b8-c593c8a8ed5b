package com.cleevio.cinemax.api.module.screeningfee.service

import com.cleevio.cinemax.api.module.screeningfee.entity.ScreeningFee
import com.cleevio.cinemax.api.module.screeningfee.exception.ScreeningFeeNotFoundException
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class ScreeningFeeJpaFinderService(
    private val screeningFeeRepository: ScreeningFeeRepository,
) {

    fun findByScreeningId(screeningId: UUID): ScreeningFee? {
        return screeningFeeRepository.findByScreeningId(screeningId)
    }

    fun getByScreeningId(screeningId: UUID): ScreeningFee {
        return findByScreeningId(screeningId) ?: throw ScreeningFeeNotFoundException()
    }

    fun findAllByScreeningIdIn(screeningIds: Set<UUID>): List<ScreeningFee> {
        return screeningFeeRepository.findAllByScreeningIdIn(screeningIds)
    }
}
