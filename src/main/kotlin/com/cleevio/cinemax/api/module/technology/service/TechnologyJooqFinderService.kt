package com.cleevio.cinemax.api.module.technology.service

import com.cleevio.cinemax.api.module.technology.entity.Technology
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class TechnologyJooqFinderService(
    private val technologyJooqFinderRepository: TechnologyJooqFinderRepository,
) {

    fun findAll(): List<Technology> {
        return technologyJooqFinderRepository.findAll()
    }
}
