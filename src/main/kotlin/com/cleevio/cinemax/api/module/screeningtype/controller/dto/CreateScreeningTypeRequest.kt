package com.cleevio.cinemax.api.module.screeningtype.controller.dto

import com.cleevio.cinemax.api.module.screeningtype.service.command.CreateOrUpdateScreeningTypeCommand

data class CreateScreeningTypeRequest(
    val code: String,
    val title: String,
) {

    fun toCommand(): CreateOrUpdateScreeningTypeCommand = CreateOrUpdateScreeningTypeCommand(
        code = code,
        title = title
    )
}
