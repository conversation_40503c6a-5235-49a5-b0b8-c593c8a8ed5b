package com.cleevio.cinemax.api.module.discountcard.service

import com.cleevio.cinemax.api.common.constant.DISCOUNT_CARD
import com.cleevio.cinemax.api.common.service.AbstractMssqlSynchronizationService
import com.cleevio.cinemax.api.common.util.forceNegate
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardLockValues.SYNCHRONIZE_BUFFET_VOUCHER_FROM_MSSQL
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.entity.DiscountCard
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountCardCodeAlreadyExistsException
import com.cleevio.cinemax.api.module.discountcard.service.command.CreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.module.product.service.ProductJooqFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlService
import com.cleevio.cinemax.mssql.rp_bufete.dbo.Tables.VOUCHER
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Voucher
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class DiscountVoucherBuffetMssqlSynchronizationService(
    private val discountCardService: DiscountCardService,
    private val discountCardJooqFinderService: DiscountCardJooqFinderService,
    private val discountVoucherMssqlBuffetFinderRepository: DiscountVoucherMssqlBuffetFinderRepository,
    private val productJooqFinderService: ProductJooqFinderService,
    private val synchronizationFromMssqlFinderService: SynchronizationFromMssqlFinderService,
    synchronizationFromMssqlService: SynchronizationFromMssqlService,
) : AbstractMssqlSynchronizationService<Voucher>(
    mssqlEntityName = VOUCHER.name,
    synchronizationFromMssqlService = synchronizationFromMssqlService,
    synchronizationType = SynchronizationFromMssqlType.DISCOUNT_VOUCHER_BUFFET
) {
    private val logger = logger()

    @TryLock(DISCOUNT_CARD, SYNCHRONIZE_BUFFET_VOUCHER_FROM_MSSQL)
    fun synchronizeAll() {
        synchronizeAll {
            discountVoucherMssqlBuffetFinderRepository.findAllValidByUpdatedAtGt(
                synchronizationFromMssqlFinderService.findByType(SynchronizationFromMssqlType.DISCOUNT_VOUCHER_BUFFET)
            )
        }
    }

    override fun validateAndPersist(mssqlEntity: Voucher) {
        val code = mssqlEntity.cislo.trim()

        val existingDiscountCard = discountCardJooqFinderService.findByCode(code)?.let {
            if (it.type == DiscountCardType.CARD) {
                logger.error("Discount card (CARD) with code=$code already exists. Synchronization of voucher aborted.")
                throw DiscountCardCodeAlreadyExistsException()
            }
            return@let it
        }

        if (mssqlEntity.slevac.isBlank()) {
            logger.warn("Discount voucher originalId=${mssqlEntity.voucherid} has blank 'slevac' attribute, skipping.")
            return
        }

        val productDiscount = productJooqFinderService.findNonDeletedByOriginalCode(mssqlEntity.slevac.trim())
        val product = productJooqFinderService.findNonDeletedByOriginalCode(mssqlEntity.cmenu.trim())

        discountCardService.createOrUpdateDiscountCard(
            mapToCreateOrUpdateDiscountCardCommand(
                existingDiscountCard = existingDiscountCard,
                mssqlVoucher = mssqlEntity,
                productDiscountId = productDiscount?.id,
                productId = product?.id
            )
        )
    }

    private fun mapToCreateOrUpdateDiscountCardCommand(
        existingDiscountCard: DiscountCard?,
        mssqlVoucher: Voucher,
        productDiscountId: UUID? = null,
        productId: UUID? = null,
    ): CreateOrUpdateDiscountCardCommand {
        return CreateOrUpdateDiscountCardCommand(
            // buffet only vouchers have original id negated and incremented by a constant
            originalId = (existingDiscountCard?.originalId ?: (mssqlVoucher.voucherid + BUFFET_VOUCHER_ID_BASE)).forceNegate(),
            productDiscountId = productDiscountId,
            productId = productId,
            type = DiscountCardType.VOUCHER,
            title = mssqlVoucher.typ.trim(),
            code = mssqlVoucher.cislo.trim(),
            validFrom = mssqlVoucher.platod.toLocalDate(),
            validUntil = mssqlVoucher.platdo.toLocalDate(),
            applicableToBasket = existingDiscountCard?.applicableToBasket,
            applicableToScreening = existingDiscountCard?.applicableToScreening,
            applicableToScreeningsPerDay = null,
            productsCount = 1
        )
    }
}

private const val BUFFET_VOUCHER_ID_BASE = 1000000
