package com.cleevio.cinemax.api.module.distributor.service

import com.cleevio.cinemax.api.module.distributor.entity.Distributor
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface DistributorRepository : JpaRepository<Distributor, UUID> {

    fun findByCodeAndDeletedAtIsNull(originalCode: String): Distributor?

    fun findByCode(originalCode: String): Distributor?

    fun findByIdAndDeletedAtIsNull(id: UUID): Distributor?

    fun findByOriginalId(originalId: Int): Distributor?

    fun existsByCodeAndDeletedAtIsNull(code: String): Boolean
}
