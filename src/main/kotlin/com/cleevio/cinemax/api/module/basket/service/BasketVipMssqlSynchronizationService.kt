package com.cleevio.cinemax.api.module.basket.service

import com.cleevio.cinemax.api.common.constant.BASKET
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.module.basket.constant.BasketLockValues.SYNCHRONIZED_CLOSED_VIP_FROM_MSSQL
import com.cleevio.cinemax.api.module.basket.service.command.CloseVipBasketCommand
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemMssqlFinderService
import com.cleevio.cinemax.api.module.table.service.TableFinderService
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class BasketVipMssqlSynchronizationService(
    private val basketFinderRepository: BasketFinderRepository,
    private val tableFinderService: TableFinderService,
    private val basketItemMssqlFinderService: BasketItemMssqlFinderService,
    private val basketVipService: BasketVipService,
) {
    private val log = logger()

    @TryLock(BASKET, SYNCHRONIZED_CLOSED_VIP_FROM_MSSQL)
    fun synchronizeClosedVipBaskets() {
        log.debug("Starting synchronization of paid or cancelled baskets on VIP tables from MSSQL.")

        val openBasketsOnTables = basketFinderRepository.findAllNonDeletedAndNonEmptyByStateIsOpenAndTableIdIsNotNull().ifEmpty {
            log.debug("No open baskets on VIP tables found, skipping sync of paid or cancelled VIP baskets from MSSQL.")
            return
        }

        val basketTableIds = openBasketsOnTables.mapNotNull { it.tableId }.toSet()
        val basketTables = tableFinderService.findAllByIdIn(basketTableIds)
        val basketTablePosOriginalIds = basketTables.mapToSet { it.originalId }
        val basketTablePosOriginalIdToId = basketTables.associate { it.originalId to it.id }
        val basketTableLegacyOriginalIds = basketItemMssqlFinderService.findAll().mapToSet { it.stolyid }
        val tableWithPaidOrCancelledBasketInMssqlOriginalIds = basketTablePosOriginalIds subtract basketTableLegacyOriginalIds
        val tableWithPaidOrCancelledBasketInMssqlIds = tableWithPaidOrCancelledBasketInMssqlOriginalIds
            .mapToSet { basketTablePosOriginalIdToId[it]!! }
        val posBasketsToClose = openBasketsOnTables.filter { tableWithPaidOrCancelledBasketInMssqlIds.contains(it.tableId) }
        log.info("Found ${posBasketsToClose.size} paid or cancelled baskets on VIP tables to synchronize.")

        posBasketsToClose.forEach {
            basketVipService.closeVipBasket(CloseVipBasketCommand(basketId = it.id, tableId = it.tableId!!))
        }
        log.debug("Synchronization of paid or cancelled baskets on VIP tables from MSSQL finished.")
    }
}
