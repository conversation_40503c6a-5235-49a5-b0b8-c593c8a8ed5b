package com.cleevio.cinemax.api.module.screeningtypes.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.module.screeningtypes.controller.dto.BlacklistScreeningTypesRequest
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesService
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController

@Tag(name = "Manager Screening Types")
@RestController
@RequestMapping("/manager-app/screening-types")
class AdminScreeningTypesController(
    private val screeningTypesService: ScreeningTypesService,
) {

    @PreAuthorize(Role.MANAGER)
    @PostMapping("/blacklist", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun blacklistScreeningTypes(@RequestBody request: BlacklistScreeningTypesRequest) {
        screeningTypesService.blacklistScreeningTypes(request.toCommand())
    }
}
