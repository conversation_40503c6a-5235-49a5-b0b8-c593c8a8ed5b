package com.cleevio.cinemax.api.module.receipt.entity

import com.cleevio.cinemax.api.common.entity.UpdatableNonAuditableEntity
import com.cleevio.cinemax.api.module.receipt.constant.ReceiptPrintState
import com.cleevio.cinemax.api.module.receipt.constant.ReceiptType
import jakarta.persistence.Column
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.util.UUID
import jakarta.persistence.Entity as JpaEntity

@JpaEntity
class Receipt(
    id: UUID = UUID.randomUUID(),

    @Column(name = "basket_id", nullable = false)
    var basketId: UUID,

    @Column(name = "directory", nullable = false)
    var directory: String,

    @Column(name = "content", nullable = false)
    var content: String,

    @Column(name = "last_print_state")
    @Enumerated(EnumType.STRING)
    var lastPrintState: ReceiptPrintState?,

    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    var type: ReceiptType,

) : UpdatableNonAuditableEntity(id)
