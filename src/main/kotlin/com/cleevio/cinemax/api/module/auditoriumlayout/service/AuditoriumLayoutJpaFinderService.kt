package com.cleevio.cinemax.api.module.auditoriumlayout.service

import com.cleevio.cinemax.api.module.auditorium.exception.AuditoriumWithLayoutNotFoundException
import com.cleevio.cinemax.api.module.auditoriumlayout.entity.AuditoriumLayout
import com.cleevio.cinemax.api.module.auditoriumlayout.exception.AuditoriumLayoutNotFoundException
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class AuditoriumLayoutJpaFinderService(
    private val repository: AuditoriumLayoutRepository,
) {

    fun findByOriginalId(originalId: Int): AuditoriumLayout? = repository.findByOriginalId(originalId)

    fun findNonDeletedByOriginalId(originalId: Int): AuditoriumLayout? =
        repository.findByOriginalIdAndDeletedAtIsNull(originalId)

    fun existsNonDeletedByIdAndIsNotDefault(id: UUID): Boolean =
        repository.existsByIdAndCodeIsNotAndDeletedAtIsNull(id = id, code = DEFAULT_AUDITORIUM_LAYOUT_CODE)

    fun existsNonDeletedById(id: UUID): Boolean = repository.existsByIdAndDeletedAtIsNull(id)

    fun getNonDeletedById(id: UUID): AuditoriumLayout = repository.findByIdAndDeletedAtIsNull(id)
        ?: throw AuditoriumLayoutNotFoundException()

    fun getDefaultByAuditoriumId(auditoriumId: UUID): AuditoriumLayout =
        repository.findByAuditoriumIdAndCodeAndDeletedAtIsNull(
            auditoriumId = auditoriumId,
            code = DEFAULT_AUDITORIUM_LAYOUT_CODE
        ) ?: throw AuditoriumLayoutNotFoundException()

    fun getNonDeletedByAuditoriumLayoutIdAndAuditoriumId(
        auditoriumLayoutId: UUID,
        auditoriumId: UUID,
    ): AuditoriumLayout = repository.findByIdAndAuditoriumIdAndDeletedAtIsNull(
        id = auditoriumLayoutId,
        auditoriumId = auditoriumId
    ) ?: throw AuditoriumWithLayoutNotFoundException()
}

const val DEFAULT_AUDITORIUM_LAYOUT_CODE = "01"
