package com.cleevio.cinemax.api.module.auditoriumlayout.service

import com.cleevio.cinemax.api.common.constant.AUDITORIUM_LAYOUT
import com.cleevio.cinemax.api.common.service.Action
import com.cleevio.cinemax.api.common.util.ifFalse
import com.cleevio.cinemax.api.common.util.ifTrue
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.auditorium.exception.AuditoriumNotFoundException
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumJpaFinderService
import com.cleevio.cinemax.api.module.auditoriumlayout.constant.AuditoriumLayoutLockValues.CREATE_OR_UPDATE_AUDITORIUM_LAYOUT
import com.cleevio.cinemax.api.module.auditoriumlayout.constant.AuditoriumLayoutLockValues.DELETE_AUDITORIUM_LAYOUT
import com.cleevio.cinemax.api.module.auditoriumlayout.entity.AuditoriumLayout
import com.cleevio.cinemax.api.module.auditoriumlayout.exception.AuditoriumLayoutAlreadyExistsException
import com.cleevio.cinemax.api.module.auditoriumlayout.service.command.CreateOrUpdateAuditoriumLayoutCommand
import com.cleevio.cinemax.api.module.auditoriumlayout.service.command.DeleteAuditoriumLayoutCommand
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.module.seat.service.command.CopyAuditoriumLayoutSeatsCommand
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import com.cleevio.library.lockinghandler.service.LockFieldParameters
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class AuditoriumLayoutService(
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val auditoriumLayoutJpaFinderService: AuditoriumLayoutJpaFinderService,
    private val auditoriumJpaFinderService: AuditoriumJpaFinderService,
    private val seatService: SeatService,
) {
    private val log = logger()

    @Transactional
    @Lock(AUDITORIUM_LAYOUT, CREATE_OR_UPDATE_AUDITORIUM_LAYOUT)
    fun syncCreateOrUpdateAuditoriumLayout(
        @Valid
        @LockFieldParameter("originalId")
        command: CreateOrUpdateAuditoriumLayoutCommand,
    ) {
        requireNotNull(command.originalId) { "Attribute 'originalId' must not be null." }

        val auditoriumLayout = auditoriumLayoutJpaFinderService.findByOriginalId(command.originalId)
        if (auditoriumLayout != null && auditoriumLayout.isDeleted()) {
            log.warn("Auditorium layout with originalId ${auditoriumLayout.originalId} has been already deleted. Skipping.")
            return
        }

        internalCreateOrUpdate(
            auditoriumLayout = auditoriumLayout,
            command = command
        )
    }

    @Transactional
    @Lock(AUDITORIUM_LAYOUT, CREATE_OR_UPDATE_AUDITORIUM_LAYOUT)
    fun adminCreateOrUpdateAuditoriumLayout(
        @Valid
        @LockFieldParameters([LockFieldParameter("auditoriumId"), LockFieldParameter("code")])
        command: CreateOrUpdateAuditoriumLayoutCommand,
    ): UUID = internalCreateOrUpdate(
        auditoriumLayout = command.id?.let { auditoriumLayoutJpaFinderService.getNonDeletedById(it) },
        command = command
    ).also {
        if (command.getAction() == Action.CREATE) {
            seatService.copyAuditoriumLayoutSeats(
                CopyAuditoriumLayoutSeatsCommand(
                    sourceAuditoriumLayoutId = auditoriumLayoutJpaFinderService.getDefaultByAuditoriumId(command.auditoriumId).id,
                    newAuditoriumLayoutId = it
                )
            )
        }
    }

    @Transactional
    @Lock(AUDITORIUM_LAYOUT, DELETE_AUDITORIUM_LAYOUT)
    fun deleteAuditoriumLayout(
        @Valid
        @LockFieldParameter("auditoriumLayoutId")
        command: DeleteAuditoriumLayoutCommand,
    ) {
        auditoriumLayoutJpaFinderService.getNonDeletedByAuditoriumLayoutIdAndAuditoriumId(
            auditoriumLayoutId = command.auditoriumLayoutId,
            auditoriumId = command.auditoriumId
        ).let {
            auditoriumLayoutRepository.save(
                it.apply { markDeleted() }
            )
        }
    }

    private fun internalCreateOrUpdate(
        auditoriumLayout: AuditoriumLayout?,
        command: CreateOrUpdateAuditoriumLayoutCommand,
    ): UUID {
        auditoriumJpaFinderService.existsById(command.auditoriumId).ifFalse {
            throw AuditoriumNotFoundException()
        }

        auditoriumLayoutRepository.existsNonDeletedByCodeAndAuditoriumIdAndIdDiffersOrIsNull(
            code = command.code,
            auditoriumId = command.auditoriumId,
            id = auditoriumLayout?.id
        ).ifTrue {
            throw AuditoriumLayoutAlreadyExistsException()
        }

        return auditoriumLayout?.let {
            auditoriumLayoutRepository.save(mapToExistingAuditoriumLayout(it, command)).id
        } ?: run {
            auditoriumLayoutRepository.save(mapToNewAuditoriumLayout(command)).id
        }
    }

    private fun mapToNewAuditoriumLayout(
        command: CreateOrUpdateAuditoriumLayoutCommand,
    ) = AuditoriumLayout(
        id = command.id ?: UUID.randomUUID(),
        originalId = command.originalId,
        auditoriumId = command.auditoriumId,
        code = command.code,
        title = command.title
    )

    private fun mapToExistingAuditoriumLayout(
        auditoriumLayout: AuditoriumLayout,
        command: CreateOrUpdateAuditoriumLayoutCommand,
    ) = auditoriumLayout.apply {
        originalId = command.originalId
        auditoriumId = command.auditoriumId
        code = command.code
        title = command.title
    }
}
