package com.cleevio.cinemax.api.module.ticketdiscount.service

import com.cleevio.cinemax.api.common.constant.TICKET_DISCOUNT
import com.cleevio.cinemax.api.common.service.CodeGenerator
import com.cleevio.cinemax.api.common.util.ifTrue
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardJpaFinderService
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountLockValues.CREATE_OR_UPDATE_TICKET_DISCOUNT
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountLockValues.DELETE_TICKET_DISCOUNT
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.entity.TicketDiscount
import com.cleevio.cinemax.api.module.ticketdiscount.event.TicketDiscountCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.ticketdiscount.event.toMessagingDeleteEvent
import com.cleevio.cinemax.api.module.ticketdiscount.event.toMessagingEvent
import com.cleevio.cinemax.api.module.ticketdiscount.exception.DiscountCardForTicketDiscountExistsException
import com.cleevio.cinemax.api.module.ticketdiscount.exception.InvalidInputParamCombinationException
import com.cleevio.cinemax.api.module.ticketdiscount.exception.TicketDiscountCodeAlreadyExistsException
import com.cleevio.cinemax.api.module.ticketdiscount.service.command.CreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.module.ticketdiscount.service.command.DeleteTicketDiscountCommand
import com.cleevio.cinemax.api.module.ticketdiscount.service.command.MessagingCreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.module.ticketdiscount.service.command.MessagingDeleteTicketDiscountCommand
import com.cleevio.cinemax.api.module.ticketdiscount.service.command.UpdateTicketDiscountOriginalIdCommand
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import jakarta.validation.Valid
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.math.BigDecimal
import java.util.UUID

@Service
@Validated
class TicketDiscountService(
    private val ticketDiscountRepository: TicketDiscountRepository,
    private val ticketDiscountJpaFinderService: TicketDiscountJpaFinderService,
    private val discountCardJpaFinderService: DiscountCardJpaFinderService,
    private val codeGenerator: CodeGenerator,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    private val log = logger()

    @Transactional
    @Lock(TICKET_DISCOUNT, CREATE_OR_UPDATE_TICKET_DISCOUNT)
    fun syncCreateOrUpdateTicketDiscount(
        @Valid
        @LockFieldParameter("originalId")
        command: CreateOrUpdateTicketDiscountCommand,
    ) {
        requireNotNull(command.originalId) { "Attribute 'originalId' must not be null." }
        requireNotNull(command.code) { "Attribute 'code' must not be null." }

        val ticketDiscount = ticketDiscountRepository.findByOriginalId(command.originalId)
        if (ticketDiscount != null && ticketDiscount.isDeleted()) {
            log.warn("Ticket discount with originalId ${ticketDiscount.originalId} has been already deleted. Skipping.")
            return
        }
        ticketDiscount?.let {
            if (command.code != it.code) {
                log.error("Attribute code cannot be updated when syncing entity from MSSQL.")
                return
            }
        }
        ticketDiscountRepository.existsNonDeletedByCodeAndIdDiffersOrIsNull(
            code = command.code,
            id = ticketDiscount?.id
        ).ifTrue { throw TicketDiscountCodeAlreadyExistsException() }

        internalCreateOrUpdateTicketDiscount(
            ticketDiscount = ticketDiscount,
            command = command
        )
    }

    @Transactional
    @Lock(TICKET_DISCOUNT, CREATE_OR_UPDATE_TICKET_DISCOUNT)
    fun adminCreateOrUpdateTicketDiscount(
        @Valid
        @LockFieldParameter("id")
        command: CreateOrUpdateTicketDiscountCommand,
    ): UUID {
        validateAdminCreateOrUpdateCommand(
            type = command.type,
            amount = command.amount,
            percentage = command.percentage
        )

        return internalCreateOrUpdateTicketDiscount(
            ticketDiscount = command.id?.let { ticketDiscountJpaFinderService.getNonDeletedById(it) },
            command = command
        ).also {
            applicationEventPublisher.publishEvent(TicketDiscountCreatedOrUpdatedEvent(it.id))
            applicationEventPublisher.publishEvent(it.toMessagingEvent())
        }.id
    }

    @Transactional
    @Lock(TICKET_DISCOUNT, DELETE_TICKET_DISCOUNT)
    fun deleteTicketDiscount(
        @Valid
        @LockFieldParameter("ticketDiscountId")
        command: DeleteTicketDiscountCommand,
    ) {
        ticketDiscountJpaFinderService.getNonDeletedById(command.ticketDiscountId).let {
            if (discountCardJpaFinderService.existsByTicketDiscountId(command.ticketDiscountId)) {
                throw DiscountCardForTicketDiscountExistsException()
            }
            ticketDiscountRepository.save(
                it.apply { markDeleted() }
            ).also { deleted ->
                applicationEventPublisher.publishEvent(deleted.toMessagingDeleteEvent())
            }
        }
    }

    @Transactional
    @Lock(TICKET_DISCOUNT, CREATE_OR_UPDATE_TICKET_DISCOUNT)
    fun updateTicketDiscountOriginalId(
        @Valid
        @LockFieldParameter("ticketDiscountId")
        command: UpdateTicketDiscountOriginalIdCommand,
    ) {
        ticketDiscountJpaFinderService.getNonDeletedById(command.ticketDiscountId).also {
            ticketDiscountRepository.save(
                it.apply {
                    originalId = command.originalId
                }
            )
        }
    }

    @Transactional
    @Lock(TICKET_DISCOUNT, CREATE_OR_UPDATE_TICKET_DISCOUNT)
    fun messagingCreateOrUpdateTicketDiscount(
        @Valid
        @LockFieldParameter("code")
        command: MessagingCreateOrUpdateTicketDiscountCommand,
    ) {
        validateAdminCreateOrUpdateCommand(
            type = command.type,
            amount = command.amount,
            percentage = command.percentage
        )

        val ticketDiscount = ticketDiscountJpaFinderService.findNonDeletedByCode(command.code)?.let {
            ticketDiscountRepository.save(mapToExistingMessagingTicketDiscount(it, command))
        } ?: run {
            ticketDiscountRepository.save(mapToNewMessagingTicketDiscount(command))
        }

        applicationEventPublisher.publishEvent(TicketDiscountCreatedOrUpdatedEvent(ticketDiscount.id))
    }

    @Transactional
    @Lock(TICKET_DISCOUNT, CREATE_OR_UPDATE_TICKET_DISCOUNT)
    fun messagingDeleteTicketDiscount(
        @Valid
        @LockFieldParameter("code")
        command: MessagingDeleteTicketDiscountCommand,
    ) {
        val ticketDiscount = ticketDiscountJpaFinderService.getNonDeletedByCode(command.code)

        if (discountCardJpaFinderService.existsByTicketDiscountId(ticketDiscount.id)) {
            throw DiscountCardForTicketDiscountExistsException()
        }

        ticketDiscountRepository.save(ticketDiscount.apply { markDeleted() })
    }

    private fun internalCreateOrUpdateTicketDiscount(
        ticketDiscount: TicketDiscount?,
        command: CreateOrUpdateTicketDiscountCommand,
    ): TicketDiscount = ticketDiscount?.let {
        return ticketDiscountRepository.save(mapToExistingTicketDiscount(it, command))
    } ?: run {
        return ticketDiscountRepository.save(mapToNewTicketDiscount(command))
    }

    private fun mapToExistingTicketDiscount(
        ticketDiscount: TicketDiscount,
        command: CreateOrUpdateTicketDiscountCommand,
    ) = ticketDiscount.apply {
        title = command.title
        type = command.type
        usageType = command.usageType
        amount = command.amount
        percentage = command.percentage
        applicableToCount = command.applicableToCount
        freeCount = command.freeCount
        zeroFees = command.zeroFees
        voucherOnly = command.voucherOnly
        active = command.active
        order = command.order
    }

    private fun mapToNewTicketDiscount(command: CreateOrUpdateTicketDiscountCommand) = TicketDiscount(
        id = command.id ?: UUID.randomUUID(),
        originalId = command.originalId,
        code = command.code ?: codeGenerator.generateCode(
            TicketDiscount::class.java,
            ticketDiscountRepository::existsByCodeAndDeletedAtIsNull
        ),
        title = command.title,
        type = command.type,
        usageType = command.usageType,
        amount = command.amount,
        percentage = command.percentage,
        applicableToCount = command.applicableToCount,
        freeCount = command.freeCount,
        zeroFees = command.zeroFees,
        voucherOnly = command.voucherOnly,
        active = command.active,
        order = command.order
    )

    private fun mapToNewMessagingTicketDiscount(
        command: MessagingCreateOrUpdateTicketDiscountCommand,
    ) = TicketDiscount(
        code = command.code,
        title = command.title,
        type = command.type,
        usageType = command.usageType,
        amount = command.amount,
        percentage = command.percentage,
        applicableToCount = command.applicableToCount,
        freeCount = command.freeCount,
        zeroFees = command.zeroFees,
        voucherOnly = command.voucherOnly,
        active = command.active,
        order = command.order
    )

    private fun mapToExistingMessagingTicketDiscount(
        ticketDiscount: TicketDiscount,
        command: MessagingCreateOrUpdateTicketDiscountCommand,
    ) = ticketDiscount.apply {
        code = command.code
        title = command.title
        type = command.type
        usageType = command.usageType
        amount = command.amount
        percentage = command.percentage
        applicableToCount = command.applicableToCount
        freeCount = command.freeCount
        zeroFees = command.zeroFees
        voucherOnly = command.voucherOnly
        active = command.active
        order = command.order
    }

    private fun validateAdminCreateOrUpdateCommand(type: TicketDiscountType, amount: BigDecimal?, percentage: Int?) {
        if (
            type == TicketDiscountType.ABSOLUTE && amount == null ||
            type == TicketDiscountType.PERCENTAGE && percentage == null ||
            !isCommonCommandOrValidFixedPriceCommand(amount, percentage, type)
        ) {
            throw InvalidInputParamCombinationException()
        }
    }

    private fun isCommonCommandOrValidFixedPriceCommand(
        amount: BigDecimal?,
        percentage: Int?,
        type: TicketDiscountType,
    ): Boolean {
        return if (amount != null && percentage != null) {
            type == TicketDiscountType.ABSOLUTE && percentage == 100
        } else {
            true
        }
    }
}
