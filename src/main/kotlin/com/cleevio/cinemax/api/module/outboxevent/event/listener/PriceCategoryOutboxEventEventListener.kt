package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import com.cleevio.cinemax.api.module.pricecategory.event.listener.PriceCategoryCreatedOrUpdatedEvent
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    "features.outbox-event.price-category.listener.enabled",
    havingValue = "true",
    matchIfMissing = true
)
class PriceCategoryOutboxEventEventListener(
    private val outboxEventService: OutboxEventService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToPriceCategoryCreatedOrUpdatedEvent(event: PriceCategoryCreatedOrUpdatedEvent) {
        outboxEventService.createOutboxEvent(
            CreateOutboxEventCommand(
                entityId = event.priceCategoryId,
                type = OutboxEventType.PRICE_CATEGORY_CREATED_OR_UPDATED
            )
        )
    }
}
