package com.cleevio.cinemax.api.module.screeningfee.service

import com.cleevio.cinemax.api.module.screeningfee.entity.ScreeningFee
import com.cleevio.cinemax.api.module.screeningfee.exception.ScreeningFeeNotFoundException
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class ScreeningFeeFinderService(
    private val screeningFeeFinderRepository: ScreeningFeeFinderRepository,
) {

    fun findAll(): List<ScreeningFee> {
        return screeningFeeFinderRepository.findAll()
    }

    fun findById(id: UUID): ScreeningFee? {
        return screeningFeeFinderRepository.findById(id)
    }

    fun getById(id: UUID): ScreeningFee {
        return findById(id) ?: throw ScreeningFeeNotFoundException()
    }

    fun findByScreeningId(screeningId: UUID): ScreeningFee? {
        return screeningFeeFinderRepository.findByScreeningId(screeningId)
    }

    fun findAllByScreeningIdIn(screeningIds: Set<UUID>): List<ScreeningFee> {
        return screeningFeeFinderRepository.findAllByScreeningIdIn(screeningIds)
    }
}
