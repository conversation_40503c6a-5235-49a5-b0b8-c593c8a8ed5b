package com.cleevio.cinemax.api.module.reservation.service

import com.cleevio.cinemax.api.common.service.JooqFinderRepository
import com.cleevio.cinemax.api.module.reservation.entity.Reservation
import com.cleevio.cinemax.psql.tables.Reservation.RESERVATION
import org.jooq.DSLContext
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
class ReservationFinderRepository(
    private val psqlDslContext: DSLContext,
) : JooqFinderRepository<Reservation> {

    override fun findAll(): List<Reservation> {
        return psqlDslContext
            .selectFrom(RESERVATION)
            .fetchInto(Reservation::class.java)
    }

    override fun findById(id: UUID): Reservation? {
        return psqlDslContext
            .selectFrom(RESERVATION)
            .where(RESERVATION.ID.eq(id))
            .fetchOneInto(Reservation::class.java)
    }

    // TODO: until CMX-576 is fixed I'm using the limit to mitigate effects of CMX-680
    fun findLatestByOriginalId(originalId: Int): Reservation? {
        return psqlDslContext
            .selectFrom(RESERVATION)
            .where(RESERVATION.ORIGINAL_ID.eq(originalId))
            .orderBy(RESERVATION.UPDATED_AT.desc())
            .fetchAnyInto(Reservation::class.java)
    }

    fun findAllNonDeleted(): List<Reservation> {
        return psqlDslContext
            .selectFrom(RESERVATION)
            .where(RESERVATION.DELETED_AT.isNull)
            .fetchInto(Reservation::class.java)
    }

    fun findNonDeletedById(id: UUID): Reservation? {
        return psqlDslContext
            .selectFrom(RESERVATION)
            .where(RESERVATION.DELETED_AT.isNull)
            .and(RESERVATION.ID.eq(id))
            .fetchOneInto(Reservation::class.java)
    }

    fun findLatestByScreeningAndSeatId(screeningId: UUID, seatId: UUID): Reservation? {
        return psqlDslContext
            .selectFrom(RESERVATION)
            .where(RESERVATION.DELETED_AT.isNull)
            .and(RESERVATION.SCREENING_ID.eq(screeningId))
            .and(RESERVATION.SEAT_ID.eq(seatId))
            .orderBy(RESERVATION.UPDATED_AT.desc())
            .fetchAnyInto(Reservation::class.java)
    }

    fun findAllByScreeningIdIn(screeningIds: Set<UUID>): List<Reservation> {
        return psqlDslContext
            .selectFrom(RESERVATION)
            .where(RESERVATION.SCREENING_ID.`in`(screeningIds))
            .fetchInto(Reservation::class.java)
    }

    fun findAllNonDeletedByIdIn(ids: Set<UUID>): List<Reservation> {
        return psqlDslContext
            .selectFrom(RESERVATION)
            .where(RESERVATION.DELETED_AT.isNull)
            .and(RESERVATION.ID.`in`(ids))
            .fetchInto(Reservation::class.java)
    }
}
