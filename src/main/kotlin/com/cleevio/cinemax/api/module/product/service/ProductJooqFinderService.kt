package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.common.util.isGreaterThanOrEqual
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.exception.ProductNotFoundException
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.UUID

@Service
class ProductJooqFinderService(
    private val productFinderRepository: ProductJooqFinderRepository,
) {

    fun findAllNonDeleted(): List<Product> {
        return productFinderRepository.findAllNonDeleted()
    }

    fun findNonDeletedById(id: UUID): Product? {
        return productFinderRepository.findNonDeletedById(id)
    }

    fun getNonDeletedById(id: UUID): Product {
        return findNonDeletedById(id) ?: throw ProductNotFoundException()
    }

    fun findNonDeletedByOriginalId(originalId: Int): Product? {
        return productFinderRepository.findNonDeletedByOriginalId(originalId)
    }

    fun findNonDeletedByOriginalCode(originalCode: String): Product? {
        return productFinderRepository.findNonDeletedByOriginalCode(originalCode)
    }

    fun findAllNonDeletedByIdIn(ids: Set<UUID>): List<Product> {
        return productFinderRepository.findAllNonDeletedByIdIn(ids)
    }

    fun findNonDeletedByTitleAndSoldInBuffet(title: String): Product? {
        return productFinderRepository.findNonDeletedByTitleAndSoldInBuffet(title)
    }

    fun findNonDeletedAndActiveByCategoryIdInAndProductModeIn(
        categoryIds: Set<UUID>,
        productModes: List<ProductMode>?,
    ): List<Product> {
        return productFinderRepository.findNonDeletedAndActiveByCategoryIdInAndProductModeIn(categoryIds, productModes)
    }

    fun findAvailableBuffetProductsByCategoryIds(categoryIds: Set<UUID>): List<Product> {
        val products = findNonDeletedAndActiveByCategoryIdInAndProductModeIn(categoryIds, listOf(ProductMode.STANDARD))
        val productIdToStockQuantity = findAllNonDeletedActiveProductStockQuantitiesByIdIn(products.mapToSet { it.id })
        return products.filter {
            it.originalId?.let { originalId ->
                if (originalId > 0) {
                    // i.e. product is synced from rmenu (product) not rzbozir (additional sale)
                    // special condition to filter out product discounts that have zero stock quantity
                    productIdToStockQuantity[it.id].isGreaterThanOrEqual(BigDecimal.ONE)
                } else {
                    true
                }
            } ?: true
        }
    }

    fun findAllNonDeletedActiveProductStockQuantitiesByIdIn(ids: Set<UUID>): Map<UUID, BigDecimal> {
        return productFinderRepository.findAllNonDeletedActiveProductStockQuantitiesByIdIn(ids)
    }

    fun existsNonDeletedByIdAndStockQuantityGreaterThanOrEqualOne(id: UUID): Boolean {
        return productFinderRepository.existsNonDeletedByIdAndStockQuantityGreaterThanOrEqualOne(id)
    }

    fun findRelatedPackagingDepositProduct(productId: UUID): Pair<UUID, BigDecimal>? {
        return productFinderRepository.findRelatedPackagingDeposits(productId)?.let {
            if (it.size != 1) error("More than one product as a packaging deposit in DB.")
            it.first()
        }
    }

    fun validateExistsAndIsPackagingDeposit(productId: UUID): Product {
        return getNonDeletedById(productId).also {
            if (!it.isPackagingDeposit) error("Product id ${it.id} is not packaging deposit.")
        }
    }
}
