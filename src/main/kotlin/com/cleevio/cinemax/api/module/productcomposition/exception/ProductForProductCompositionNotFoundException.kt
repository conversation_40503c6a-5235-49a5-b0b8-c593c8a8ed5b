package com.cleevio.cinemax.api.module.productcomposition.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
class ProductForProductCompositionNotFoundException : ApiException(
    Module.PRODUCT_COMPOSITION,
    ProductCompositionErrorType.PRODUCT_FOR_PRODUCT_COMPOSITION_NOT_FOUND
)
