package com.cleevio.cinemax.api.module.basketitem.controller.dto

import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal

data class AdminSearchProductBasketItemsSummaryResponse(
    val sales: SalesResponse,
) {

    @Schema(name = "AdminSearchProductBasketItemsSummarySalesResponse")
    data class SalesResponse(
        val productsCount: Int,
        val basketsCount: Int,
        val purchasePriceSum: BigDecimal,
        val salesCash: BigDecimal,
        val salesCashless: BigDecimal,
        val grossSales: BigDecimal,
        val netSales: BigDecimal,
        val taxAmount: BigDecimal,
        val grossRevenue: BigDecimal,
        val netRevenue: BigDecimal,
        val cancelledProductsCount: Int,
        val cancelledProductsExpense: BigDecimal,
    )
}
