package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.file.service.FileJpaFinderService
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.service.ProductJpaFinderService
import com.cleevio.cinemax.api.module.product.service.ProductMssqlFinderRepository
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.product.service.command.UpdateProductOriginalIdCommand
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJpaFinderService
import com.cleevio.cinemax.api.module.productcomponent.entity.ProductComponent
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentJpaFinderService
import com.cleevio.cinemax.api.module.productcomposition.entity.ProductComposition
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionJpaFinderService
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.productcomposition.service.command.UpdateProductCompositionOriginalIdCommand
import com.cleevio.cinemax.mssql.rp_bufete.dbo.Tables.RMENU
import com.cleevio.cinemax.mssql.rp_bufete.dbo.Tables.RPOLM
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.records.RmenuRecord
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.records.RpolmRecord
import org.jooq.DSLContext
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime
import java.util.UUID
import kotlin.math.abs

@Service
class ProductCreatedOrUpdatedEventProcessor(
    private val mssqlBuffetDslContext: DSLContext,
    private val productJpaFinderService: ProductJpaFinderService,
    private val productCompositionService: ProductCompositionService,
    private val productCompositionJpaFinderService: ProductCompositionJpaFinderService,
    private val productComponentJpaFinderService: ProductComponentJpaFinderService,
    private val productMssqlFinderRepository: ProductMssqlFinderRepository,
    private val productService: ProductService,
    private val productCategoryJpaFinderService: ProductCategoryJpaFinderService,
    private val fileJpaFinderService: FileJpaFinderService,
) {
    private val log = logger()

    @Transactional(transactionManager = "mssqlBuffetTransactionManager")
    fun process(event: OutboxEvent): Int {
        log.info("Starting to process OutboxEvent=$event.")

        return runCatching {
            val product = productJpaFinderService.findNonDeletedById(event.entityId)
                ?: error("Product with id=${event.entityId} not found.")

            product.originalId?.let {
                if (!productMssqlFinderRepository.existsByOriginalId(it)) {
                    error("MSSQL rmenu record with originalId $it not found.")
                }
                updateMssqlProductRecord(product)
                deleteAndCreateMssqlProductCompositions(product, it)
            } ?: run {
                createMssqlProductRecord(product)?.let { originalId ->
                    productService.updateProductOriginalId(
                        UpdateProductOriginalIdCommand(
                            code = product.code,
                            originalId = originalId
                        )
                    )
                    deleteAndCreateMssqlProductCompositions(product, originalId)
                } ?: run {
                    error("MSSQL rmenu record was not created or its originalId could not be fetched.")
                }
            }
            1
        }.onFailure { ex ->
            log.error("Failed to process OutboxEvent: ${ex.message}", ex)
        }.getOrDefault(0)
    }

    private fun createMssqlProductRecord(product: Product): Int? = mssqlBuffetDslContext.insertInto(RMENU)
        .set(prepareMssqlProductRecord(product))
        .returningResult(RMENU.RMENUID)
        .fetchOneInto(Int::class.java)

    private fun updateMssqlProductRecord(product: Product): Int = mssqlBuffetDslContext
        .update(RMENU)
        .set(prepareMssqlProductRecord(product))
        .where(RMENU.RMENUID.eq(product.originalId))
        .execute()

    private fun prepareMssqlProductRecord(product: Product): RmenuRecord = mssqlBuffetDslContext.newRecord(RMENU).also {
        val productCategory = product.productCategoryId.let { productCategoryId ->
            productCategoryJpaFinderService.getNonDeletedById(productCategoryId)
        }
        val imageFile = product.imageFileId?.let { imageFileId -> fileJpaFinderService.findById(imageFileId) }

        it.cislo = product.code
        it.nazev = product.title
        it.druh = productCategory.code
        it.cena = product.priceNoVat
        it.aktivni = product.active
        it.cenacelk = product.price
        it.lcelk = product.type == ProductType.PRODUCT
        it.poraditablet = product.tabletOrder ?: 0
        it.poradiprodej = product.order ?: 0
        it.lbufet = product.soldInBuffet
        it.lbar = product.soldInCafe
        it.ltablet = product.soldInVip
        it.obrazek = imageFile?.let { file -> file.originalName ?: file.getFilename() } ?: ""
        it.lzbozi = productCompositionJpaFinderService.existsSoleCompositionForProduct(product.id)
        it.minim = product.stockQuantityThreshold?.toShort() ?: 0
        it.tabletne = !product.soldInVip
        it.zuziv = product.updatedBy
        it.zcas = LocalDateTime.now()

        // unused columns need to be set with default values
        it.carkod = ""
        it.rezervace = 0
        it.sklad = ""
        it.poznamka = null
        it.nazev2 = ""
    }

    private fun deleteAndCreateMssqlProductCompositions(product: Product, productOriginalId: Int) {
        deleteMssqlProductCompositions(productOriginalId)

        val productCompositions = productCompositionJpaFinderService.findAllByProductId(product.id)
        val productComponentIdToProductComponent = productComponentJpaFinderService.findAllNonDeletedByIdIn(
            productCompositions.mapNotNull { pcs -> pcs.productComponentId }.toSet()
        ).associateBy { component -> component.id }
        val productInProductIdToProduct = productJpaFinderService.findAllNonDeletedByIdIn(
            productCompositions.mapNotNull { pcs -> pcs.productInProductId }.toSet()
        ).associateBy { productInProduct -> productInProduct.id }

        productCompositions
            .forEach { composition ->
                val productComponent = composition.productComponentId?.let { productComponentId ->
                    productComponentIdToProductComponent[productComponentId]
                }
                val productInProduct = composition.productInProductId?.let { productInProductId ->
                    productInProductIdToProduct[productInProductId]
                }

                createMssqlProductComposition(
                    product = product,
                    productOriginalId = productOriginalId,
                    productComponent = productComponent,
                    productInProduct = productInProduct,
                    productComposition = composition
                )?.let { originalId ->
                    productCompositionService.updateProductCompositionOriginalId(
                        UpdateProductCompositionOriginalIdCommand(
                            productCompositionId = composition.id,
                            originalId = originalId,
                            productId = product.id
                        )
                    )
                } ?: error("MSSQL rpolm record was not created or its originalId could not be fetched.")
            }
    }

    private fun deleteMssqlProductCompositions(productOriginalId: Int) = mssqlBuffetDslContext
        .deleteFrom(RPOLM)
        .where(RPOLM.RMENUID.eq(productOriginalId))
        .execute()

    private fun createMssqlProductComposition(
        product: Product,
        productOriginalId: Int,
        productComponent: ProductComponent?,
        productInProduct: Product?,
        productComposition: ProductComposition,
    ): Int? {
        if (productComponent == null && productInProduct == null) {
            error("No productComponent or productInProduct found for productComposition id=${productComposition.id}, skipping.")
        }

        return mssqlBuffetDslContext.insertInto(RPOLM)
            .set(
                prepareMssqlProductComposition(
                    product = product,
                    productOriginalId = productOriginalId,
                    productComponent = productComponent,
                    productInProduct = productInProduct,
                    productComposition = productComposition
                )
            )
            .returningResult(RPOLM.RPOLMID)
            .fetchOneInto(Int::class.java)
    }

    private fun prepareMssqlProductComposition(
        product: Product,
        productOriginalId: Int,
        productComponent: ProductComponent?,
        productInProduct: Product?,
        productComposition: ProductComposition,
    ): RpolmRecord = mssqlBuffetDslContext.newRecord(RPOLM).also {
        it.rmenuid = productOriginalId
        it.rzboziid = mapProductComponentOriginalId(productComponent, productComposition.id)
        it.menu = product.code
        it.zbozi = productComponent?.code ?: ""
        it.mnozs = productComposition.amount.toDouble()
        it.zuziv = product.updatedBy
        it.zcas = LocalDateTime.now()

        if (productInProduct?.isPackagingDeposit == true) {
            it.rzbozirid = abs(mapProductInProductOriginalId(productInProduct, productComposition.id))
        } else {
            it.rmenu2id = mapProductInProductOriginalId(productInProduct, productComposition.id)
        }
    }

    private fun mapProductComponentOriginalId(
        productComponent: ProductComponent?,
        productCompositionId: UUID,
    ): Int = productComponent?.let {
        it.originalId ?: error(
            "Product component id=${productComponent.id} have null originalId when syncing product composition " +
                "id=$productCompositionId to MSSQL."
        )
    } ?: 0

    private fun mapProductInProductOriginalId(
        productInProduct: Product?,
        productCompositionId: UUID,
    ): Int = productInProduct?.let {
        it.originalId ?: error(
            "Product id=${productInProduct.id} (product in product) have null originalId when syncing product composition " +
                "id=$productCompositionId to MSSQL."
        )
    } ?: 0
}
