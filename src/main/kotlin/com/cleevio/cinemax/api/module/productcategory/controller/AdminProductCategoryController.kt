package com.cleevio.cinemax.api.module.productcategory.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.common.dto.SimplePage
import com.cleevio.cinemax.api.common.dto.toSimplePage
import com.cleevio.cinemax.api.module.productcategory.controller.dto.AdminSearchProductCategoriesResponse
import com.cleevio.cinemax.api.module.productcategory.controller.dto.CreateProductCategoryRequest
import com.cleevio.cinemax.api.module.productcategory.controller.dto.UpdateProductCategoryRequest
import com.cleevio.cinemax.api.module.productcategory.service.AdminSearchProductCategoriesQueryService
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcategory.service.command.DeleteProductCategoryCommand
import com.cleevio.cinemax.api.module.productcategory.service.query.SearchProductCategoriesFilter
import com.cleevio.cinemax.psql.tables.ProductCategoryColumnNames
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.data.domain.Pageable
import org.springframework.data.web.PageableDefault
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Manager Product Categories")
@RestController
@RequestMapping("/manager-app/product-categories")
class AdminProductCategoryController(
    private val productCategoryService: ProductCategoryService,
    private val adminSearchProductCategoriesQueryService: AdminSearchProductCategoriesQueryService,
) {

    @PreAuthorize(Role.MANAGER)
    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun createProductCategory(@RequestBody request: CreateProductCategoryRequest): Unit =
        productCategoryService.adminCreateOrUpdateProductCategory(request.toCommand())

    @PreAuthorize(Role.MANAGER)
    @PutMapping("/{productCategoryId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun updateProductCategory(@PathVariable productCategoryId: UUID, @RequestBody request: UpdateProductCategoryRequest): Unit =
        productCategoryService.adminCreateOrUpdateProductCategory(request.toCommand(productCategoryId))

    @PreAuthorize(Role.MANAGER)
    @DeleteMapping("/{productCategoryId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun deleteProductCategory(
        @PathVariable productCategoryId: UUID,
    ): Unit = productCategoryService.deleteProductCategory(DeleteProductCategoryCommand(productCategoryId))

    @PreAuthorize(Role.MANAGER)
    @PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
    fun searchProductCategories(
        @ParameterObject
        @PageableDefault(sort = [ProductCategoryColumnNames.ORDER, ProductCategoryColumnNames.TITLE])
        pageable: Pageable,
        @RequestBody filter: SearchProductCategoriesFilter,
    ): SimplePage<AdminSearchProductCategoriesResponse> =
        adminSearchProductCategoriesQueryService(filter.toQuery(pageable = pageable)).toSimplePage()
}
