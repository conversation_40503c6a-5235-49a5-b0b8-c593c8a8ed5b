package com.cleevio.cinemax.api.module.screening.controller.dto

import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.seat.constant.DoubleSeatType
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

data class AdminGetScreeningResponse(
    val id: UUID,
    val date: LocalDate,

    @field:Schema(type = "string", pattern = "HH:mm:ss", example = "10:30:00")
    val time: LocalTime,
    val saleTimeLimit: Int,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val movie: MovieGetResponse,
    val auditorium: AuditoriumGetResponse,
    val seats: List<SeatGetResponse>,
)

@Schema(name = "AdminGetScreeningMovieResponse")
data class MovieGetResponse(
    val id: UUID,
    val title: String,
    val rawTitle: String,
)

@Schema(name = "AdminGetScreeningAuditoriumResponse")
data class AuditoriumGetResponse(
    val id: UUID,
    val title: String,
    val code: String,
)

@Schema(name = "AdminGetScreeningSeatResponse")
data class SeatGetResponse(
    val id: UUID,
    val type: SeatType,
    val doubleSeatType: DoubleSeatType?,
    val row: String,
    val number: String,
    val positionLeft: Int,
    val positionTop: Int,
    val reservation: ReservationGetResponse?,
)

@Schema(name = "AdminGetScreeningReservationResponse")
data class ReservationGetResponse(
    val state: ReservationState,
    val groupReservationId: UUID?,
)
