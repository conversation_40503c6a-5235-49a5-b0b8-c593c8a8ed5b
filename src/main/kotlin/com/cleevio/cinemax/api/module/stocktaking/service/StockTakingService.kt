package com.cleevio.cinemax.api.module.stocktaking.service

import com.cleevio.cinemax.api.common.constant.STOCK_TAKING
import com.cleevio.cinemax.api.common.util.ifFalse
import com.cleevio.cinemax.api.common.util.ifNotEmpty
import com.cleevio.cinemax.api.common.util.isWholeNumber
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.entity.ProductComponent
import com.cleevio.cinemax.api.module.productcomponent.exception.ProductComponentNotFoundException
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentJpaFinderService
import com.cleevio.cinemax.api.module.stocktaking.constant.StockTakingLockValues.CREATE_OR_UPDATE_STOCK_TAKING
import com.cleevio.cinemax.api.module.stocktaking.entity.StockTaking
import com.cleevio.cinemax.api.module.stocktaking.exception.InvalidStockTakingValueException
import com.cleevio.cinemax.api.module.stocktaking.exception.ProductComponentsForStockTakingsNotFoundException
import com.cleevio.cinemax.api.module.stocktaking.service.command.AdminCreateStockTakingCommand
import com.cleevio.cinemax.api.module.stocktaking.service.command.AdminCreateStockTakingsCommand
import com.cleevio.cinemax.api.module.stocktaking.service.command.CreateOrUpdateStockTakingCommand
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import com.cleevio.library.lockinghandler.service.LockService
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class StockTakingService(
    private val stockTakingRepository: StockTakingRepository,
    private val productComponentJpaFinderService: ProductComponentJpaFinderService,

    @Suppress("SpringJavaInjectionPointsAutowiringInspection")
    private val lockService: LockService,
) {

    @Transactional
    @Lock(STOCK_TAKING, CREATE_OR_UPDATE_STOCK_TAKING)
    fun syncCreateOrUpdateStockTaking(
        @Valid
        @LockFieldParameter("originalId")
        command: CreateOrUpdateStockTakingCommand,
    ) {
        productComponentJpaFinderService.existsNonDeletedById(command.productComponentId).ifFalse {
            throw ProductComponentNotFoundException()
        }

        stockTakingRepository.findByOriginalId(command.originalId)?.let {
            stockTakingRepository.save(mapToExistingStockTaking(it, command))
        } ?: run {
            stockTakingRepository.save(mapToNewStockTaking(command))
        }
    }

    @Transactional
    @Lock(STOCK_TAKING, CREATE_OR_UPDATE_STOCK_TAKING)
    fun adminCreateStockTaking(
        @Valid
        @LockFieldParameter("productComponentId")
        command: AdminCreateStockTakingCommand,
    ) {
        val productComponent = productComponentJpaFinderService.getNonDeletedById(command.productComponentId)
        if (productComponent.unit == ProductComponentUnit.KS && !command.stockQuantityActual.isWholeNumber()) {
            throw InvalidStockTakingValueException()
        }

        val stockQuantityDifference = command.stockQuantityActual.minus(productComponent.stockQuantity)

        stockTakingRepository.save(
            StockTaking(
                productComponentId = command.productComponentId,
                stockQuantity = productComponent.stockQuantity,
                stockQuantityActual = command.stockQuantityActual,
                stockQuantityDifference = stockQuantityDifference,
                purchasePrice = productComponent.purchasePrice,
                purchasePriceDifference = productComponent.purchasePrice.multiply(stockQuantityDifference)
            )
        )
    }

    @Transactional
    fun adminBatchCreateStockTakings(@Valid command: AdminCreateStockTakingsCommand) {
        val commandProductComponentIds = command.stockTakings.mapToSet { it.productComponentId }
        val productComponentIdToProductComponent =
            productComponentJpaFinderService.findAllNonDeletedByIdIn(commandProductComponentIds).associateBy { it.id }

        validateStockTakings(
            stockTakings = command.stockTakings,
            commandProductComponentIds = commandProductComponentIds,
            productComponentIdToProductComponent = productComponentIdToProductComponent
        )

        command.stockTakings.forEach {
            lockService.obtainBlockingLock(
                module = STOCK_TAKING,
                lockName = CREATE_OR_UPDATE_STOCK_TAKING,
                it.productComponentId.toString()
            ).use { _ ->
                productComponentIdToProductComponent[it.productComponentId]?.let { productComponent ->
                    val stockQuantityDifference = it.stockQuantityActual.minus(productComponent.stockQuantity)
                    stockTakingRepository.save(
                        StockTaking(
                            productComponentId = productComponent.id,
                            stockQuantity = productComponent.stockQuantity,
                            stockQuantityActual = it.stockQuantityActual,
                            stockQuantityDifference = stockQuantityDifference,
                            purchasePrice = productComponent.purchasePrice,
                            purchasePriceDifference = productComponent.purchasePrice.multiply(stockQuantityDifference)
                        )
                    )
                }
            }
        }
    }

    private fun mapToNewStockTaking(command: CreateOrUpdateStockTakingCommand) = StockTaking(
        id = command.id ?: UUID.randomUUID(),
        originalId = command.originalId,
        productComponentId = command.productComponentId,
        stockQuantity = command.stockQuantity,
        stockQuantityActual = command.stockQuantityActual,
        stockQuantityDifference = command.stockQuantityDifference,
        purchasePrice = command.purchasePrice,
        purchasePriceDifference = command.purchasePriceDifference
    )

    private fun mapToExistingStockTaking(stockTaking: StockTaking, command: CreateOrUpdateStockTakingCommand) =
        stockTaking.apply {
            productComponentId = command.productComponentId
            stockQuantity = command.stockQuantity
            stockQuantityActual = command.stockQuantityActual
            stockQuantityDifference = command.stockQuantityDifference
            purchasePrice = command.purchasePrice
            purchasePriceDifference = command.purchasePriceDifference
        }

    private fun validateStockTakings(
        stockTakings: List<AdminCreateStockTakingCommand>,
        commandProductComponentIds: Set<UUID>,
        productComponentIdToProductComponent: Map<UUID, ProductComponent>,
    ) {
        productComponentIdToProductComponent.keys.let {
            commandProductComponentIds.subtract(it).ifNotEmpty {
                throw ProductComponentsForStockTakingsNotFoundException(this)
            }
        }
        stockTakings.forEach {
            val productComponent = productComponentIdToProductComponent[it.productComponentId]
            if (productComponent?.unit == ProductComponentUnit.KS && !it.stockQuantityActual.isWholeNumber()) {
                throw InvalidStockTakingValueException()
            }
        }
    }
}
