package com.cleevio.cinemax.api.module.auditorium.service

import com.cleevio.cinemax.api.module.auditorium.controller.dto.AdminGetAuditoriumResponse
import com.cleevio.cinemax.api.module.auditorium.controller.dto.AuditoriumDefaultResponse
import com.cleevio.cinemax.api.module.auditorium.controller.dto.AuditoriumLayoutResponse
import com.cleevio.cinemax.api.module.auditorium.exception.AuditoriumNotFoundException
import com.cleevio.cinemax.api.module.auditorium.service.query.AdminGetAuditoriumQuery
import com.cleevio.cinemax.psql.Tables.AUDITORIUM
import com.cleevio.cinemax.psql.Tables.AUDITORIUM_DEFAULT
import com.cleevio.cinemax.psql.Tables.AUDITORIUM_LAYOUT
import jakarta.validation.Valid
import org.jooq.DSLContext
import org.jooq.impl.DSL.multiset
import org.jooq.impl.DSL.select
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class AdminGetAuditoriumQueryService(
    private val psqlDslContext: DSLContext,
) {

    operator fun invoke(
        @Valid query: AdminGetAuditoriumQuery,
    ): AdminGetAuditoriumResponse {
        val auditoriumLayouts = multiset(
            select(
                AUDITORIUM_LAYOUT.ID,
                AUDITORIUM_LAYOUT.CODE,
                AUDITORIUM_LAYOUT.TITLE
            )
                .from(AUDITORIUM_LAYOUT)
                .where(AUDITORIUM_LAYOUT.AUDITORIUM_ID.eq(AUDITORIUM.ID))
                .and(AUDITORIUM_LAYOUT.DELETED_AT.isNull)
        )

        return psqlDslContext
            .select(
                AUDITORIUM.ID,
                AUDITORIUM.TITLE,
                AUDITORIUM.ORIGINAL_CODE,
                AUDITORIUM.CITY,
                AUDITORIUM.CAPACITY,
                AUDITORIUM.CREATED_AT,
                AUDITORIUM.UPDATED_AT,
                AUDITORIUM.auditoriumDefault().ID,
                AUDITORIUM.auditoriumDefault().SURCHARGE_VIP,
                AUDITORIUM.auditoriumDefault().SURCHARGE_PREMIUM,
                AUDITORIUM.auditoriumDefault().SURCHARGE_IMAX,
                AUDITORIUM.auditoriumDefault().SURCHARGE_ULTRA_X,
                AUDITORIUM.auditoriumDefault().SERVICE_FEE_VIP,
                AUDITORIUM.auditoriumDefault().SERVICE_FEE_PREMIUM,
                AUDITORIUM.auditoriumDefault().SERVICE_FEE_IMAX,
                AUDITORIUM.auditoriumDefault().SERVICE_FEE_ULTRA_X,
                AUDITORIUM.auditoriumDefault().SURCHARGE_D_BOX,
                AUDITORIUM.auditoriumDefault().PRO_COMMISSION,
                AUDITORIUM.auditoriumDefault().FILM_FOND_COMMISSION,
                AUDITORIUM.auditoriumDefault().DISTRIBUTOR_COMMISSION,
                AUDITORIUM.auditoriumDefault().SALE_TIME_LIMIT,
                AUDITORIUM.auditoriumDefault().PUBLISH_ONLINE,
                auditoriumLayouts
            )
            .from(AUDITORIUM)
            .join(AUDITORIUM.auditoriumDefault())
            .where(AUDITORIUM.ID.eq(query.auditoriumId))
            .fetchOne()
            ?.map {
                AdminGetAuditoriumResponse(
                    id = it[AUDITORIUM.ID],
                    title = it[AUDITORIUM.TITLE],
                    originalCode = it[AUDITORIUM.ORIGINAL_CODE],
                    city = it[AUDITORIUM.CITY],
                    capacity = it[AUDITORIUM.CAPACITY],
                    createdAt = it[AUDITORIUM.CREATED_AT],
                    updatedAt = it[AUDITORIUM.UPDATED_AT],
                    default = AuditoriumDefaultResponse(
                        id = it[AUDITORIUM_DEFAULT.ID],
                        surchargeVip = it[AUDITORIUM_DEFAULT.SURCHARGE_VIP],
                        surchargePremium = it[AUDITORIUM_DEFAULT.SURCHARGE_PREMIUM],
                        surchargeImax = it[AUDITORIUM_DEFAULT.SURCHARGE_IMAX],
                        surchargeUltraX = it[AUDITORIUM_DEFAULT.SURCHARGE_ULTRA_X],
                        surchargeDBox = it[AUDITORIUM_DEFAULT.SURCHARGE_D_BOX],
                        serviceFeeVip = it[AUDITORIUM_DEFAULT.SERVICE_FEE_VIP],
                        serviceFeePremium = it[AUDITORIUM_DEFAULT.SERVICE_FEE_PREMIUM],
                        serviceFeeImax = it[AUDITORIUM_DEFAULT.SERVICE_FEE_IMAX],
                        serviceFeeUltraX = it[AUDITORIUM_DEFAULT.SERVICE_FEE_ULTRA_X],
                        saleTimeLimit = it[AUDITORIUM_DEFAULT.SALE_TIME_LIMIT],
                        proCommission = it[AUDITORIUM_DEFAULT.PRO_COMMISSION],
                        filmFondCommission = it[AUDITORIUM_DEFAULT.FILM_FOND_COMMISSION],
                        distributorCommission = it[AUDITORIUM_DEFAULT.DISTRIBUTOR_COMMISSION],
                        publishOnline = it[AUDITORIUM_DEFAULT.PUBLISH_ONLINE]
                    ),
                    layouts = it[auditoriumLayouts]?.map { layout ->
                        AuditoriumLayoutResponse(
                            id = layout[AUDITORIUM_LAYOUT.ID],
                            code = layout[AUDITORIUM_LAYOUT.CODE],
                            title = layout[AUDITORIUM_LAYOUT.TITLE]
                        )
                    } ?: emptyList()
                )
            } ?: throw AuditoriumNotFoundException()
    }
}
