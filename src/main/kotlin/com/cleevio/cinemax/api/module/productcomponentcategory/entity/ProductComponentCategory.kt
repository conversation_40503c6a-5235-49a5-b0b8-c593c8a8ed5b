package com.cleevio.cinemax.api.module.productcomponentcategory.entity

import com.cleevio.cinemax.api.common.entity.DeletableEntity
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Table
import java.util.UUID

@Entity
@Table(name = "product_component_category")
class ProductComponentCategory(
    id: UUID = UUID.randomUUID(),

    @Column(name = "original_id")
    var originalId: Int?,

    @Column(name = "code", nullable = false)
    var code: String,

    @Column(name = "title", nullable = false)
    var title: String,

    @Column(name = "tax_rate", nullable = false)
    var taxRate: Int,
) : DeletableEntity(id)
