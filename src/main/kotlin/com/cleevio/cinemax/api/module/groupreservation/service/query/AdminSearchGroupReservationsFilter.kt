package com.cleevio.cinemax.api.module.groupreservation.service.query

import org.springframework.data.domain.Pageable
import java.time.LocalDateTime

data class AdminSearchGroupReservationsFilter(
    val screeningDateTimeFrom: LocalDateTime? = null,
    val screeningDateTimeTo: LocalDateTime? = null,
) {
    fun toQuery(pageable: Pageable) = AdminSearchGroupReservationsQuery(pageable = pageable, filter = this)
}
