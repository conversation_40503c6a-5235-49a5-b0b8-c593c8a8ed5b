package com.cleevio.cinemax.api.module.auditoriumlayout.service.command

import com.cleevio.cinemax.api.common.service.CreateOrUpdateCommand
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.util.UUID

data class CreateOrUpdateAuditoriumLayoutCommand(
    override val id: UUID? = null,
    val originalId: Int? = null,
    val auditoriumId: UUID,

    @field:NotBlank
    @field:Size(max = 2)
    val code: String,

    @field:NotBlank
    @field:Size(max = 40)
    val title: String,

) : CreateOrUpdateCommand()
