package com.cleevio.cinemax.api.module.dailyclosingmovement.service

import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemSubtype
import com.cleevio.cinemax.api.module.dailyclosingmovement.entity.DailyClosingMovement
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import java.util.UUID

interface DailyClosingMovementRepository : JpaRepository<DailyClosingMovement, UUID> {

    fun findAllByDailyClosingIdAndItemSubtypeInAndDeletedAtIsNull(
        dailyClosingId: UUID,
        itemSubtypes: Set<DailyClosingMovementItemSubtype>,
    ): List<DailyClosingMovement>

    fun findByIdAndDailyClosingIdAndDeletedAtIsNull(id: UUID, dailyClosingId: UUID): DailyClosingMovement?

    @Query(
        """
            SELECT dcm.*
                FROM daily_closing_movement dcm
                WHERE dcm.daily_closing_id IN :dailyClosingIds
                AND dcm.deleted_at IS NULL
                AND dcm.payment_type = 'CASH';
        """,
        nativeQuery = true
    )
    fun findAllNonDeletedCashMovementsByDailyClosingIds(dailyClosingIds: Set<UUID>): List<DailyClosingMovement>

    fun existsByDailyClosingIdAndItemSubtypeAndDeletedAtIsNull(
        dailyClosingId: UUID,
        itemSubtype: DailyClosingMovementItemSubtype,
    ): Boolean

    @Query(
        """
            SELECT dcm.daily_closing_id
            FROM daily_closing_movement dcm
            WHERE dcm.payment_type = 'CASH'
              AND dcm.deleted_at IS NULL
              AND dcm.daily_closing_id IN :dailyClosingIds
            GROUP BY dcm.daily_closing_id
            HAVING SUM(dcm.amount) = 0
              AND SUM(CASE WHEN dcm.item_type = 'DEDUCTION' THEN 1 ELSE 0 END) = 0;
        """,
        nativeQuery = true
    )
    fun findAllDailyClosingIdsWithEmptyCashMovements(dailyClosingIds: Set<UUID>): List<UUID>
}
