package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.Symbol
import com.cleevio.cinemax.api.common.util.buildDateAndTimeCondition
import com.cleevio.cinemax.api.common.util.buildUnaccentLikeIgnoreCaseCondition
import com.cleevio.cinemax.api.common.util.inOrNullIfNullOrEmpty
import com.cleevio.cinemax.api.module.basket.util.PAID_BASKET_STATES
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchProductBasketItemsFilter
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchTicketBasketItemsFilter
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.constant.ServiceFeeType
import com.cleevio.cinemax.psql.Tables.BASKET
import com.cleevio.cinemax.psql.Tables.BASKET_ITEM
import com.cleevio.cinemax.psql.Tables.DISCOUNT_CARD
import com.cleevio.cinemax.psql.Tables.DISCOUNT_CARD_USAGE
import com.cleevio.cinemax.psql.Tables.PRICE_CATEGORY_ITEM
import com.cleevio.cinemax.psql.Tables.PRODUCT_COMPOSITION
import com.cleevio.cinemax.psql.Tables.SCREENING
import com.cleevio.cinemax.psql.Tables.SCREENING_TYPES
import com.cleevio.cinemax.psql.Tables.TICKET
import com.cleevio.cinemax.psql.tables.records.ScreeningRecord
import org.jooq.CommonTableExpression
import org.jooq.Condition
import org.jooq.Field
import org.jooq.Record2
import org.jooq.SelectHavingStep
import org.jooq.TableField
import org.jooq.impl.DSL.and
import org.jooq.impl.DSL.coalesce
import org.jooq.impl.DSL.count
import org.jooq.impl.DSL.countDistinct
import org.jooq.impl.DSL.exists
import org.jooq.impl.DSL.falseCondition
import org.jooq.impl.DSL.field
import org.jooq.impl.DSL.name
import org.jooq.impl.DSL.or
import org.jooq.impl.DSL.round
import org.jooq.impl.DSL.select
import org.jooq.impl.DSL.selectOne
import org.jooq.impl.DSL.sum
import org.jooq.impl.DSL.trueCondition
import org.jooq.impl.DSL.`when`
import org.jooq.impl.DSL.with
import org.jooq.impl.UpdatableRecordImpl
import java.math.BigDecimal
import java.util.UUID

fun createTicketBasketItemFilterConditions(filter: AdminSearchTicketBasketItemsFilter): List<Condition> = listOfNotNull(
    // required conditions
    BASKET_ITEM.basket().STATE.`in`(PAID_BASKET_STATES),
    BASKET_ITEM.TYPE.eq(BasketItemType.TICKET),
    BASKET_ITEM.DELETED_AT.isNull,
    DISCOUNT_CARD_USAGE.DELETED_AT.isNull,

    // optional conditions
    filter.basketPaidAtFrom?.let { BASKET_ITEM.basket().PAID_AT.ge(it) },
    filter.basketPaidAtTo?.let { BASKET_ITEM.basket().PAID_AT.le(it) },
    BASKET_ITEM.basket().UPDATED_BY.inOrNullIfNullOrEmpty(filter.basketUpdatedBy),
    BASKET_ITEM.basket().STATE.inOrNullIfNullOrEmpty(filter.basketStates),
    filter.basketVariableSymbol?.let { BASKET_ITEM.basket().VARIABLE_SYMBOL.eq(it) },
    buildDateAndTimeCondition(
        dateTime = filter.screeningDateTimeFrom,
        dateField = BASKET_ITEM.ticket().screening().DATE,
        timeField = BASKET_ITEM.ticket().screening().TIME,
        symbol = Symbol.GREATER_OR_EQUAL
    ),
    buildDateAndTimeCondition(
        dateTime = filter.screeningDateTimeTo,
        dateField = BASKET_ITEM.ticket().screening().DATE,
        timeField = BASKET_ITEM.ticket().screening().TIME,
        symbol = Symbol.LESS_OR_EQUAL
    ),
    BASKET_ITEM.ticket().screening().auditorium().ID.inOrNullIfNullOrEmpty(filter.auditoriumIds),
    BASKET_ITEM.ticket().screening().ID.inOrNullIfNullOrEmpty(filter.screeningIds),
    BASKET_ITEM.ticket().screening().movie().ID.inOrNullIfNullOrEmpty(filter.movieIds),
    BASKET_ITEM.ticket().screening().movie().distributor().ID.inOrNullIfNullOrEmpty(filter.distributorIds),
    BASKET_ITEM.basket().posConfiguration().ID.inOrNullIfNullOrEmpty(filter.posConfigurationIds),
    BASKET_ITEM.basket().PAYMENT_TYPE.inOrNullIfNullOrEmpty(filter.paymentTypes),
    filter.ticketUsed?.let { BASKET_ITEM.ticket().IS_USED.eq(it) },
    filter.ticketIncludes3dGlasses?.let { BASKET_ITEM.ticket().INCLUDES_3D_GLASSES.eq(it) },
    filter.surchargeTypes?.let {
        BASKET_ITEM.ticket().ticketPrice().SEAT_SURCHARGE_TYPE.`in`(it)
            .or(BASKET_ITEM.ticket().ticketPrice().AUDITORIUM_SURCHARGE_TYPE.`in`(it))
    },
    filter.serviceFeeTypes?.let {
        BASKET_ITEM.ticket().ticketPrice().SEAT_SERVICE_FEE_TYPE.`in`(it)
            .or(BASKET_ITEM.ticket().ticketPrice().AUDITORIUM_SERVICE_FEE_TYPE.`in`(it))
            .or(
                BASKET_ITEM.ticket().ticketPrice().SERVICE_FEE_GENERAL.gt(0.toBigDecimal()).and(
                    if (ServiceFeeType.GENERAL in it) trueCondition() else falseCondition()
                )
            )
    },
    BASKET_ITEM.ticket().screening().movie().technology().ID.inOrNullIfNullOrEmpty(filter.technologyIds),
    BASKET_ITEM.ticket().screening().movie().production().ID.inOrNullIfNullOrEmpty(filter.productionIds),
    BASKET_ITEM.ticket().RECEIPT_NUMBER.inOrNullIfNullOrEmpty(filter.ticketReceiptNumbers),
    DISCOUNT_CARD.CODE.inOrNullIfNullOrEmpty(filter.discountCardCodes),
    DISCOUNT_CARD.TITLE.inOrNullIfNullOrEmpty(filter.discountCardTitles),
    filter.isDiscounted?.let {
        if (it) {
            BASKET_ITEM.ticket().primaryTicketDiscount().ID.isNotNull
                .or(BASKET_ITEM.ticket().secondaryTicketDiscount().ID.isNotNull)
                .or(PRICE_CATEGORY_ITEM.DISCOUNTED.isTrue)
                .or(DISCOUNT_CARD.ID.isNotNull)
        } else {
            BASKET_ITEM.ticket().primaryTicketDiscount().ID.isNull
                .and(BASKET_ITEM.ticket().secondaryTicketDiscount().ID.isNull)
                .and(PRICE_CATEGORY_ITEM.DISCOUNTED.isFalse)
                .and(DISCOUNT_CARD.ID.isNull)
        }
    },
    BASKET_ITEM.ticket().primaryTicketDiscount().ID.inOrNullIfNullOrEmpty(filter.primaryTicketDiscountIds),
    BASKET_ITEM.ticket().secondaryTicketDiscount().ID.inOrNullIfNullOrEmpty(filter.secondaryTicketDiscountIds),
    filter.isCancelled?.let {
        if (it) BASKET_ITEM.IS_CANCELLED.isTrue else BASKET_ITEM.IS_CANCELLED.isFalse
    },
    BASKET_ITEM.branch().ID.inOrNullIfNullOrEmpty(filter.branchIds)
)

fun createProductBasketItemFilterConditions(filter: AdminSearchProductBasketItemsFilter): List<Condition> =
    listOfNotNull(
        // required conditions
        BASKET_ITEM.basket().STATE.`in`(PAID_BASKET_STATES),
        BASKET_ITEM.TYPE.eq(BasketItemType.PRODUCT)
            .or(
                BASKET_ITEM.TYPE.eq(BasketItemType.PRODUCT_DISCOUNT).and(BASKET_ITEM.PRODUCT_ISOLATED_WITH_ID.isNotNull)
            )
            .or(BASKET_ITEM.ID.eq(ACTIVE_PRODUCT_DISCOUNT_ID)),
        BASKET_ITEM.DELETED_AT.isNull,
        BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull,

        // optional conditions
        filter.basketPaidAtFrom?.let { BASKET_ITEM.basket().PAID_AT.ge(it) },
        filter.basketPaidAtTo?.let { BASKET_ITEM.basket().PAID_AT.le(it) },
        BASKET.UPDATED_BY.inOrNullIfNullOrEmpty(filter.basketUpdatedBy),
        BASKET.posConfiguration().ID.inOrNullIfNullOrEmpty(filter.posConfigurationIds),
        buildUnaccentLikeIgnoreCaseCondition(filter.productReceiptNumber, BASKET_ITEM.PRODUCT_RECEIPT_NUMBER),
        BASKET_ITEM.PRODUCT_ID.inOrNullIfNullOrEmpty(filter.productIds),
        BASKET_ITEM.product().PRODUCT_CATEGORY_ID.inOrNullIfNullOrEmpty(filter.productCategoryIds),
        buildUnaccentLikeIgnoreCaseCondition(filter.productCode, BASKET_ITEM.product().CODE),
        BASKET.PAYMENT_TYPE.inOrNullIfNullOrEmpty(filter.paymentTypes),
        buildUnaccentLikeIgnoreCaseCondition(filter.discountCardCode, DISCOUNT_CARD.CODE),
        DISCOUNT_CARD.TYPE.inOrNullIfNullOrEmpty(filter.discountCardTypes),
        buildUnaccentLikeIgnoreCaseCondition(filter.discountCardTitle, DISCOUNT_CARD.TITLE),
        filter.isDiscount?.let {
            if (it) {
                BASKET_ITEM.TYPE.eq(BasketItemType.PRODUCT_DISCOUNT)
            } else {
                BASKET_ITEM.TYPE.eq(BasketItemType.PRODUCT)
            }
        },
        filter.isCancelled?.let {
            if (it) BASKET_ITEM.IS_CANCELLED.isTrue else BASKET_ITEM.IS_CANCELLED.isFalse
        },
        BASKET_ITEM.branch().ID.inOrNullIfNullOrEmpty(filter.branchIds),
        filter.soldInBuffet?.let {
            if (it) BASKET_ITEM.product().SOLD_IN_BUFFET.isTrue else BASKET_ITEM.product().SOLD_IN_BUFFET.isFalse
        },
        filter.soldInCafe?.let {
            if (it) BASKET_ITEM.product().SOLD_IN_CAFE.isTrue else BASKET_ITEM.product().SOLD_IN_CAFE.isFalse
        },
        filter.soldInVip?.let {
            if (it) BASKET_ITEM.product().SOLD_IN_VIP.isTrue else BASKET_ITEM.product().SOLD_IN_VIP.isFalse
        }
    )

fun countBasketItemFilterWhere(condition: Condition): Field<Int> = coalesce(
    count(BASKET_ITEM.ID).filterWhere(condition),
    0
)

fun <T : UpdatableRecordImpl<*>> sumFieldFilterWhere(
    field: TableField<T, BigDecimal>,
    condition: Condition,
): Field<BigDecimal> = coalesce(
    sum(field).filterWhere(condition),
    0.toBigDecimal()
)

fun <T : UpdatableRecordImpl<*>> sumFieldsFilterWhere(
    field1: TableField<T, BigDecimal>,
    field2: TableField<T, BigDecimal>,
    condition: Condition,
): Field<BigDecimal> = coalesce(
    sum(field1.plus(field2)).filterWhere(condition),
    0.toBigDecimal()
)

fun mapDeductionForSingleTicket(field: TableField<ScreeningRecord, Int>): Field<BigDecimal> {
    return (
        BASKET_ITEM.ticket().ticketPrice().BASE_PRICE
            .plus(
                coalesce(
                    BASKET_ITEM.ticket().ticketPrice().SEAT_SURCHARGE,
                    0.toBigDecimal()
                )
            )
            .plus(
                coalesce(
                    BASKET_ITEM.ticket().ticketPrice().AUDITORIUM_SURCHARGE,
                    0.toBigDecimal()
                )
            )
        )
        .multiply(field)
        .divide(100)
}

fun mapDeduction(field: TableField<ScreeningRecord, Int>): Field<BigDecimal> = coalesce(
    sum(mapDeductionForSingleTicket(field)).filterWhere(
        BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull
    ),
    0.toBigDecimal()
)

fun mapSeatServiceFeeCondition(type: SeatServiceFeeType): Condition = and(
    BASKET_ITEM.ticket().ticketPrice().SEAT_SERVICE_FEE.gt(0.toBigDecimal()),
    BASKET_ITEM.ticket().ticketPrice().SEAT_SERVICE_FEE_TYPE.eq(type.name),
    BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull,
    BASKET_ITEM.IS_CANCELLED.isFalse
)

fun mapSeatSurchargeCondition(type: SeatSurchargeType) = and(
    BASKET_ITEM.ticket().ticketPrice().SEAT_SURCHARGE.gt(0.toBigDecimal()),
    BASKET_ITEM.ticket().ticketPrice().SEAT_SURCHARGE_TYPE.eq(type),
    BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull,
    BASKET_ITEM.IS_CANCELLED.isFalse
)

fun mapAuditoriumServiceFeeCondition(type: AuditoriumServiceFeeType) = and(
    BASKET_ITEM.ticket().ticketPrice().AUDITORIUM_SERVICE_FEE.gt(0.toBigDecimal()),
    BASKET_ITEM.ticket().ticketPrice().AUDITORIUM_SERVICE_FEE_TYPE.eq(type.name),
    BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull,
    BASKET_ITEM.IS_CANCELLED.isFalse
)

fun mapAuditoriumSurchargeCondition(type: AuditoriumSurchargeType) = and(
    BASKET_ITEM.ticket().ticketPrice().AUDITORIUM_SURCHARGE.gt(0.toBigDecimal()),
    BASKET_ITEM.ticket().ticketPrice().AUDITORIUM_SURCHARGE_TYPE.eq(type.name),
    BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull,
    BASKET_ITEM.IS_CANCELLED.isFalse
)

private val BASKET_ITEM_ALIAS = BASKET_ITEM.`as`("basket_item")
val ACTIVE_PRODUCT_DISCOUNT_ID = field(
    select(BASKET_ITEM_ALIAS.ID)
        .from(BASKET_ITEM_ALIAS)
        .where(BASKET_ITEM_ALIAS.BASKET_ID.eq(BASKET_ITEM.BASKET_ID))
        .and(BASKET_ITEM_ALIAS.TYPE.eq(BasketItemType.PRODUCT_DISCOUNT))
        .and(BASKET_ITEM_ALIAS.PRODUCT_ISOLATED_WITH_ID.isNull)
        .and(BASKET_ITEM_ALIAS.DELETED_AT.isNull)
        .orderBy(BASKET_ITEM_ALIAS.PRICE.neg().desc())
        .limit(1)
)

// Ticket and Product common fields (Sales section)
val NOT_CANCELLED_ITEMS_COUNT = countBasketItemFilterWhere(
    BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull.and(
        BASKET_ITEM.IS_CANCELLED.isFalse
    )
)
val NOT_CANCELLED_ITEMS_SUM = sumFieldsFilterWhere(
    BASKET_ITEM.PRICE,
    BASKET_ITEM.FIXED_PRICE,
    and(
        BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull,
        BASKET_ITEM.IS_CANCELLED.isFalse
    )
)
val SALES_CASH_SUM = sumFieldsFilterWhere(
    BASKET_ITEM.PRICE,
    BASKET_ITEM.FIXED_PRICE,
    and(
        BASKET_ITEM.basket().PAYMENT_TYPE.eq(PaymentType.CASH),
        BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull,
        BASKET_ITEM.IS_CANCELLED.isFalse
    )
)
val SALES_CASHLESS_SUM = sumFieldsFilterWhere(
    BASKET_ITEM.PRICE,
    BASKET_ITEM.FIXED_PRICE,
    and(
        BASKET_ITEM.basket().PAYMENT_TYPE.eq(PaymentType.CASHLESS),
        BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull,
        BASKET_ITEM.IS_CANCELLED.isFalse
    )
)

// TODO: cancellation items have isCancelled=true when they shouldn't, fix
val CANCELLED_ITEMS_COUNT = countBasketItemFilterWhere(
    BASKET_ITEM.IS_CANCELLED.isTrue.and(BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull)
)
val CANCELLED_ITEMS_EXPENSE = sumFieldsFilterWhere(
    BASKET_ITEM.PRICE,
    BASKET_ITEM.FIXED_PRICE,
    BASKET_ITEM.IS_CANCELLED.isTrue.and(BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull)
)

// Tickets summary - section 1
val TICKETS_USED_COUNT = countBasketItemFilterWhere(
    and(
        BASKET_ITEM.ticket().IS_USED.isTrue,
        BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull
    )
)
val PRO_DEDUCTION = mapDeduction(BASKET_ITEM.ticket().screening().PRO_COMMISSION)
val FILM_FOND_DEDUCTION = mapDeduction(BASKET_ITEM.ticket().screening().FILM_FOND_COMMISSION)
val DISTRIBUTOR_DEDUCTION = mapDeduction(BASKET_ITEM.ticket().screening().DISTRIBUTOR_COMMISSION)

// Tickets summary - section 2
private val GENERAL_SERVICE_FEE_CONDITION = and(
    BASKET_ITEM.ticket().ticketPrice().SERVICE_FEE_GENERAL.gt(0.toBigDecimal()),
    BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull,
    BASKET_ITEM.IS_CANCELLED.isFalse
)
private val VIP_SERVICE_FEE_CONDITION = mapSeatServiceFeeCondition(SeatServiceFeeType.VIP)
private val VIP_SURCHARGE_CONDITION = mapSeatSurchargeCondition(SeatSurchargeType.VIP)
private val DBOX_SURCHARGE_CONDITION = mapSeatSurchargeCondition(SeatSurchargeType.DBOX)
private val PP_SERVICE_FEE_CONDITION = mapSeatServiceFeeCondition(SeatServiceFeeType.PREMIUM_PLUS)
private val PP_SURCHARGE_CONDITION = mapSeatSurchargeCondition(SeatSurchargeType.PREMIUM_PLUS)
private val IMAX_SERVICE_FEE_CONDITION = mapAuditoriumServiceFeeCondition(AuditoriumServiceFeeType.IMAX)
private val IMAX_SURCHARGE_CONDITION = mapAuditoriumSurchargeCondition(AuditoriumSurchargeType.IMAX)
private val ULTRA_X_SERVICE_FEE_CONDITION = mapAuditoriumServiceFeeCondition(AuditoriumServiceFeeType.ULTRA_X)
private val ULTRA_X_SURCHARGE_CONDITION = mapAuditoriumSurchargeCondition(AuditoriumSurchargeType.ULTRA_X)

val GENERAL_SERVICE_FEE_SUM = sumFieldFilterWhere(
    BASKET_ITEM.ticket().ticketPrice().SERVICE_FEE_GENERAL,
    GENERAL_SERVICE_FEE_CONDITION
)
val GENERAL_SERVICE_FEE_COUNT = countBasketItemFilterWhere(GENERAL_SERVICE_FEE_CONDITION)
val VIP_SERVICE_FEE_SUM = sumFieldFilterWhere(
    BASKET_ITEM.ticket().ticketPrice().SEAT_SERVICE_FEE,
    VIP_SERVICE_FEE_CONDITION
)
val VIP_SURCHARGE_SUM = sumFieldFilterWhere(
    BASKET_ITEM.ticket().ticketPrice().SEAT_SURCHARGE,
    VIP_SURCHARGE_CONDITION
)
val VIP_COUNT = countBasketItemFilterWhere(or(VIP_SERVICE_FEE_CONDITION, VIP_SURCHARGE_CONDITION))
val DBOX_SURCHARGE_SUM = sumFieldFilterWhere(
    BASKET_ITEM.ticket().ticketPrice().SEAT_SURCHARGE,
    DBOX_SURCHARGE_CONDITION
)
val DBOX_COUNT = countBasketItemFilterWhere(DBOX_SURCHARGE_CONDITION)
val PP_SERVICE_FEE_SUM = sumFieldFilterWhere(
    BASKET_ITEM.ticket().ticketPrice().SEAT_SERVICE_FEE,
    PP_SERVICE_FEE_CONDITION
)
val PP_SURCHARGE_SUM = sumFieldFilterWhere(
    BASKET_ITEM.ticket().ticketPrice().SEAT_SURCHARGE,
    PP_SURCHARGE_CONDITION
)
val PP_COUNT = countBasketItemFilterWhere(PP_SURCHARGE_CONDITION)
val IMAX_SERVICE_FEE_SUM = sumFieldFilterWhere(
    BASKET_ITEM.ticket().ticketPrice().AUDITORIUM_SERVICE_FEE,
    IMAX_SERVICE_FEE_CONDITION
)
val IMAX_SURCHARGE_SUM = sumFieldFilterWhere(
    BASKET_ITEM.ticket().ticketPrice().AUDITORIUM_SURCHARGE,
    IMAX_SURCHARGE_CONDITION
)
val IMAX_COUNT = countBasketItemFilterWhere(IMAX_SURCHARGE_CONDITION)
val ULTRA_X_SERVICE_FEE_SUM = sumFieldFilterWhere(
    BASKET_ITEM.ticket().ticketPrice().AUDITORIUM_SERVICE_FEE,
    ULTRA_X_SERVICE_FEE_CONDITION
)
val ULTRA_X_SURCHARGE_SUM = sumFieldFilterWhere(
    BASKET_ITEM.ticket().ticketPrice().AUDITORIUM_SURCHARGE,
    ULTRA_X_SURCHARGE_CONDITION
)
val ULTRA_X_COUNT = countBasketItemFilterWhere(ULTRA_X_SURCHARGE_CONDITION)

// Tickets summary - section 3
val NOT_DISCOUNTED_TICKETS_COUNT = countBasketItemFilterWhere(
    and(
        BASKET_ITEM.ticket().TICKET_DISCOUNT_PRIMARY_ID.isNull,
        BASKET_ITEM.ticket().TICKET_DISCOUNT_SECONDARY_ID.isNull,
        PRICE_CATEGORY_ITEM.DISCOUNTED.isFalse,
        DISCOUNT_CARD.ID.isNull,
        BASKET_ITEM.IS_CANCELLED.isFalse
    )
)

// Products summary
fun <T : UpdatableRecordImpl<*>> mapTaxRateDivisor(field: TableField<T, Int>): Field<BigDecimal> {
    return ((field.divide(100.toBigDecimal())) + 1).cast(BigDecimal::class.java)
}

private val SIMPLE_PRODUCT_TYPES = setOf(ProductType.PRODUCT, ProductType.ADDITIONAL_SALE)
private val PIP_PRODUCT_COMPOSITION = PRODUCT_COMPOSITION.`as`("pip_product_composition")
private val EXISTS_PRODUCT_COMPOSITION_FOR_PRODUCT = exists(
    selectOne().from(PRODUCT_COMPOSITION).where(PRODUCT_COMPOSITION.PRODUCT_ID.eq(BASKET_ITEM.PRODUCT_ID))
)

val PRODUCTS_TAX_AMOUNT = coalesce(
    sum(
        `when`(
            BASKET_ITEM.product().TYPE.eq(ProductType.PRODUCT_IN_PRODUCT).and(BASKET_ITEM.product().TAX_RATE.isNotNull),
            BASKET_ITEM.PRICE - round(
                BASKET_ITEM.PRICE / mapTaxRateDivisor(BASKET_ITEM.product().TAX_RATE),
                4
            )
        ).otherwise(
            // product is a) type PRODUCT_IN_PRODUCT with null taxRate, b) type PRODUCT, c) type ADDITIONAL_SALE
            BASKET_ITEM.PRICE - round(
                BASKET_ITEM.PRICE / mapTaxRateDivisor(BASKET_ITEM.product().productCategory().TAX_RATE),
                4
            )
        )
    ).filterWhere(
        BASKET_ITEM.TYPE.eq(BasketItemType.PRODUCT),
        BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull
    ),
    0.toBigDecimal()
)
val BASKETS_COUNT = countDistinct(BASKET_ITEM.BASKET_ID)
val SUM_PIP_COMPOSITIONS_PURCHASE_PRICE = select(
    sum(PIP_PRODUCT_COMPOSITION.productComponent().PURCHASE_PRICE * PIP_PRODUCT_COMPOSITION.AMOUNT * PRODUCT_COMPOSITION.AMOUNT)
)
    .from(PIP_PRODUCT_COMPOSITION)
    .leftJoin(PIP_PRODUCT_COMPOSITION.productComponent())
    // join is performed on PRODUCT_COMPONENT_ID so PIP compositions representing deposit packages are omitted from calculation
    // (those are joined via PRODUCT_IN_PRODUCT_ID)
    .leftJoin(PRODUCT_COMPOSITION).on(PIP_PRODUCT_COMPOSITION.PRODUCT_ID.eq(PRODUCT_COMPOSITION.PRODUCT_IN_PRODUCT_ID))
    .where(PRODUCT_COMPOSITION.PRODUCT_ID.eq(BASKET_ITEM.PRODUCT_ID))
    .asField<BigDecimal>()
val SUM_PRODUCT_COMPOSITIONS_PURCHASE_PRICE = select(
    sum(PRODUCT_COMPOSITION.productComponent().PURCHASE_PRICE * PRODUCT_COMPOSITION.AMOUNT)
)
    .from(PRODUCT_COMPOSITION)
    // join is performed on PRODUCT_COMPONENT_ID so compositions representing deposit packages are omitted from calculation
    // (those are joined via PRODUCT_IN_PRODUCT_ID)
    .where(PRODUCT_COMPOSITION.PRODUCT_ID.eq(BASKET_ITEM.PRODUCT_ID))
    .asField<BigDecimal>()
val PRODUCT_WITHOUT_COMPOSITION_PURCHASE_PRICE: Field<BigDecimal> =
    BASKET_ITEM.PRICE / mapTaxRateDivisor(BASKET_ITEM.product().productCategory().TAX_RATE)
val PURCHASE_PRICE_SUM: Field<BigDecimal> = coalesce(
    sum(
        `when`(
            BASKET_ITEM.product().TYPE.`in`(SIMPLE_PRODUCT_TYPES).and(EXISTS_PRODUCT_COMPOSITION_FOR_PRODUCT),
            SUM_PRODUCT_COMPOSITIONS_PURCHASE_PRICE * BASKET_ITEM.QUANTITY
        ).`when`(
            BASKET_ITEM.product().TYPE.`in`(SIMPLE_PRODUCT_TYPES).and(!EXISTS_PRODUCT_COMPOSITION_FOR_PRODUCT),
            round(PRODUCT_WITHOUT_COMPOSITION_PURCHASE_PRICE, 4)
        ).`when`(
            BASKET_ITEM.product().TYPE.eq(ProductType.PRODUCT_IN_PRODUCT),
            SUM_PIP_COMPOSITIONS_PURCHASE_PRICE * BASKET_ITEM.QUANTITY
        ).otherwise(0.toBigDecimal())
    ).filterWhere(
        BASKET_ITEM.TYPE.eq(BasketItemType.PRODUCT),
        BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull
    ),
    0.toBigDecimal()
)

fun createScreeningTypesByScreeningCte(screeningTypeIds: Set<UUID>?) = name(SCREENING_TYPES_BY_SCREENING)
    .`as`(
        select(
            SCREENING_TYPES.SCREENING_ID.`as`(SCREENING_TYPES_SCREENING_ID),
            SCREENING_TYPES.SCREENING_TYPE_ID.`as`(SCREENING_TYPES_SCREENING_TYPE_ID)
        )
            .from(SCREENING_TYPES)
            .where(SCREENING_TYPES.SCREENING_TYPE_ID.`in`(screeningTypeIds))
    )

const val SCREENING_TYPES_BY_SCREENING = "sts"
const val SCREENING_TYPES_SCREENING_ID = "sts_screening_id"
const val SCREENING_TYPES_SCREENING_TYPE_ID = "sts_screening_type_id"

fun createScreeningsCountConditions(
    filter: AdminSearchTicketBasketItemsFilter,
): List<Condition> = listOfNotNull(
    SCREENING.STATE.eq(ScreeningState.PUBLISHED),
    SCREENING.DELETED_AT.isNull,
    SCREENING.STOPPED.isFalse,
    SCREENING.CANCELLED.isFalse,
    buildDateAndTimeCondition(
        dateTime = filter.screeningDateTimeFrom,
        dateField = SCREENING.DATE,
        timeField = SCREENING.TIME,
        symbol = Symbol.GREATER_OR_EQUAL
    ),
    buildDateAndTimeCondition(
        dateTime = filter.screeningDateTimeTo,
        dateField = SCREENING.DATE,
        timeField = SCREENING.TIME,
        symbol = Symbol.LESS_OR_EQUAL
    ),
    SCREENING.AUDITORIUM_ID.inOrNullIfNullOrEmpty(filter.auditoriumIds),
    SCREENING.ID.inOrNullIfNullOrEmpty(filter.screeningIds),
    SCREENING.MOVIE_ID.inOrNullIfNullOrEmpty(filter.movieIds),
    SCREENING.movie().DISTRIBUTOR_ID.inOrNullIfNullOrEmpty(filter.distributorIds),
    SCREENING.movie().TECHNOLOGY_ID.inOrNullIfNullOrEmpty(filter.technologyIds),
    SCREENING.movie().PRODUCTION_ID.inOrNullIfNullOrEmpty(filter.productionIds),
    SCREENING_TYPES.SCREENING_TYPE_ID.inOrNullIfNullOrEmpty(filter.screeningTypeIds),

    filter.isCancelled?.let {
        if (it) {
            BASKET_ITEM.IS_CANCELLED.isTrue.and(BASKET_ITEM.DELETED_AT.isNull)
        } else {
            BASKET_ITEM.IS_CANCELLED.isFalse.and(BASKET_ITEM.DELETED_AT.isNull)
        }
    },
    filter.basketPaidAtFrom?.let { BASKET_ITEM.basket().PAID_AT.ge(it).and(BASKET_ITEM.DELETED_AT.isNull) },
    filter.basketPaidAtTo?.let { BASKET_ITEM.basket().PAID_AT.le(it).and(BASKET_ITEM.DELETED_AT.isNull) }
)

val TICKET_DISCOUNT_TITLE_FIELD = coalesce(
    BASKET_ITEM.ticket().primaryTicketDiscount().TITLE,
    BASKET_ITEM.ticket().secondaryTicketDiscount().TITLE
)

val SCREENINGS_COUNT_FIELD: (List<Condition>) -> Field<Int> =
    { conditions ->
        field(
            select(countDistinct(SCREENING.ID))
                .from(SCREENING)
                .leftJoin(SCREENING_TYPES).on(SCREENING.ID.eq(SCREENING_TYPES.SCREENING_ID))
                .leftJoin(TICKET).on(SCREENING.ID.eq(TICKET.SCREENING_ID))
                .leftJoin(BASKET_ITEM).on(TICKET.ID.eq(BASKET_ITEM.TICKET_ID))
                .leftJoin(BASKET).on(BASKET_ITEM.BASKET_ID.eq(BASKET.ID))
                .where(conditions)
        )
    }

fun createDiscountCountsQuery(
    ticketBasketItemConditions: List<Condition>,
    screeningTypesByScreeningCte: CommonTableExpression<Record2<UUID, UUID>>,
    screeningTypeIds: Set<UUID>?,
): SelectHavingStep<Record2<String, Int>> {
    val screeningTypesScreeningId =
        screeningTypesByScreeningCte.field(SCREENING_TYPES_SCREENING_ID, UUID::class.java)!!
    val screeningTypesScreeningTypeId =
        screeningTypesByScreeningCte.field(SCREENING_TYPES_SCREENING_TYPE_ID, UUID::class.java)!!

    return with(
        screeningTypesByScreeningCte
    ).select(
        TICKET_DISCOUNT_TITLE_FIELD,
        count(BASKET_ITEM.ID)
    )
        .from(BASKET_ITEM)
        .leftJoin(PRICE_CATEGORY_ITEM).on(
            BASKET_ITEM.ticket().screening().priceCategory().ID.eq(PRICE_CATEGORY_ITEM.PRICE_CATEGORY_ID)
                .and(BASKET_ITEM.ticket().ticketPrice().BASE_PRICE_ITEM_NUMBER.eq(PRICE_CATEGORY_ITEM.NUMBER))
        )
        .leftJoin(screeningTypesByScreeningCte).on(BASKET_ITEM.ticket().screening().ID.eq(screeningTypesScreeningId))
        .leftJoin(DISCOUNT_CARD_USAGE).on(BASKET_ITEM.ID.eq(DISCOUNT_CARD_USAGE.TICKET_BASKET_ITEM_ID))
        .leftJoin(DISCOUNT_CARD).on(DISCOUNT_CARD.ID.eq(DISCOUNT_CARD_USAGE.DISCOUNT_CARD_ID))
        .where(
            ticketBasketItemConditions + listOf(
                BASKET_ITEM.ticket().TICKET_DISCOUNT_PRIMARY_ID.isNotNull
                    .or(BASKET_ITEM.ticket().TICKET_DISCOUNT_SECONDARY_ID.isNotNull),
                BASKET_ITEM.CANCELLED_BASKET_ITEM_ID.isNull,
                screeningTypeIds?.let { screeningTypesScreeningTypeId.`in`(it) }
            )
        )
        .groupBy(TICKET_DISCOUNT_TITLE_FIELD)
}
