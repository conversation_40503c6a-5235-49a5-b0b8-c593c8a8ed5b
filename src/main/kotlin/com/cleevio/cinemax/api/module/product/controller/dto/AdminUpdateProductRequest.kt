package com.cleevio.cinemax.api.module.product.controller.dto

import com.cleevio.cinemax.api.module.product.service.command.AdminUpdateProductCommand
import com.cleevio.cinemax.api.module.product.service.command.AdminUpdateProductCommand.AdminUpdateProductProductCompositionCommand
import java.math.BigDecimal
import java.util.UUID

data class AdminUpdateProductRequest(
    val title: String,
    val active: Boolean,
    val productCategoryId: UUID,
    val imageFileId: UUID? = null,
    val price: BigDecimal,
    val flagshipPrice: BigDecimal? = null,
    val soldInBuffet: Boolean,
    val soldInCafe: Boolean,
    val soldInVip: Boolean,
    val order: Int? = null,
    val tabletOrder: Int? = null,
    val isPackagingDeposit: Boolean? = null,
    val stockQuantityThreshold: Int? = null,
    val taxRate: Int? = null,
    val discountAmount: BigDecimal? = null,
    val discountPercentage: Int? = null,
    val productComposition: List<AdminUpdateProductProductCompositionRequest>,
) {
    fun toCommand(id: UUID) = AdminUpdateProductCommand(
        id = id,
        title = title,
        active = active,
        productCategoryId = productCategoryId,
        imageFileId = imageFileId,
        price = price,
        flagshipPrice = flagshipPrice,
        soldInBuffet = soldInBuffet,
        soldInCafe = soldInCafe,
        soldInVip = soldInVip,
        order = order,
        tabletOrder = tabletOrder,
        isPackagingDeposit = isPackagingDeposit,
        stockQuantityThreshold = stockQuantityThreshold,
        taxRate = taxRate,
        discountAmount = discountAmount,
        discountPercentage = discountPercentage,
        productComposition = productComposition.map {
            AdminUpdateProductProductCompositionCommand(
                productComponentId = it.productComponentId,
                productInProductId = it.productInProductId,
                quantity = it.quantity,
                productInProductPrice = it.productInProductPrice,
                productInProductFlagshipPrice = it.productInProductFlagshipPrice
            )
        }
    )

    data class AdminUpdateProductProductCompositionRequest(
        val productComponentId: UUID?,
        val productInProductId: UUID?,
        val quantity: BigDecimal,
        val productInProductPrice: BigDecimal?,
        val productInProductFlagshipPrice: BigDecimal?,
    )
}
