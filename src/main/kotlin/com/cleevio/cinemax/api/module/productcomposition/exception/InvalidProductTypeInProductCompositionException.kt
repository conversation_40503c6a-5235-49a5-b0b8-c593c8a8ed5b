package com.cleevio.cinemax.api.module.productcomposition.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.BAD_REQUEST)
class InvalidProductTypeInProductCompositionException : ApiException(
    Module.PRODUCT_COMPOSITION,
    ProductCompositionErrorType.INVALID_PRODUCT_TYPE_IN_PRODUCT_COMPOSITION
)
