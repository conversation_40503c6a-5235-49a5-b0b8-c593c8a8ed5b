package com.cleevio.cinemax.api.module.screeningtype.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.NOT_FOUND)
class ScreeningTypeNotFoundException : ApiException(
    Module.SCREENING_TYPE,
    ScreenigTypeErrorType.SCREENING_TYPE_NOT_FOUND
)
