package com.cleevio.cinemax.api.module.reservation.constant

import org.jooq.types.UByte

val SYNCABLE_MSSQL_RESERVATION_STATES = arrayOf(
    UByte.valueOf(11),
    UByte.valueOf(12),
    UByte.valueOf(15),
    UByte.valueOf(16),
    UByte.valueOf(1),
    UByte.valueOf(4),
    UByte.valueOf(13)
)

val RESERVATION_STATE_TO_MSSQL_RESERVATION_STATE_MAP = mapOf(
    ReservationState.UNAVAILABLE to 11,
    ReservationState.GROUP_RESERVED to 12,
    ReservationState.RESERVED to 15,
    ReservationState.FREE to 1,
    ReservationState.DELETED to 1,
    ReservationState.DISABLED to 4,
    ReservationState.ONLINE_RESERVED to 16
)

val MSSQL_RESERVATION_STATE_TO_RESERVATION_STATE_MAP = mapOf(
    11 to ReservationState.UNAVAILABLE,
    12 to ReservationState.GROUP_RESERVED,
    15 to ReservationState.RESERVED,
    16 to ReservationState.RESERVED,
    1 to ReservationState.FREE,
    4 to ReservationState.DISABLED,
    10 to ReservationState.DISABLED,
    13 to ReservationState.DELETED,
    16 to ReservationState.ONLINE_RESERVED
)

val UNAVAILABLE_MSSQL_STATE: UByte = UByte.valueOf(11)
val GROUP_RESERVED_MSSQL_STATE: UByte = UByte.valueOf(12)

val MSSQL_RESERVATION_STATE_TO_SEAT_DEFAULT_RESERVATION_STATE_MAP =
    MSSQL_RESERVATION_STATE_TO_RESERVATION_STATE_MAP +
        (0 to null) +
        (1 to null)
