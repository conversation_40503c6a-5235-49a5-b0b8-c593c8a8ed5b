package com.cleevio.cinemax.api.module.ticketdiscount.service.query

import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import org.springframework.data.domain.Pageable

data class AdminSearchTicketDiscountsFilter(
    val usageTypes: Set<TicketDiscountUsageType>? = null,
    val title: String? = null,
) {
    fun toQuery(pageable: Pageable) = AdminSearchTicketDiscountsQuery(pageable = pageable, filter = this)
}
