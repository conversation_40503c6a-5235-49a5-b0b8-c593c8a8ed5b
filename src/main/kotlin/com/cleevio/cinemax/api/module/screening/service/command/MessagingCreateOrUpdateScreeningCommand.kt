package com.cleevio.cinemax.api.module.screening.service.command

import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import jakarta.validation.constraints.PositiveOrZero
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID

data class MessagingCreateOrUpdateScreeningCommand(
    val id: UUID,
    val auditoriumOriginalCode: Int,
    val movieMessagingCode: String,
    val priceCategoryId: UUID,
    val state: ScreeningState,
    val date: LocalDate,
    val time: LocalTime,

    @field:PositiveOrZero
    val saleTimeLimit: Int,

    val stopped: Boolean,
    val cancelled: Boolean,

    @field:PositiveOrZero
    val proCommission: Int,

    @field:PositiveOrZero
    val filmFondCommission: Int,

    @field:PositiveOrZero
    val distributorCommission: Int,
    val publishOnline: Boolean,
    val screeningTypeCodes: Set<String>,

    @field:PositiveOrZero
    val adTimeSlot: Int = 15,

    @field:PositiveOrZero
    val surchargeVip: BigDecimal,

    @field:PositiveOrZero
    val surchargePremium: BigDecimal,

    @field:PositiveOrZero
    val surchargeImax: BigDecimal,

    @field:PositiveOrZero
    val surchargeUltraX: BigDecimal,

    @field:PositiveOrZero
    val serviceFeeVip: BigDecimal,

    @field:PositiveOrZero
    val serviceFeePremium: BigDecimal,

    @field:PositiveOrZero
    val serviceFeeImax: BigDecimal,

    @field:PositiveOrZero
    val serviceFeeUltraX: BigDecimal,

    @field:PositiveOrZero
    val surchargeDBox: BigDecimal,

    @field:PositiveOrZero
    val serviceFeeGeneral: BigDecimal,
)
