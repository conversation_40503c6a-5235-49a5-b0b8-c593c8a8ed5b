package com.cleevio.cinemax.api.module.movie.service

import com.cleevio.cinemax.api.module.movie.entity.Movie
import com.cleevio.cinemax.api.module.movie.exception.MovieNotFoundException
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class MovieJooqFinderService(
    private val movieJooqFinderRepository: MovieJooqFinderRepository,
) {

    fun findById(id: UUID): Movie? {
        return movieJooqFinderRepository.findById(id)
    }

    fun getById(id: UUID): Movie {
        return findById(id) ?: throw MovieNotFoundException()
    }

    fun findAllNonDeleted(): List<Movie> {
        return movieJooqFinderRepository.findAllNonDeleted()
    }

    fun findAll(): List<Movie> {
        return movieJooqFinderRepository.findAll()
    }

    fun findAllNonDeletedByIdIn(ids: Set<UUID>): List<Movie> {
        return movieJooqFinderRepository.findAllNonDeletedByIdIn(ids)
    }

    fun findNonDeletedByOriginalId(originalId: Int): Movie? {
        return movieJooqFinderRepository.findNonDeletedByOriginalId(originalId)
    }
}
