package com.cleevio.cinemax.api.module.productcomponent.controller.dto

import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.command.CreateOrUpdateProductComponentCommand
import java.math.BigDecimal
import java.util.UUID

data class CreateProductComponentRequest(
    val productComponentCategoryId: UUID,
    val title: String,
    val unit: ProductComponentUnit,
    val purchasePrice: BigDecimal,
    val active: Boolean,
    val taxRateOverride: Int? = null,
) {

    fun toCommand() = CreateOrUpdateProductComponentCommand(
        title = title,
        productComponentCategoryId = productComponentCategoryId,
        unit = unit,
        purchasePrice = purchasePrice,
        active = active,
        taxRateOverride = taxRateOverride,
        stockQuantity = BigDecimal.ZERO
    )
}
