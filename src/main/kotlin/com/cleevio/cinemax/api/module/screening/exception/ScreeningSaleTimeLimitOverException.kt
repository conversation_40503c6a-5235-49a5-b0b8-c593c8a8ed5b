package com.cleevio.cinemax.api.module.screening.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
class ScreeningSaleTimeLimitOverException : ApiException(
    module = Module.SCREENING,
    errorType = ScreeningErrorType.SCREENING_SALE_TIME_LIMIT_OVER
)
