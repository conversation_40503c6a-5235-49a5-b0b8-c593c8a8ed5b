package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.common.service.JooqFinderRepository
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.psql.tables.Basket.BASKET
import com.cleevio.cinemax.psql.tables.BasketItem.BASKET_ITEM
import org.jooq.DSLContext
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
class BasketItemJooqFinderRepository(
    private val psqlDslContext: DSLContext,
) : JooqFinderRepository<BasketItem> {

    override fun findAll(): List<BasketItem> {
        return psqlDslContext
            .selectFrom(BASKET_ITEM)
            .fetchInto(BasketItem::class.java)
    }

    override fun findById(id: UUID): BasketItem? {
        return psqlDslContext
            .selectFrom(BASKET_ITEM)
            .where(BASKET_ITEM.ID.eq(id))
            .fetchOneInto(BasketItem::class.java)
    }

    fun findAllNonDeleted(): List<BasketItem> {
        return psqlDslContext
            .selectFrom(BASKET_ITEM)
            .where(BASKET_ITEM.DELETED_AT.isNull)
            .fetchInto(BasketItem::class.java)
    }

    fun findNonDeletedById(id: UUID): BasketItem? {
        return psqlDslContext
            .selectFrom(BASKET_ITEM)
            .where(BASKET_ITEM.DELETED_AT.isNull)
            .and(BASKET_ITEM.ID.eq(id))
            .fetchOneInto(BasketItem::class.java)
    }

    fun findAllNonDeletedByBasketId(basketId: UUID): List<BasketItem> {
        return psqlDslContext
            .selectFrom(BASKET_ITEM)
            .where(BASKET_ITEM.DELETED_AT.isNull)
            .and(BASKET_ITEM.BASKET_ID.eq(basketId))
            .fetchInto(BasketItem::class.java)
    }

    fun findAllNonDeletedByOriginalIdIn(originalIds: Set<Int>): List<BasketItem> {
        return psqlDslContext
            .selectFrom(BASKET_ITEM)
            .where(BASKET_ITEM.DELETED_AT.isNull)
            .and(BASKET_ITEM.ORIGINAL_ID.`in`(originalIds))
            .fetchInto(BasketItem::class.java)
    }

    fun findAllNonDeletedWithOriginalIdByBasketIdIn(basketIds: Set<UUID>): List<BasketItem> {
        return psqlDslContext
            .selectFrom(BASKET_ITEM)
            .where(BASKET_ITEM.DELETED_AT.isNull)
            .and(BASKET_ITEM.ORIGINAL_ID.isNotNull)
            .and(BASKET_ITEM.BASKET_ID.`in`(basketIds))
            .fetchInto(BasketItem::class.java)
    }

    fun findNonDeletedByOriginalIdInAndBasketIsOpen(originalIds: Set<Int>): List<BasketItem> {
        return psqlDslContext
            .select(BASKET_ITEM)
            .from(BASKET_ITEM)
            .leftJoin(BASKET).on(BASKET_ITEM.BASKET_ID.eq(BASKET.ID))
            .where(BASKET_ITEM.DELETED_AT.isNull)
            .and(BASKET_ITEM.ORIGINAL_ID.`in`(originalIds))
            .and(BASKET.STATE.eq(BasketState.OPEN))
            .fetchInto(BasketItem::class.java)
    }
}
