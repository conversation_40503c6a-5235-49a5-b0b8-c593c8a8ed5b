package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.common.constant.PRODUCT
import com.cleevio.cinemax.api.common.service.AbstractMssqlSynchronizationService
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.toIntDown
import com.cleevio.cinemax.api.common.util.unaccent
import com.cleevio.cinemax.api.module.product.constant.ProductLockValues.SYNCHRONIZE_ADDITIONAL_SALE_FROM_MSSQL
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.command.SyncCreateOrUpdateProductCommand
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJooqFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlService
import com.cleevio.cinemax.mssql.rp_bufete.dbo.Tables.RZBOZIR
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Rzbozir
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class AdditionalSaleProductMssqlSynchronizationService(
    private val productService: ProductService,
    private val additionalSaleProductMssqlFinderRepository: AdditionalSaleProductMssqlFinderRepository,
    private val productCategoryJooqFinderService: ProductCategoryJooqFinderService,
    private val synchronizationFromMssqlFinderService: SynchronizationFromMssqlFinderService,
    synchronizationFromMssqlService: SynchronizationFromMssqlService,
) : AbstractMssqlSynchronizationService<Rzbozir>(
    mssqlEntityName = RZBOZIR.name,
    synchronizationFromMssqlService = synchronizationFromMssqlService,
    synchronizationType = SynchronizationFromMssqlType.ADDITIONAL_SALE_PRODUCT
) {
    private val log = logger()

    @TryLock(PRODUCT, SYNCHRONIZE_ADDITIONAL_SALE_FROM_MSSQL)
    fun synchronizeAll() = synchronizeAll {
        additionalSaleProductMssqlFinderRepository.findAllByUpdatedAtGt(
            synchronizationFromMssqlFinderService.findByType(SynchronizationFromMssqlType.ADDITIONAL_SALE_PRODUCT)
        )
    }

    override fun validateAndPersist(mssqlEntity: Rzbozir) {
        if (mssqlEntity.nazev.isBlank()) {
            log.info("MSSQL ${RZBOZIR.name} originalId=${mssqlEntity.rzbozirid} has blank title, skipping.")
            return
        }

        if (mssqlEntity.druh.isBlank()) {
            log.info("MSSQL ${RZBOZIR.name} name=${mssqlEntity.nazev} has null originalCode (rzbozir.druh), skipping.")
            return
        }

        val productCategory = productCategoryJooqFinderService.findNonDeletedByOriginalCode(mssqlEntity.druh) ?: run {
            log.warn(
                "Product category with originalCode=${mssqlEntity.druh} not found. " +
                    "Skipping sync of additional sale Product originalId=${mssqlEntity.rzbozirid}"
            )
            return
        }

        if (mssqlEntity.cislo.isBlank()) {
            log.info("MSSQL ${RZBOZIR.name} name=${mssqlEntity.nazev} has null originalCode (rzbozir.cislo), skipping.")
            return
        }

        productService.syncCreateOrUpdateProduct(
            mapToCreateOrUpdateProductCommand(
                mssqlProduct = mssqlEntity,
                productCategoryId = productCategory.id
            )
        )
    }

    private fun mapToCreateOrUpdateProductCommand(
        mssqlProduct: Rzbozir,
        productCategoryId: UUID,
    ): SyncCreateOrUpdateProductCommand {
        val title = mssqlProduct.nazev.trimEnd()
        return SyncCreateOrUpdateProductCommand(
            originalId = -mssqlProduct.rzbozirid,
            originalCode = mssqlProduct.cislo.trim(),
            productCategoryId = productCategoryId,
            title = title,
            type = ProductType.ADDITIONAL_SALE,
            price = mssqlProduct.cenacelk,
            priceNoVat = mssqlProduct.cena,
            active = mssqlProduct.aktivni,
            soldInBuffet = mssqlProduct.lbufet,
            soldInCafe = mssqlProduct.lbar,
            soldInVip = mssqlProduct.ltablet,
            discountPercentage = if (mssqlProduct.lproc) {
                mssqlProduct.cenacelk.toIntDown()
            } else if (HARDCODED_CARD_DISCOUNTS[title] != null) {
                HARDCODED_CARD_DISCOUNTS[title]
            } else {
                null
            },
            discountAmount = if (!mssqlProduct.lproc) mssqlProduct.cenacelk else null,
            isPackagingDeposit = title.lowercase().unaccent().let { "zaloha" in it && "obal" in it }
        )
    }
}
