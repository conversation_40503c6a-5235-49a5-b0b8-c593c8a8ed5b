package com.cleevio.cinemax.api.module.basket.service.command

import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import jakarta.validation.constraints.NotEmpty
import java.util.UUID

data class InitOnlineBasketCommand(
    @field:NotEmpty
    val tickets: List<InitOnlineTicketBasketItemInput>,
)

data class InitOnlineTicketBasketItemInput(
    val screeningId: UUID,
    val priceCategoryItemNumber: PriceCategoryItemNumber,
    val seatId: UUID,
    val reservationId: UUID,
    val discountCardId: UUID? = null,
    val ticketDiscountId: UUID? = null,
)
