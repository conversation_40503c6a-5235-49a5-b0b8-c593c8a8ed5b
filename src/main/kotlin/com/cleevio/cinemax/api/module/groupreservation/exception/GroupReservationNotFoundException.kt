package com.cleevio.cinemax.api.module.groupreservation.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.NOT_FOUND)
class GroupReservationNotFoundException : ApiException(
    Module.GROUP_RESERVATION,
    GroupReservationErrorType.GROUP_RESERVATION_NOT_FOUND
)
