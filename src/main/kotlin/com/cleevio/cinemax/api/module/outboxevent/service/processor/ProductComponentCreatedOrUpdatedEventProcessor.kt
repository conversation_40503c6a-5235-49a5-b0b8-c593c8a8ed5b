package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.productcomponent.entity.ProductComponent
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentJpaFinderService
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentMssqlFinderRepository
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponent.service.command.UpdateProductComponentOriginalIdCommand
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryJpaFinderService
import com.cleevio.cinemax.mssql.rp_bufete.dbo.Tables.RZBOZI
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.records.RzboziRecord
import org.jooq.DSLContext
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Service
class ProductComponentCreatedOrUpdatedEventProcessor(
    private val mssqlCinemaxDslContext: DSLContext,
    private val productComponentMssqlFinderRepository: ProductComponentMssqlFinderRepository,
    private val productComponentService: ProductComponentService,
    private val productComponentJpaFinderService: ProductComponentJpaFinderService,
    private val productComponenCategoryJpaFinderService: ProductComponentCategoryJpaFinderService,
) {
    private val log = logger()

    @Transactional(transactionManager = "mssqlBuffetTransactionManager")
    fun process(event: OutboxEvent): Int {
        log.info("Starting to process OutboxEvent=$event.")

        return runCatching {
            val productComponent = productComponentJpaFinderService.findNonDeletedById(event.entityId)
                ?: error("Product component with id=${event.entityId} not found")

            productComponent.originalId?.let {
                if (!productComponentMssqlFinderRepository.existsByOriginalId(it)) {
                    error("MSSQL rzbozi record with originalId $it not found.")
                }
                updateMssqlProductComponentRecord(productComponent)
            } ?: run {
                createMssqlProductComponentRecord(productComponent)?.let { originalId ->
                    productComponentService.updateProductComponentOriginalId(
                        UpdateProductComponentOriginalIdCommand(
                            productComponentId = productComponent.id,
                            originalId = originalId
                        )
                    )
                } ?: run {
                    error("MSSQL rzbozi record was not created or its originalId could not be fetched.")
                }
            }
            1
        }.onFailure { ex ->
            log.error("Failed to process OutboxEvent: ${ex.message}", ex)
        }.getOrDefault(0)
    }

    private fun createMssqlProductComponentRecord(productComponent: ProductComponent): Int? =
        mssqlCinemaxDslContext.insertInto(RZBOZI)
            .set(prepareMssqlProductComponentRecord(productComponent))
            .returningResult(RZBOZI.RZBOZIID)
            .fetchOneInto(Int::class.java)

    private fun updateMssqlProductComponentRecord(productComponent: ProductComponent): Int =
        mssqlCinemaxDslContext.update(RZBOZI)
            .set(prepareMssqlProductComponentRecord(productComponent))
            .where(RZBOZI.RZBOZIID.eq(productComponent.originalId))
            .execute()

    private fun prepareMssqlProductComponentRecord(productComponent: ProductComponent): RzboziRecord =
        mssqlCinemaxDslContext.newRecord(RZBOZI).also {
            val productComponentCategory = productComponenCategoryJpaFinderService.getNonDeletedById(
                productComponent.productComponentCategoryId
            )

            it.zbozi = productComponent.code
            it.nazev = productComponent.title
            it.skupina = productComponentCategory.code
            it.mj = productComponent.unit.mssqlValue
            it.aktivni = productComponent.active
            it.cenap = productComponent.purchasePrice
            it.zuziv = productComponent.updatedBy
            it.zcas = LocalDateTime.now()

            // Setting unused columns with default values
            it.minim = 0
            it.cena = 0.toBigDecimal()
            it.carkod = ""
        }
}
