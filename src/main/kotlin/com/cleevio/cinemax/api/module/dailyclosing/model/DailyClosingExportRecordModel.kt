package com.cleevio.cinemax.api.module.dailyclosing.model

import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import java.math.BigDecimal
import java.time.LocalDateTime

data class DailyClosingExportRecordModel(
    val closedAt: LocalDateTime?,
    val posConfigurationTitle: String,
    val counts: DailyClosingExportCountsRecordModel,
    val movements: DailyClosingExportMovementsRecordModel,
    val otherMovements: List<DailyClosingExportOtherMovementRecordModel>,
    val summary: DailyClosingExportSummaryRecordModel,
)

data class DailyClosingExportCountsRecordModel(
    val tickets: Int,
    val cancelledTickets: Int,
    val products: Int,
    val cancelledProducts: Int,
)

data class DailyClosingExportMovementsRecordModel(
    val cash: DailyClosingExportMovementRecordModel,
    val cashless: DailyClosingExportMovementRecordModel,
)

data class DailyClosingExportMovementRecordModel(
    val ticketsRevenue: BigDecimal,
    val ticketsServiceFeesRevenue: BigDecimal,
    val productsRevenue: BigDecimal,
    val cancelledTicketsExpense: BigDecimal,
    val cancelledProductsExpense: BigDecimal,
    val otherExpenses: BigDecimal,
    val otherRevenues: BigDecimal,
    val total: BigDecimal,
)

data class DailyClosingExportOtherMovementRecordModel(
    val title: String,
    val itemType: DailyClosingMovementItemType,
    val type: DailyClosingMovementType,
    val receiptNumber: String,
    val paymentType: PaymentType,
    val amount: BigDecimal,
    val variableSymbol: String?,
    val otherReceiptNumber: String?,
)

data class DailyClosingExportSummaryRecordModel(
    val cashTotal: BigDecimal,
    val deduction: BigDecimal,
    val afterDeduction: BigDecimal,
)
