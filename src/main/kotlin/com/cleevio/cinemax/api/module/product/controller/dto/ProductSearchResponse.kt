package com.cleevio.cinemax.api.module.product.controller.dto

import com.cleevio.cinemax.api.module.product.constant.ProductType
import java.math.BigDecimal
import java.util.UUID

data class ProductSearchResponse(
    val id: UUID,
    val title: String,
    val type: ProductType,
    val price: BigDecimal,
    val order: Int? = null,
    val stockQuantity: Int,
    val stockQuantityThreshold: Int? = null,
)
