package com.cleevio.cinemax.api.module.ticketprice.service

import com.cleevio.cinemax.api.common.constant.TICKET_PRICE
import com.cleevio.cinemax.api.common.util.toPercentageOrOne
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryJooqFinderService
import com.cleevio.cinemax.api.module.pricecategory.service.model.PriceCategoryModel
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.screening.entity.Screening
import com.cleevio.cinemax.api.module.screening.service.ScreeningJooqFinderService
import com.cleevio.cinemax.api.module.screeningfee.entity.ScreeningFee
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeFinderService
import com.cleevio.cinemax.api.module.seat.entity.Seat
import com.cleevio.cinemax.api.module.seat.service.SeatJooqFinderService
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.entity.TicketDiscount
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountJooqFinderService
import com.cleevio.cinemax.api.module.ticketprice.constant.TicketPriceLockValues.CREATE_TICKET_PRICE
import com.cleevio.cinemax.api.module.ticketprice.constant.TicketPriceLockValues.UPDATE_TICKET_PRICE
import com.cleevio.cinemax.api.module.ticketprice.entity.TicketPrice
import com.cleevio.cinemax.api.module.ticketprice.exception.FailedToCreateTicketPriceForSeatException
import com.cleevio.cinemax.api.module.ticketprice.exception.PriceCategoryForTicketPriceNotFoundException
import com.cleevio.cinemax.api.module.ticketprice.exception.PriceCategoryItemForTicketPriceNotFoundException
import com.cleevio.cinemax.api.module.ticketprice.exception.ScreeningFeeForTicketPriceNotFoundException
import com.cleevio.cinemax.api.module.ticketprice.exception.ScreeningForTicketPriceNotFoundException
import com.cleevio.cinemax.api.module.ticketprice.exception.SeatForTicketPriceNotFoundException
import com.cleevio.cinemax.api.module.ticketprice.service.command.CreateTicketPriceCommand
import com.cleevio.cinemax.api.module.ticketprice.service.command.CreateTicketPriceForSeatCommand
import com.cleevio.cinemax.api.module.ticketprice.service.command.UpdateMssqlTicketDiscountsCommand
import com.cleevio.cinemax.api.module.ticketprice.service.command.UpdateTicketDiscountsCommand
import com.cleevio.cinemax.api.module.ticketprice.service.command.UpdateTicketPricePriceCategoryCommand
import com.cleevio.cinemax.api.module.ticketprice.util.mapAuditoriumServiceFeeTypeAndPrice
import com.cleevio.cinemax.api.module.ticketprice.util.mapAuditoriumSurchargeTypeAndPrice
import com.cleevio.cinemax.api.module.ticketprice.util.mapSeatServiceFeeTypeAndPrice
import com.cleevio.cinemax.api.module.ticketprice.util.mapSeatSurchargeTypeAndPrice
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import com.cleevio.library.lockinghandler.service.LockFieldParameters
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.math.BigDecimal
import java.util.Optional
import java.util.UUID

@Service
@Validated
class TicketPriceService(
    private val screeningJooqFinderService: ScreeningJooqFinderService,
    private val screeningFeeFinderService: ScreeningFeeFinderService,
    private val priceCategoryJooqFinderService: PriceCategoryJooqFinderService,
    private val seatJooqFinderService: SeatJooqFinderService,
    private val ticketPriceRepository: TicketPriceRepository,
    private val ticketPriceJpaFinderService: TicketPriceJpaFinderService,
    private val ticketDiscountJooqFinderService: TicketDiscountJooqFinderService,
) {

    @Transactional
    @Lock(TICKET_PRICE, CREATE_TICKET_PRICE)
    fun createTicketPriceForSeat(
        @Valid
        @LockFieldParameters([LockFieldParameter("screeningId"), LockFieldParameter("seatId")])
        command: CreateTicketPriceForSeatCommand,
    ): TicketPrice {
        val screening = getScreening(command.screeningId)
        val seat = seatJooqFinderService.findByIdAndAuditoriumLayoutId(
            id = command.seatId,
            auditoriumLayoutId = screening.auditoriumLayoutId
        ) ?: throw SeatForTicketPriceNotFoundException()

        return ticketPriceRepository.save(
            mapToTicketPricesForSeat(
                screening = screening,
                screeningFee = getScreeningFee(command.screeningId),
                priceCategory = getPriceCategory(screening.priceCategoryId),
                priceCategoryItem = command.priceCategoryItemNumber,
                seat = seat,
                existingTicketPrices = listOf()
            ).firstOrNull() ?: throw FailedToCreateTicketPriceForSeatException()
        )
    }

    fun createTicketPrice(command: CreateTicketPriceCommand): TicketPrice = ticketPriceRepository.save(
        TicketPrice(
            screeningId = command.screeningId,
            seatId = command.seatId,
            basePrice = command.basePrice,
            basePriceItemNumber = command.priceCategoryItemNumber,
            basePriceBeforeDiscount = command.basePriceBeforeDiscount ?: command.basePrice,
            seatSurcharge = command.seatSurcharge,
            seatSurchargeType = command.seatSurchargeType,
            auditoriumSurcharge = command.auditoriumSurcharge,
            auditoriumSurchargeType = command.auditoriumSurchargeType,
            seatServiceFee = command.seatServiceFee,
            seatServiceFeeType = command.seatServiceFeeType,
            auditoriumServiceFee = command.auditoriumServiceFee,
            auditoriumServiceFeeType = command.auditoriumServiceFeeType,
            serviceFeeGeneral = command.serviceFeeGeneral,
            totalPrice = command.totalPrice
        )
    )

    @Transactional
    @Lock(TICKET_PRICE, UPDATE_TICKET_PRICE)
    fun updateTicketPricePriceCategory(
        @Valid
        @LockFieldParameter("ticketPriceId")
        command: UpdateTicketPricePriceCategoryCommand,
    ): TicketPrice {
        val ticketPrice = ticketPriceJpaFinderService.getById(command.ticketPriceId)
        val screening = screeningJooqFinderService.getById(command.screeningId)
        val priceCategory = priceCategoryJooqFinderService.getByIdWithItems(screening.priceCategoryId)
            ?: throw PriceCategoryForTicketPriceNotFoundException()
        val priceCategoryItem = priceCategory.items.firstOrNull { it.number == command.priceCategoryItemNumber }
            ?: throw PriceCategoryItemForTicketPriceNotFoundException()

        return ticketPriceRepository.saveAndFlush(
            ticketPrice.apply {
                basePrice = priceCategoryItem.price
                basePriceItemNumber = priceCategoryItem.number
                basePriceBeforeDiscount = priceCategoryItem.price
                totalPrice = priceCategoryItem.price.plus(ticketPrice.getSurchargeAndFeeSum())
            }
        )
    }

    @Transactional
    @Lock(TICKET_PRICE, UPDATE_TICKET_PRICE)
    fun updateTicketDiscounts(
        @Valid
        @LockFieldParameter("ticketPriceId")
        command: UpdateTicketDiscountsCommand,
    ): BigDecimal {
        val ticketPrice = ticketPriceJpaFinderService.getById(command.ticketPriceId)
        val (primaryDiscount, secondaryDiscount) = ticketDiscountJooqFinderService.validateAndGetDiscounts(
            primaryDiscountId = command.primaryTicketDiscountId?.orElse(null),
            secondaryDiscountId = command.secondaryTicketDiscountId?.orElse(null)
        )

        val newBasePrice = calculateDiscountedBasePrice(
            freeTicket = command.freeTicket,
            basePrice = ticketPrice.basePrice,
            primaryDiscount = primaryDiscount,
            secondaryDiscount = secondaryDiscount
        )

        val discountZeroFees = primaryDiscount?.zeroFees == true || secondaryDiscount?.zeroFees == true
        ticketPriceRepository.saveAndFlush(
            ticketPrice.apply {
                basePrice = newBasePrice
                primaryDiscountAmount = handleOptional(command.primaryTicketDiscountId, primaryDiscount?.amount)
                primaryDiscountPercentage = handleOptional(command.primaryTicketDiscountId, primaryDiscount?.percentage)
                secondaryDiscountAmount = handleOptional(command.secondaryTicketDiscountId, secondaryDiscount?.amount)
                secondaryDiscountPercentage = handleOptional(command.secondaryTicketDiscountId, secondaryDiscount?.percentage)
                zeroFees = discountZeroFees
                freeTicket = command.freeTicket
                totalPrice = calculateTotalPrice(discountZeroFees, newBasePrice, ticketPrice)

                if (discountZeroFees) {
                    seatSurchargeType?.let { seatSurcharge = 0.toBigDecimal() }
                    auditoriumSurchargeType?.let { auditoriumSurcharge = 0.toBigDecimal() }
                    seatServiceFeeType?.let { seatServiceFee = 0.toBigDecimal() }
                    auditoriumServiceFeeType?.let { auditoriumServiceFee = 0.toBigDecimal() }
                    serviceFeeGeneral = 0.toBigDecimal()
                }
            }
        )

        // function returns the "fixed price" amount if there is any, or 0 if there's not
        return if (primaryDiscount?.isFixedPrice() == true || secondaryDiscount?.isFixedPrice() == true) {
            primaryDiscount?.amount ?: secondaryDiscount?.amount ?: 0.toBigDecimal()
        } else {
            0.toBigDecimal()
        }
    }

    // function is mostly a duplicate of updateTicketDiscounts, for safety reasons, will be deleted sooner or later anyway
    fun updateMssqlTicketDiscounts(command: UpdateMssqlTicketDiscountsCommand) {
        val ticketPrice = ticketPriceJpaFinderService.getById(command.ticketPriceId)
        val (primaryDiscount, secondaryDiscount) = ticketDiscountJooqFinderService.validateAndGetDiscounts(
            primaryDiscountId = command.primaryTicketDiscountId?.orElse(null),
            secondaryDiscountId = command.secondaryTicketDiscountId?.orElse(null)
        )

        val discountZeroFees = primaryDiscount?.zeroFees == true || secondaryDiscount?.zeroFees == true
        ticketPriceRepository.saveAndFlush(
            ticketPrice.apply {
                if (primaryDiscount?.isFixedPrice() == true || secondaryDiscount?.isFixedPrice() == true) {
                    val newBasePrice = calculateDiscountedBasePrice(
                        freeTicket = command.freeTicket,
                        basePrice = ticketPrice.basePrice,
                        primaryDiscount = primaryDiscount,
                        secondaryDiscount = secondaryDiscount
                    )

                    basePrice = newBasePrice
                    totalPrice = calculateTotalPrice(discountZeroFees, newBasePrice, ticketPrice)
                }
                primaryDiscountAmount = handleOptional(command.primaryTicketDiscountId, primaryDiscount?.amount)
                primaryDiscountPercentage = handleOptional(command.primaryTicketDiscountId, primaryDiscount?.percentage)
                secondaryDiscountAmount = handleOptional(command.secondaryTicketDiscountId, secondaryDiscount?.amount)
                secondaryDiscountPercentage = handleOptional(command.secondaryTicketDiscountId, secondaryDiscount?.percentage)
                zeroFees = discountZeroFees
                freeTicket = command.freeTicket

                if (discountZeroFees) {
                    seatSurchargeType?.let { seatSurcharge = 0.toBigDecimal() }
                    auditoriumSurchargeType?.let { auditoriumSurcharge = 0.toBigDecimal() }
                    seatServiceFeeType?.let { seatServiceFee = 0.toBigDecimal() }
                    auditoriumServiceFeeType?.let { auditoriumServiceFee = 0.toBigDecimal() }
                    serviceFeeGeneral = 0.toBigDecimal()
                }
            }
        )
    }

    private fun mapToTicketPricesForSeat(
        screening: Screening,
        screeningFee: ScreeningFee,
        priceCategory: PriceCategoryModel,
        priceCategoryItem: PriceCategoryItemNumber? = null,
        seat: Seat,
        existingTicketPrices: List<TicketPrice>,
    ): List<TicketPrice> {
        val seatSurchargeData = mapSeatSurchargeTypeAndPrice(screeningFee, seat.type)
        val auditoriumSurchargeData = mapAuditoriumSurchargeTypeAndPrice(screeningFee)
        val seatServiceFeeData = mapSeatServiceFeeTypeAndPrice(screeningFee, seat.type)
        val auditoriumServiceFeeData = mapAuditoriumServiceFeeTypeAndPrice(screeningFee)

        val surchargeAndServiceFeeSum = calculateSurchargeAndServiceFeeSum(
            seatSurchargeData = seatSurchargeData,
            seatServiceFeeData = seatServiceFeeData,
            auditoriumSurchargeData = auditoriumSurchargeData,
            auditoriumServiceFeeData = auditoriumServiceFeeData,
            generalServiceFee = screeningFee.serviceFeeGeneral
        )

        val priceCategoryItemsFiltered = if (priceCategoryItem != null) {
            priceCategory.items.filter { priceCategoryItem == it.number }
        } else {
            priceCategory.items
        }

        return priceCategoryItemsFiltered
            .map { item ->
                val existingTicketPrice = existingTicketPrices.firstOrNull { it.basePriceItemNumber == item.number }

                TicketPrice(
                    id = existingTicketPrice?.id ?: UUID.randomUUID(),
                    screeningId = screening.id,
                    seatId = seat.id,
                    basePrice = item.price,
                    basePriceItemNumber = item.number,
                    basePriceBeforeDiscount = item.price,
                    seatSurcharge = seatSurchargeData?.second,
                    seatSurchargeType = seatSurchargeData?.first,
                    auditoriumSurcharge = auditoriumSurchargeData?.second,
                    auditoriumSurchargeType = auditoriumSurchargeData?.first,
                    seatServiceFee = seatServiceFeeData?.second,
                    seatServiceFeeType = seatServiceFeeData?.first,
                    auditoriumServiceFee = auditoriumServiceFeeData?.second,
                    auditoriumServiceFeeType = auditoriumServiceFeeData?.first,
                    serviceFeeGeneral = screeningFee.serviceFeeGeneral,
                    totalPrice = item.price.plus(surchargeAndServiceFeeSum)
                )
            }
    }

    private fun calculateSurchargeAndServiceFeeSum(
        seatSurchargeData: Pair<Any, BigDecimal>?,
        seatServiceFeeData: Pair<Any, BigDecimal>?,
        auditoriumSurchargeData: Pair<Any, BigDecimal>?,
        auditoriumServiceFeeData: Pair<Any, BigDecimal>?,
        generalServiceFee: BigDecimal,
    ) = setOfNotNull(seatSurchargeData, seatServiceFeeData, auditoriumSurchargeData, auditoriumServiceFeeData)
        .map { it.second }
        .sumOf { it }
        .plus(generalServiceFee)

    private fun calculateDiscountedBasePrice(
        freeTicket: Boolean,
        basePrice: BigDecimal,
        primaryDiscount: TicketDiscount? = null,
        secondaryDiscount: TicketDiscount? = null,
    ): BigDecimal {
        if (freeTicket) return BigDecimal.ZERO
        if (primaryDiscount?.isFixedPrice() == true || secondaryDiscount?.isFixedPrice() == true) return BigDecimal.ZERO

        return basePrice
            .minus(calculateDiscountAmount(basePrice, primaryDiscount))
            .minus(calculateDiscountAmount(basePrice, secondaryDiscount))
            .also {
                if (it < BigDecimal.ZERO) return BigDecimal.ZERO
            }
    }

    private fun calculateDiscountAmount(basePrice: BigDecimal, discount: TicketDiscount?): BigDecimal {
        return when (discount?.type) {
            TicketDiscountType.PERCENTAGE -> calculatePercentageDiscountAmount(basePrice, discount.percentage)
            TicketDiscountType.ABSOLUTE -> calculateAbsoluteDiscountAmount(discount.amount)
            null -> BigDecimal.ZERO
        }
    }

    private fun calculatePercentageDiscountAmount(basePrice: BigDecimal, percentage: Int?) =
        basePrice.times(percentage.toPercentageOrOne())

    private fun calculateAbsoluteDiscountAmount(amount: BigDecimal?) = amount ?: BigDecimal.ZERO

    private fun calculateTotalPrice(
        zeroFees: Boolean,
        newBasePrice: BigDecimal,
        ticketPrice: TicketPrice,
    ): BigDecimal {
        return if (zeroFees) {
            newBasePrice
        } else {
            newBasePrice.plus(
                listOf(
                    ticketPrice.seatSurcharge,
                    ticketPrice.auditoriumSurcharge,
                    ticketPrice.seatServiceFee,
                    ticketPrice.auditoriumServiceFee,
                    ticketPrice.serviceFeeGeneral
                ).mapNotNull { it }.sumOf { it }
            )
        }
    }

    private fun getScreening(screeningId: UUID) = screeningJooqFinderService.findNonDeletedById(screeningId)
        ?: throw ScreeningForTicketPriceNotFoundException()

    private fun getScreeningFee(screeningId: UUID) = screeningFeeFinderService.findByScreeningId(screeningId)
        ?: throw ScreeningFeeForTicketPriceNotFoundException()

    private fun getPriceCategory(priceCategoryId: UUID) = priceCategoryJooqFinderService.getByIdWithItems(priceCategoryId)
        ?: throw PriceCategoryForTicketPriceNotFoundException()

    fun <T> handleOptional(id: Optional<UUID>?, currentValue: T?): T? {
        return when {
            id != null && id.isPresent -> currentValue
            id != null && id.isEmpty -> null
            else -> currentValue
        }
    }
}
