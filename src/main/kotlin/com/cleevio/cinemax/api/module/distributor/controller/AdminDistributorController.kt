package com.cleevio.cinemax.api.module.distributor.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.common.dto.IdResult
import com.cleevio.cinemax.api.common.dto.SimplePage
import com.cleevio.cinemax.api.common.dto.toSimplePage
import com.cleevio.cinemax.api.module.distributor.controller.dto.AdminGetDistributorResponse
import com.cleevio.cinemax.api.module.distributor.controller.dto.AdminSearchDistributorsResponse
import com.cleevio.cinemax.api.module.distributor.controller.dto.CreateDistributorRequest
import com.cleevio.cinemax.api.module.distributor.controller.dto.UpdateDistributorRequest
import com.cleevio.cinemax.api.module.distributor.service.AdminGetDistributorQueryService
import com.cleevio.cinemax.api.module.distributor.service.AdminSearchDistributorsQueryService
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.distributor.service.command.DeleteDistributorCommand
import com.cleevio.cinemax.api.module.distributor.service.query.AdminGetDistributorQuery
import com.cleevio.cinemax.api.module.distributor.service.query.SearchDistributorsFilter
import com.cleevio.cinemax.psql.tables.DistributorColumnNames
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.data.domain.Pageable
import org.springframework.data.web.PageableDefault
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Manager Distributors")
@RestController
@RequestMapping("/manager-app/distributors")
class AdminDistributorController(
    private val distributorService: DistributorService,
    private val adminSearchDistributorsQueryService: AdminSearchDistributorsQueryService,
    private val adminGetDistributorQueryService: AdminGetDistributorQueryService,
) {

    @PreAuthorize(Role.MANAGER)
    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    fun createDistributor(@RequestBody request: CreateDistributorRequest): IdResult =
        IdResult(distributorService.adminCreateOrUpdateDistributor(request.toCommand()))

    @PreAuthorize(Role.MANAGER)
    @PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
    fun searchDistributors(
        @ParameterObject
        @PageableDefault(sort = [DistributorColumnNames.TITLE])
        pageable: Pageable,
        @RequestBody filter: SearchDistributorsFilter,
    ): SimplePage<AdminSearchDistributorsResponse> =
        adminSearchDistributorsQueryService(filter.toQuery(pageable = pageable)).toSimplePage()

    @PreAuthorize(Role.MANAGER)
    @GetMapping("/{distributorId}", produces = [ApiVersion.VERSION_1_JSON])
    fun getDistributor(@PathVariable distributorId: UUID): AdminGetDistributorResponse =
        adminGetDistributorQueryService(AdminGetDistributorQuery(distributorId = distributorId))

    @PreAuthorize(Role.MANAGER)
    @DeleteMapping("/{distributorId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun deleteDistributor(@PathVariable distributorId: UUID): Unit =
        distributorService.deleteDistributor(DeleteDistributorCommand(distributorId))

    @PreAuthorize(Role.MANAGER)
    @PutMapping("/{distributorId}", produces = [ApiVersion.VERSION_1_JSON])
    fun updateDistributor(@PathVariable distributorId: UUID, @RequestBody request: UpdateDistributorRequest): IdResult =
        IdResult(distributorService.adminCreateOrUpdateDistributor(request.toCommand(distributorId)))
}
