package com.cleevio.cinemax.api.module.employee.service

import com.cleevio.cinemax.api.common.constant.EMPLOYEE
import com.cleevio.cinemax.api.common.service.AbstractMssqlSynchronizationService
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.employee.constant.EmployeeLockValues.SYNCHRONIZE_FROM_MSSQL
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.employee.service.command.CreateOrUpdateEmployeeCommand
import com.cleevio.cinemax.api.module.employee.util.decodePassword
import com.cleevio.cinemax.api.module.employee.util.removeAsciiControlCharacters
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlService
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables.RUZIV
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Ruziv
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.stereotype.Service
import java.time.LocalDateTime

@Service
class EmployeeMssqlSynchronizationService(
    private val employeeService: EmployeeService,
    private val employeeFinderService: EmployeeFinderService,
    private val employeeMssqlCinemaxFinderRepository: EmployeeMssqlCinemaxFinderRepository,
    private val employeeMssqlBuffetFinderRepository: EmployeeMssqlBuffetFinderRepository,
    private val synchronizationFromMssqlFinderService: SynchronizationFromMssqlFinderService,
    synchronizationFromMssqlService: SynchronizationFromMssqlService,
) : AbstractMssqlSynchronizationService<Ruziv>(
    mssqlEntityName = RUZIV.name,
    synchronizationFromMssqlService = synchronizationFromMssqlService,
    synchronizationType = SynchronizationFromMssqlType.EMPLOYEE
) {
    private val log = logger()
    private val cashierUsernames: MutableSet<String> = mutableSetOf()
    private val buffetEmployeeUsernameToOriginalId: MutableMap<String, Int> = mutableMapOf()

    @TryLock(EMPLOYEE, SYNCHRONIZE_FROM_MSSQL)
    fun synchronizeAll() {
        cashierUsernames.addAll(
            employeeMssqlCinemaxFinderRepository.findAllCashierUsernames()
        )
        buffetEmployeeUsernameToOriginalId.putAll(
            employeeMssqlBuffetFinderRepository.findAllByUpdatedAtGt().associate { it.uziv.trim() to it.ruzivid.toInt() }
        )

        synchronizeAll {
            employeeMssqlCinemaxFinderRepository.findAllByUpdatedAtGt(
                synchronizationFromMssqlFinderService.findByType(SynchronizationFromMssqlType.EMPLOYEE)
            )
        }

        // goddamn password reset hack because of duplicate 'ruziv' source of truth tables...
        val cinemaxEmployeesByUsername = employeeMssqlCinemaxFinderRepository.findAllByUpdatedAtGt()
            .associateBy { it.uziv.trim() }

        employeeMssqlBuffetFinderRepository.findAllByUpdatedAtGt().filter { it.zapis }.forEach {
            cinemaxEmployeesByUsername[it.uziv.trim()]?.let { cinemaxEmployee ->
                employeeService.syncCreateOrUpdateEmployee(
                    mapToCreateOrUpdateEmployee(
                        username = cinemaxEmployee.uziv.trim(),
                        mssqlPassword = it.heslo,
                        mssqlOriginalId = cinemaxEmployee.ruzivid,
                        mssqlFullName = cinemaxEmployee.jmeno,
                        mssqlPosName = cinemaxEmployee.pokladna,
                        mssqlPasswordReset = it.zapis,
                        mssqlAccessibleAt = it.datod
                    )
                )
            }
        }

        cashierUsernames.clear()
        buffetEmployeeUsernameToOriginalId.clear()
    }

    override fun validateAndPersist(mssqlEntity: Ruziv) {
        val username = mssqlEntity.uziv.trim()

        employeeFinderService.findByUsername(username)?.let {
            if (it.isDeleted()) {
                log.warn("Employee with username $username has been already deleted. Skipping.")
                return
            }

            if (mssqlEntity.ruzivid.toInt() != it.originalId) {
                log.warn(
                    "Employee with username=$username already exists in DB. " +
                        "Skipping sync of Employee originalId=${mssqlEntity.ruzivid.toInt()}."
                )
                return
            }

            if (it.passwordReset) {
                log.info("Employee originalId=${it.originalId} has passwordReset=true and waits for sync to MSSQL, skipping.")
                return
            }
        }

        employeeService.syncCreateOrUpdateEmployee(
            mapToCreateOrUpdateEmployee(
                username = username,
                mssqlPassword = mssqlEntity.heslo,
                mssqlOriginalId = mssqlEntity.ruzivid,
                mssqlFullName = mssqlEntity.jmeno,
                mssqlPosName = mssqlEntity.pokladna,
                mssqlPasswordReset = mssqlEntity.zapis,
                mssqlAccessibleAt = mssqlEntity.datod
            )
        )
    }

    private fun mapToCreateOrUpdateEmployee(
        username: String,
        mssqlPassword: String,
        mssqlOriginalId: Short,
        mssqlFullName: String,
        mssqlPosName: String,
        mssqlPasswordReset: Boolean,
        mssqlAccessibleAt: LocalDateTime? = null,
    ): CreateOrUpdateEmployeeCommand {
        val passwordTrimmed = removeAsciiControlCharacters(mssqlPassword)
        val passwordPlaintext = decodePassword(passwordTrimmed)

        return CreateOrUpdateEmployeeCommand(
            originalId = mssqlOriginalId.toInt(),
            originalBuffetId = buffetEmployeeUsernameToOriginalId[username] ?: mssqlOriginalId.toInt(),
            username = username,
            fullName = mssqlFullName.trim().ifEmpty { null },
            posName = mssqlPosName.trim().ifEmpty { null },
            password = passwordPlaintext,
            passwordReset = mssqlPasswordReset,
            role = if (cashierUsernames.contains(username)) EmployeeRole.CASHIER else EmployeeRole.MANAGER,
            accessibleAt = mssqlAccessibleAt
        )
    }
}
