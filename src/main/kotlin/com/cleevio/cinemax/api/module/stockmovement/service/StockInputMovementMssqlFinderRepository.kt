package com.cleevio.cinemax.api.module.stockmovement.service

import com.cleevio.cinemax.api.common.util.buildGreaterThanDateTimeMssqlCondition
import com.cleevio.cinemax.mssql.rp_bufete.dbo.Tables.RPRIJ
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Rprij
import org.jooq.DSLContext
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class StockInputMovementMssqlFinderRepository(
    private val mssqlBuffetDslContext: DSLContext,
) {

    fun findAllStockInputMovementsByUpdatedAtGt(updatedAt: LocalDateTime? = null): List<Rprij> {
        return mssqlBuffetDslContext
            .selectFrom(RPRIJ)
            .where(buildGreaterThanDateTimeMssqlCondition(updatedAt, RPRIJ.ZCAS))
            .fetchInto(Rprij::class.java)
    }
}
