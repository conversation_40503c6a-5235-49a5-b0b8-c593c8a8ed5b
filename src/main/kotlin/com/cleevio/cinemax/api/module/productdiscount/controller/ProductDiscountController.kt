package com.cleevio.cinemax.api.module.productdiscount.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.module.product.service.ProductJooqFinderService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryJooqFinderService
import com.cleevio.cinemax.api.module.productdiscount.controller.dto.ProductDiscountSearchResponse
import com.cleevio.cinemax.api.module.productdiscount.controller.mapper.ProductDiscountResponseMapper
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "POS Product Discounts")
@RestController
@RequestMapping("/pos-app/product-discounts")
class ProductDiscountController(
    private val productCategoryJooqFinderService: ProductCategoryJooqFinderService,
    private val productDiscountResponseMapper: ProductDiscountResponseMapper,
    private val productJooqFinderService: ProductJooqFinderService,
) {

    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @GetMapping(produces = [ApiVersion.VERSION_1_JSON])
    fun getProductDiscounts(
        @RequestParam(required = false) basketId: UUID? = null,
    ): List<ProductDiscountSearchResponse> {
        val productCategoryIds = productCategoryJooqFinderService.findAllNonDeletedByType(
            type = ProductCategoryType.DISCOUNT
        ).mapToSet { it.id }
        val discountProducts = productJooqFinderService.findAvailableBuffetProductsByCategoryIds(productCategoryIds)
        return productDiscountResponseMapper.mapList(
            products = discountProducts,
            basketId = basketId
        )
    }
}
