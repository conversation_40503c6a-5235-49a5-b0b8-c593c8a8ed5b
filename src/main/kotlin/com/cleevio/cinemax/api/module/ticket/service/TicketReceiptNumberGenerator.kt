package com.cleevio.cinemax.api.module.ticket.service

import com.cleevio.cinemax.api.common.service.EntityRepository
import com.cleevio.cinemax.psql.Sequences
import org.springframework.stereotype.Service

@Service
class TicketReceiptNumberGenerator(
    private val entityRepository: EntityRepository,
) {

    fun generateTicketReceiptNumber(): String {
        return entityRepository.getSequenceNextVal(
            sequenceName = Sequences.TICKET_RECEIPT_NUMBER_SEQ.name
        ).let {
            "1${it.toString().padStart(9, '0')}"
        }
    }
}
