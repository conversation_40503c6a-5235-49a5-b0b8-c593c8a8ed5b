package com.cleevio.cinemax.api.module.file.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.common.dto.IdResult
import com.cleevio.cinemax.api.module.file.constant.FileType
import com.cleevio.cinemax.api.module.file.service.FileService
import com.cleevio.cinemax.api.module.file.service.command.UploadFileCommand
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.MediaType
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile

@Tag(name = "Manager Files")
@RestController
@RequestMapping("/manager-app/files")
class FileController(
    private val fileService: FileService,
) {

    @PreAuthorize(Role.MANAGER)
    @PostMapping(
        "/upload/{fileType}",
        consumes = [MediaType.MULTIPART_FORM_DATA_VALUE],
        produces = [ApiVersion.VERSION_1_JSON]
    )
    fun uploadFile(
        @PathVariable fileType: FileType,
        @RequestParam file: MultipartFile,
    ) = IdResult(fileService.uploadFile(UploadFileCommand(multipartFile = file, type = fileType)))
}
