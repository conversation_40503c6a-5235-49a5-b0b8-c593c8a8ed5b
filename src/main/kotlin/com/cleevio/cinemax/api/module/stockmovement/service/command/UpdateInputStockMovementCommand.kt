package com.cleevio.cinemax.api.module.stockmovement.service.command

import com.cleevio.cinemax.api.common.annotation.NullOrNotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDateTime
import java.util.UUID

data class UpdateInputStockMovementCommand(
    val stockMovementId: UUID,
    val supplierId: UUID,
    val recordedAt: LocalDateTime,

    @field:Size(max = 15)
    val receiptNumber: String,

    @field:NullOrNotBlank
    val note: String? = null,
)
