package com.cleevio.cinemax.api.module.discountcard.service

import com.cleevio.cinemax.api.module.discountcard.service.command.ActivateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.WebActivateDiscountCardCommand
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationJpaFinderService
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class WebActivateDiscountCardService(
    private val discountCardService: DiscountCardService,
    private val discountCardJpaFinderService: DiscountCardJpaFinderService,
    private val posConfigurationJpaFinderService: PosConfigurationJpaFinderService,
) {

    operator fun invoke(
        @Valid command: WebActivateDiscountCardCommand,
    ) {
        discountCardService.activateDiscountCard(
            ActivateDiscountCardCommand(
                discountCardId = discountCardJpaFinderService.getByCode(command.discountCardCode).id,
                basketId = command.basketId,
                posConfigurationId = posConfigurationJpaFinderService.getOnlinePosConfigurationId()
            )
        )
    }
}
