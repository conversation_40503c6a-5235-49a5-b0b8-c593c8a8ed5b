package com.cleevio.cinemax.api.module.productcategory.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.ExportCurrency
import com.cleevio.cinemax.api.common.constant.TaxRateType
import com.cleevio.cinemax.api.common.service.FileStorageService
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.productcategory.controller.dto.VipProductCategoriesResponse
import com.cleevio.cinemax.api.module.productcategory.controller.dto.VipProductCategoryResponse
import com.cleevio.cinemax.api.module.productcategory.controller.dto.VipProductResponse
import com.cleevio.cinemax.api.module.productcategory.controller.dto.VipTableResponse
import com.cleevio.cinemax.api.module.productcategory.service.query.GetVipProductCategoriesByTableIdQuery
import com.cleevio.cinemax.api.module.screening.service.PRODUCT_COMPONENT_STOCK_QUANTITY
import com.cleevio.cinemax.api.module.screening.service.PRODUCT_IN_PRODUCT_STOCK_QUANTITY
import com.cleevio.cinemax.api.module.table.constant.TableType
import com.cleevio.cinemax.api.module.table.constant.VipTableStatus
import com.cleevio.cinemax.psql.Tables.BASKET
import com.cleevio.cinemax.psql.Tables.PRODUCT
import com.cleevio.cinemax.psql.Tables.PRODUCT_CATEGORY
import com.cleevio.cinemax.psql.Tables.PRODUCT_COMPOSITION
import com.cleevio.cinemax.psql.tables.Table.TABLE
import org.jooq.DSLContext
import org.jooq.impl.DSL.exists
import org.jooq.impl.DSL.min
import org.jooq.impl.DSL.multiset
import org.jooq.impl.DSL.select
import org.jooq.impl.DSL.`when`
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.math.BigDecimal

@Service
@Validated
class GetVipProductCategoriesByTableIdQueryService(
    private val psqlDslContext: DSLContext,
    private val fileStorageService: FileStorageService,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
) {
    operator fun invoke(query: GetVipProductCategoriesByTableIdQuery): VipProductCategoriesResponse {
        val tableResponse =
            psqlDslContext
                .select(TABLE.IP_ADDRESS)
                .from(TABLE)
                .where(TABLE.ID.eq(query.tableId))
                .and(TABLE.PRODUCT_MODE.eq(ProductMode.VIP))
                .and(TABLE.TYPE.eq(TableType.SEAT))
                .fetchOne()
                ?.map {
                    VipTableResponse(
                        ipAddress = it[TABLE.IP_ADDRESS],
                        status = VipTableStatus.OK
                    )
                } ?: VipTableResponse(
                ipAddress = null,
                status = VipTableStatus.NOT_CONNECTED
            )

        val basketState = psqlDslContext
            .select(BASKET.STATE)
            .from(BASKET)
            .where(BASKET.TABLE_ID.eq(query.tableId))
            .and(BASKET.DELETED_AT.isNull)
            .orderBy(BASKET.CREATED_AT.desc())
            .limit(1)
            .fetchOneInto(BasketState::class.java)

        val vipProductCategories = fetchVipProductCategories()

        return VipProductCategoriesResponse(
            basketState = basketState,
            currency = currencySymbol,
            table = tableResponse,
            categories = vipProductCategories
        )
    }

    private fun fetchVipProductCategories(): List<VipProductCategoryResponse> {
        val vipProductsConditions = listOfNotNull(
            PRODUCT.PRODUCT_CATEGORY_ID.eq(PRODUCT_CATEGORY.ID),
            PRODUCT.SOLD_IN_VIP.isTrue,
            PRODUCT.ACTIVE.isTrue,
            PRODUCT.DELETED_AT.isNull,
            minQuantityCondition
        )

        // Fetch products that meet the criteria: soldInVip=true, active=true, quantity>1
        val vipProducts = multiset(
            select(
                PRODUCT.ID,
                PRODUCT.TITLE,
                PRODUCT.PRICE,
                PRODUCT.file().ID,
                PRODUCT.file().TYPE,
                PRODUCT.file().EXTENSION,
                PRODUCT.TABLET_ORDER
            )
                .from(PRODUCT)
                .where(vipProductsConditions)
                .orderBy(PRODUCT.TABLET_ORDER.asc().nullsLast())
        )

        return psqlDslContext
            .select(
                PRODUCT_CATEGORY.ID,
                PRODUCT_CATEGORY.TITLE,
                PRODUCT_CATEGORY.file().ID,
                PRODUCT_CATEGORY.file().TYPE,
                PRODUCT_CATEGORY.file().EXTENSION,
                PRODUCT_CATEGORY.ORDER,
                vipProducts
            )
            .from(PRODUCT_CATEGORY)
            .where(
                PRODUCT_CATEGORY.DELETED_AT.isNull,
                exists(
                    select(PRODUCT.ID)
                        .from(PRODUCT)
                        .where(vipProductsConditions)
                )
            )
            .orderBy(PRODUCT_CATEGORY.ORDER.asc().nullsLast())
            .fetch()
            .map { record ->
                val productsList = record[vipProducts]?.map { productRecord ->
                    VipProductResponse(
                        id = productRecord[PRODUCT.ID],
                        title = productRecord[PRODUCT.TITLE],
                        price = productRecord[PRODUCT.PRICE],
                        imageFileUrl = productRecord[PRODUCT.file().ID]?.let {
                            fileStorageService.getFileUrl(
                                fileId = productRecord[PRODUCT.file().ID],
                                fileExtension = productRecord[PRODUCT.file().EXTENSION],
                                fileType = productRecord[PRODUCT.file().TYPE]
                            )
                        },
                        tabletOrder = productRecord[PRODUCT.TABLET_ORDER]
                    )
                } ?: emptyList()

                VipProductCategoryResponse(
                    id = record[PRODUCT_CATEGORY.ID],
                    title = record[PRODUCT_CATEGORY.TITLE],
                    imageFileUrl = record[PRODUCT_CATEGORY.file().ID]?.let {
                        fileStorageService.getFileUrl(
                            fileId = record[PRODUCT_CATEGORY.file().ID],
                            fileExtension = record[PRODUCT_CATEGORY.file().EXTENSION],
                            fileType = record[PRODUCT_CATEGORY.file().TYPE]
                        )
                    },
                    order = record[PRODUCT_CATEGORY.ORDER],
                    taxRate = cinemaxConfigProperties.getTaxRate(TaxRateType.STANDARD),
                    products = productsList
                )
            }.toList()
    }

    private val stockQuantity =
        `when`(
            PRODUCT_COMPOSITION.PRODUCT_COMPONENT_ID.isNotNull,
            PRODUCT_COMPONENT_STOCK_QUANTITY
        ).`when`(
            PRODUCT_COMPOSITION.PRODUCT_IN_PRODUCT_ID.isNotNull,
            PRODUCT_IN_PRODUCT_STOCK_QUANTITY
        ).otherwise(BigDecimal.ZERO)

    private val minQuantityCondition = exists(
        select(min(stockQuantity))
            .from(PRODUCT_COMPOSITION)
            .leftJoin(PRODUCT_COMPOSITION.productComponent())
            .where(PRODUCT_COMPOSITION.PRODUCT_ID.eq(PRODUCT.ID))
            .having(min(stockQuantity).gt(BigDecimal.ONE))
    )

    private val currencySymbol = ExportCurrency.fromBusinessCountry(cinemaxConfigProperties.businessCountry)
}
