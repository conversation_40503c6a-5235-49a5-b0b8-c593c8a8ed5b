package com.cleevio.cinemax.api.module.productcomposition.service

import com.cleevio.cinemax.api.module.productcomposition.entity.ProductComposition
import com.cleevio.cinemax.api.module.productcomposition.exception.ProductCompositionNotFoundException
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class ProductCompositionJooqFinderService(
    private val productCompositionJooqFinderRepository: ProductCompositionJooqFinderRepository,
) {

    fun findAll(): List<ProductComposition> {
        return productCompositionJooqFinderRepository.findAll()
    }

    fun findById(id: UUID): ProductComposition? {
        return productCompositionJooqFinderRepository.findById(id)
    }

    fun getById(id: UUID): ProductComposition {
        return findById(id) ?: throw ProductCompositionNotFoundException()
    }

    fun findByOriginalId(originalId: Int): ProductComposition? {
        return productCompositionJooqFinderRepository.findByOriginalId(originalId)
    }

    fun findAllByProductIdIn(productIds: Set<UUID>): List<ProductComposition> {
        return productCompositionJooqFinderRepository.findAllByProductIdIn(productIds)
    }

    fun findAllProductIdsSharingSameProductComponents(productId: UUID): Set<UUID> {
        return productCompositionJooqFinderRepository.findAllProductIdsSharingSameProductComponents(productId)
    }
}
