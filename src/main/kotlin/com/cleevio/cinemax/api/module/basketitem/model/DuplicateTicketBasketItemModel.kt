package com.cleevio.cinemax.api.module.basketitem.model

import java.time.LocalDateTime
import java.util.UUID

interface DuplicateTicketBasketItemModelProjection {
    val basketItemId: UUID
    val basketItemCreatedAt: LocalDateTime
    val receiptNumber: String
}

data class DuplicateTicketBasketItemModel(
    override val basketItemId: UUID,
    override val basketItemCreatedAt: LocalDateTime,
    override val receiptNumber: String,
) : DuplicateTicketBasketItemModelProjection
