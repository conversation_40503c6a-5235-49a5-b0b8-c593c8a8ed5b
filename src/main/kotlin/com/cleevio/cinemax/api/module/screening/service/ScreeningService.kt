package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.common.constant.SCREENING
import com.cleevio.cinemax.api.common.service.Action
import com.cleevio.cinemax.api.common.util.ifFalse
import com.cleevio.cinemax.api.common.util.ifTrue
import com.cleevio.cinemax.api.common.util.isInInterval
import com.cleevio.cinemax.api.common.util.isInThePast
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.common.util.truncatedToMinutes
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumJpaFinderService
import com.cleevio.cinemax.api.module.auditoriumdefault.entity.AuditoriumDefault
import com.cleevio.cinemax.api.module.auditoriumdefault.service.AuditoriumDefaultJpaFinderService
import com.cleevio.cinemax.api.module.auditoriumlayout.exception.AuditoriumLayoutNotFoundException
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutJpaFinderService
import com.cleevio.cinemax.api.module.movie.service.MovieJpaFinderService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryAutoSelectService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryJpaFinderService
import com.cleevio.cinemax.api.module.reservation.event.ScreeningWithNonDefaultLayoutSyncedFromMssqlEvent
import com.cleevio.cinemax.api.module.screening.constant.ScreeningLockValues.CREATE_OR_UPDATE_SCREENING
import com.cleevio.cinemax.api.module.screening.constant.ScreeningLockValues.DELETE_SCREENING
import com.cleevio.cinemax.api.module.screening.constant.ScreeningLockValues.DUPLICATE_SCREENING
import com.cleevio.cinemax.api.module.screening.constant.ScreeningLockValues.DUPLICATE_SCREENING_DAY
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.entity.Screening
import com.cleevio.cinemax.api.module.screening.event.AdminScreeningCreatedEvent
import com.cleevio.cinemax.api.module.screening.event.PublishedScreeningCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.screening.event.ScreeningCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.screening.event.ScreeningsPublishedEvent
import com.cleevio.cinemax.api.module.screening.event.ScreeningsSwitchedToDraftOrDeletedEvent
import com.cleevio.cinemax.api.module.screening.exception.AuditoriumForScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.exception.DestinationDateInThePastException
import com.cleevio.cinemax.api.module.screening.exception.MovieForScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.exception.PriceCategoryForScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningDateTimeConflictException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningDateTimeInThePastException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningInPublishedStateException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningWithTicketsSoldException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningsDateTimeConflictException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningsWithTicketsSoldException
import com.cleevio.cinemax.api.module.screening.service.command.CreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.module.screening.service.command.DeleteScreeningCommand
import com.cleevio.cinemax.api.module.screening.service.command.DuplicateScreeningCommand
import com.cleevio.cinemax.api.module.screening.service.command.DuplicateScreeningDayCommand
import com.cleevio.cinemax.api.module.screening.service.command.MessagingCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.module.screening.service.command.UpdateScreeningOriginalIdCommand
import com.cleevio.cinemax.api.module.screening.service.command.UpdateScreeningStatesCommand
import com.cleevio.cinemax.api.module.screeningfee.entity.ScreeningFee
import com.cleevio.cinemax.api.module.screeningfee.exception.ScreeningFeeNotFoundException
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeJpaFinderService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.screeningfee.service.command.CreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.module.screeningtype.exception.ScreeningTypeNotFoundException
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeJpaFinderService
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesJpaFinderService
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesService
import com.cleevio.cinemax.api.module.screeningtypes.service.command.DeleteAndCreateScreeningTypesCommand
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import com.cleevio.library.lockinghandler.service.LockService
import jakarta.validation.Valid
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.math.BigDecimal
import java.time.Clock
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID
import kotlin.jvm.optionals.getOrNull

@Service
@Validated
class ScreeningService(
    private val screeningRepository: ScreeningRepository,
    private val screeningJpaFinderService: ScreeningJpaFinderService,
    private val auditoriumLayoutJpaFinderService: AuditoriumLayoutJpaFinderService,
    private val auditoriumJpaFinderService: AuditoriumJpaFinderService,
    private val auditoriumDefaultJpaFinderService: AuditoriumDefaultJpaFinderService,
    private val movieJpaFinderService: MovieJpaFinderService,
    private val priceCategoryJpaFinderService: PriceCategoryJpaFinderService,
    private val priceCategoryAutoSelectService: PriceCategoryAutoSelectService,
    private val screeningTypeJpaFinderService: ScreeningTypeJpaFinderService,
    private val screeningTypesJpaFinderService: ScreeningTypesJpaFinderService,
    private val screeningFeeJpaFinderService: ScreeningFeeJpaFinderService,
    private val screeningFeeService: ScreeningFeeService,
    private val screeningTypesService: ScreeningTypesService,
    private val applicationEventPublisher: ApplicationEventPublisher,

    @Suppress("SpringJavaInjectionPointsAutowiringInspection")
    private val lockService: LockService,
    private val clock: Clock,
) {
    private val log = logger()

    @Transactional
    @Lock(SCREENING, CREATE_OR_UPDATE_SCREENING)
    fun syncCreateOrUpdateScreening(
        @Valid
        @LockFieldParameter("originalId")
        command: CreateOrUpdateScreeningCommand,
    ) {
        requireNotNull(command.originalId) { "Attribute 'originalId' must not be null." }

        val screening = screeningJpaFinderService.findByOriginalId(command.originalId)
        if (screening != null && screening.isDeleted()) {
            log.warn("Screening with originalId ${screening.originalId} has been already deleted. Skipping.")
            return
        }

        internalCreateOrUpdate(
            screening = screening,
            command = command
        ).let {
            if (auditoriumLayoutJpaFinderService.existsNonDeletedByIdAndIsNotDefault(it.auditoriumLayoutId)) {
                applicationEventPublisher.publishEvent(
                    ScreeningWithNonDefaultLayoutSyncedFromMssqlEvent(command.originalId)
                )
            }
        }
    }

    @Transactional
    @Lock(SCREENING, CREATE_OR_UPDATE_SCREENING)
    fun adminCreateOrUpdateScreening(
        @Valid
        @LockFieldParameter("id")
        command: CreateOrUpdateScreeningCommand,
    ) {
        val screening = command.id?.let { screeningJpaFinderService.getNonDeletedById(it) }
        val auditoriumId = screening?.auditoriumId
        val screeningDateTime = screening?.getScreeningTime()

        if (command.state == ScreeningState.PUBLISHED || screening?.state == ScreeningState.PUBLISHED) {
            existsPublishedInRequestedTimeRangeForCommand(command).ifTrue {
                throw ScreeningDateTimeConflictException()
            }

            when (command.getAction()) {
                Action.CREATE -> {
                    isScreeningDateTimeInThePast(date = command.date, time = command.time).ifTrue {
                        throw ScreeningDateTimeInThePastException()
                    }
                }

                Action.UPDATE -> {
                    screening?.let {
                        if (hasUnmodifiableAttributeChanged(it, command)) {
                            throw ScreeningInPublishedStateException()
                        }
                    } ?: error("Null screening when updating.")
                }
            }
        }

        internalCreateOrUpdate(
            screening = screening,
            command = command
        ).let {
            createOrUpdateScreeningFee(screening = it, command = command, sourceAuditoriumId = auditoriumId)
            updatePriceCategory(screening = it, screeningDateTime = screeningDateTime, command = command)
            deleteAndCreateScreeningTypes(
                screeningId = it.id,
                screeningTypeIds = resolveScreeningTypes(
                    screeningId = command.id,
                    sourceAuditoriumId = auditoriumId,
                    destinationAuditoriumId = command.auditoriumId,
                    requestedScreeningTypeIds = command.screeningTypeIds
                )
            )

            if (it.state == ScreeningState.PUBLISHED) {
                if (command.getAction() == Action.CREATE) {
                    applicationEventPublisher.publishEvent(
                        AdminScreeningCreatedEvent(
                            auditoriumLayoutId = it.auditoriumLayoutId,
                            screeningId = it.id
                        )
                    )
                }

                applicationEventPublisher.publishEvent(
                    PublishedScreeningCreatedOrUpdatedEvent(screeningId = it.id)
                )
            }

            applicationEventPublisher.publishEvent(
                ScreeningCreatedOrUpdatedEvent(screeningId = it.id)
            )
        }
    }

    @Transactional
    @Lock(SCREENING, CREATE_OR_UPDATE_SCREENING)
    fun messagingCreateOrUpdateScreening(
        @Valid
        @LockFieldParameter("id")
        command: MessagingCreateOrUpdateScreeningCommand,
    ) {
        val auditorium = auditoriumJpaFinderService.findByOriginalCode(command.auditoriumOriginalCode)
            ?: error("Auditorium for Screening ${command.id} messaging not found.")
        val auditoriumLayout = auditoriumLayoutJpaFinderService.getDefaultByAuditoriumId(auditorium.id)
        val movie = movieJpaFinderService.findNonDeletedByMessagingCode(command.movieMessagingCode)
            ?: error("Movie for Screening ${command.id} messaging not found.")

        val screening = screeningRepository.save(
            Screening(
                id = command.id,
                originalId = null,
                auditoriumId = auditorium.id,
                auditoriumLayoutId = auditoriumLayout.id,
                movieId = movie.id,
                priceCategoryId = command.priceCategoryId,
                date = command.date,
                time = command.time,
                saleTimeLimit = command.saleTimeLimit,
                stopped = command.stopped,
                cancelled = command.cancelled,
                proCommission = command.proCommission,
                filmFondCommission = command.filmFondCommission,
                distributorCommission = command.distributorCommission,
                publishOnline = command.publishOnline,
                state = command.state,
                adTimeSlot = command.adTimeSlot
            )
        )

        val screeningTypes = screeningTypeJpaFinderService.findAllByCodeIn(command.screeningTypeCodes)
        deleteAndCreateScreeningTypes(
            screeningId = screening.id,
            screeningTypeIds = screeningTypes.mapToSet { screeningType -> screeningType.id }
        )

        screeningFeeService.createOrUpdateScreeningFee(
            CreateOrUpdateScreeningFeeCommand(
                originalScreeningId = screening.originalId,
                screeningId = screening.id,
                surchargeVip = command.surchargeVip,
                surchargePremium = command.surchargePremium,
                surchargeImax = command.surchargeImax,
                surchargeUltraX = command.surchargeUltraX,
                surchargeDBox = command.surchargeDBox,
                serviceFeeVip = command.serviceFeeVip,
                serviceFeePremium = command.serviceFeePremium,
                serviceFeeImax = command.serviceFeeImax,
                serviceFeeUltraX = command.serviceFeeUltraX,
                serviceFeeGeneral = command.serviceFeeGeneral
            )
        )
    }

    @Transactional
    @Lock(SCREENING, DELETE_SCREENING)
    fun syncDeleteScreening(
        @Valid
        @LockFieldParameter("id")
        command: DeleteScreeningCommand,
    ) {
        screeningRepository.findById(command.id).getOrNull()?.let {
            screeningRepository.save(
                it.apply { markDeleted() }
            )
        }
    }

    @Transactional
    @Lock(SCREENING, DELETE_SCREENING)
    fun deleteScreening(
        @Valid
        @LockFieldParameter("id")
        command: DeleteScreeningCommand,
    ) {
        screeningJpaFinderService.getNonDeletedById(command.id).let {
            if (it.state == ScreeningState.PUBLISHED) throw ScreeningInPublishedStateException()
            screeningRepository.save(
                it.apply { markDeleted() }
            ).also { screening ->
                if (screening.originalId != null) {
                    applicationEventPublisher.publishEvent(ScreeningsSwitchedToDraftOrDeletedEvent(setOf(screening.id)))
                }
            }
        }
    }

    @Transactional
    @Lock(SCREENING, CREATE_OR_UPDATE_SCREENING)
    fun updateScreeningOriginalId(
        @Valid
        @LockFieldParameter("screeningId")
        command: UpdateScreeningOriginalIdCommand,
    ) {
        screeningJpaFinderService.getNonDeletedById(command.screeningId).let {
            screeningRepository.save(
                it.apply {
                    originalId = command.originalId
                }
            )
        }
    }

    @Transactional
    @Lock(SCREENING, DUPLICATE_SCREENING)
    fun duplicateScreening(
        @Valid
        @LockFieldParameter("screeningId")
        command: DuplicateScreeningCommand,
    ) {
        val sourceScreening = screeningJpaFinderService.getNonDeletedById(command.screeningId)
        val sourceScreeningFee = screeningFeeJpaFinderService.getByScreeningId(command.screeningId)
        val sourceScreeningTypeIds =
            screeningTypesJpaFinderService.findAllNonBlacklistedScreeningTypeIdsByScreeningId(sourceScreening.id)

        auditoriumJpaFinderService.existsById(command.auditoriumId).ifFalse {
            throw AuditoriumForScreeningNotFoundException()
        }

        val auditoriumLayoutId = if (sourceScreening.auditoriumId == command.auditoriumId) {
            sourceScreening.auditoriumLayoutId
        } else {
            auditoriumLayoutJpaFinderService.getDefaultByAuditoriumId(command.auditoriumId).id
        }

        val screeningFee = if (sourceScreening.auditoriumId == command.auditoriumId) {
            sourceScreeningFee
        } else {
            auditoriumDefaultJpaFinderService.getByAuditoriumId(command.auditoriumId).toScreeningFee(
                screeningId = command.screeningId,
                serviceFeeGeneral = sourceScreeningFee.serviceFeeGeneral
            )
        }

        val priceCategoryId = autoSelectPriceCategoryId(
            sourceDate = sourceScreening.date,
            sourceTime = sourceScreening.time,
            destinationTime = command.time,
            destinationDate = sourceScreening.date
        ) ?: sourceScreening.priceCategoryId

        internalDuplicateScreening(
            screening = sourceScreening,
            destinationAuditoriumId = command.auditoriumId,
            destinationTime = command.time,
            screeningTypeIds = resolveScreeningTypes(
                screeningId = sourceScreening.id,
                sourceAuditoriumId = sourceScreening.auditoriumId,
                destinationAuditoriumId = command.auditoriumId,
                requestedScreeningTypeIds = sourceScreeningTypeIds
            ),
            screeningFee = screeningFee,
            auditoriumLayoutId = auditoriumLayoutId,
            priceCategoryId = priceCategoryId
        )
    }

    @Transactional
    @Lock(SCREENING, DUPLICATE_SCREENING_DAY)
    fun duplicateScreeningDay(
        @Valid
        @LockFieldParameter("sourceDate")
        command: DuplicateScreeningDayCommand,
    ) {
        if (command.destinationDates.any { it.isInThePast() }) throw DestinationDateInThePastException()

        val screenings = screeningJpaFinderService.findAllNonDeletedByDate(command.sourceDate)
        val screeningIds = screenings.mapToSet { it.id }

        val screeningIdToScreeningTypeIds = screeningTypesJpaFinderService.findAllNonBlacklistedByScreeningIdIn(screeningIds)
            .groupBy { it.screeningId }
            .mapValues { it.value.mapToSet { screeningTypes -> screeningTypes.screeningTypeId } }
        val screeningIdToScreeningFee = screeningFeeJpaFinderService.findAllByScreeningIdIn(screeningIds)
            .associateBy { it.screeningId }
        val screeningIdToAuditoriumLayoutId = screenings.associate { it.id to it.auditoriumLayoutId }

        screenings.forEach {
            command.destinationDates.forEach { date ->
                internalDuplicateScreening(
                    screening = it,
                    destinationAuditoriumId = it.auditoriumId,
                    destinationDate = date,
                    destinationTime = it.time,
                    screeningTypeIds = screeningIdToScreeningTypeIds[it.id],
                    screeningFee = screeningIdToScreeningFee[it.id] ?: throw ScreeningFeeNotFoundException(),
                    auditoriumLayoutId = screeningIdToAuditoriumLayoutId[it.id]
                        ?: throw AuditoriumLayoutNotFoundException(),
                    priceCategoryId = autoSelectPriceCategoryId(
                        sourceDate = it.date,
                        sourceTime = it.time,
                        destinationTime = it.time,
                        destinationDate = date
                    ) ?: it.priceCategoryId
                )
            }
        }
    }

    @Transactional
    fun updateScreeningStates(@Valid command: UpdateScreeningStatesCommand) {
        val invalidScreeningIds: MutableList<UUID> = mutableListOf()
        val skipDateTimePastValidation = command.screeningIds.size > 1
        var apiException: Throwable? = null

        command.screeningIds.forEach { screeningId ->
            runCatching {
                lockService.obtainBlockingLock(
                    module = SCREENING,
                    lockName = CREATE_OR_UPDATE_SCREENING,
                    screeningId.toString()
                ).use {
                    updateScreeningState(
                        screeningId = screeningId,
                        state = command.screeningState,
                        skipDateTimePastValidation = skipDateTimePastValidation
                    )
                }
            }.onFailure { ex ->
                if (ex is ScreeningNotFoundException) throw ex
                invalidScreeningIds += screeningId
                apiException = ex
            }
        }

        if (invalidScreeningIds.isNotEmpty()) {
            when (apiException) {
                is ScreeningDateTimeConflictException -> throw ScreeningsDateTimeConflictException(
                    invalidScreeningIds
                )

                is ScreeningWithTicketsSoldException -> throw ScreeningsWithTicketsSoldException(invalidScreeningIds)
                else -> apiException?.let { throw it }
            }
        }

        when (command.screeningState) {
            ScreeningState.PUBLISHED -> applicationEventPublisher.publishEvent(ScreeningsPublishedEvent(command.screeningIds))
            ScreeningState.DRAFT -> {
                screeningRepository.findAllAlreadySyncedAndNonDeletedIdsIn(command.screeningIds).toSet().let {
                    applicationEventPublisher.publishEvent(
                        ScreeningsSwitchedToDraftOrDeletedEvent(it)
                    )
                }
            }
        }
    }

    private fun resolveScreeningTypes(
        screeningId: UUID?,
        sourceAuditoriumId: UUID?,
        destinationAuditoriumId: UUID,
        requestedScreeningTypeIds: Set<UUID>?,
    ): Set<UUID>? {
        if (screeningId == null || sourceAuditoriumId == destinationAuditoriumId) {
            return requestedScreeningTypeIds
        }

        return screeningTypeJpaFinderService.findScreeningTypesByScreeningIdAndAuditoriumId(
            requestedScreeningTypeIds = requestedScreeningTypeIds,
            screeningId = screeningId,
            auditoriumId = destinationAuditoriumId
        )
    }

    private fun autoSelectPriceCategoryId(
        sourceDate: LocalDate,
        sourceTime: LocalTime,
        destinationTime: LocalTime,
        destinationDate: LocalDate,
    ): UUID? {
        if (!FIVE_PM.isInInterval(sourceTime, destinationTime) && sourceDate == destinationDate) return null

        val firstAutoSelectTitle = priceCategoryAutoSelectService.resolvePriceCategoryAutoSelectionTitleFilter(
            dateTime = LocalDateTime.of(destinationDate, destinationTime)
        ).also {
            if (it.size > 1) {
                log.error("Multiple price category titles determined from auto select=$it, resolving first one.")
            }
        }.firstOrNull()

        val firstAutoSelectPriceCategory = priceCategoryJpaFinderService.findAllActiveByTitleCaseInsensitive(
            title = firstAutoSelectTitle ?: ""
        ).also {
            if (it.size > 1) {
                log.error("Multiple price categories found for title=$firstAutoSelectTitle, resolving first one.")
            }
        }.firstOrNull()

        return firstAutoSelectPriceCategory?.id
    }

    private fun updateScreeningState(screeningId: UUID, state: ScreeningState, skipDateTimePastValidation: Boolean = false) {
        val screening = screeningJpaFinderService.getNonDeletedById(screeningId)
        if (screening.isToBePublished(state)) {
            if (!skipDateTimePastValidation) {
                isScreeningDateTimeInThePast(date = screening.date, time = screening.time).ifTrue {
                    throw ScreeningDateTimeInThePastException()
                }
            }

            existsPublishedInRequestedTimeRangeForScreening(screening).ifTrue {
                throw ScreeningDateTimeConflictException()
            }
        }

        if (screening.isToBeRevertedToDraft(state)) {
            screeningJpaFinderService.existSoldTicketsForScreening(screeningId).ifTrue {
                throw ScreeningWithTicketsSoldException()
            }
        }

        screeningRepository.save(
            screening.also { it.state = state }
        )
    }

    private fun internalCreateOrUpdate(screening: Screening?, command: CreateOrUpdateScreeningCommand): Screening {
        auditoriumLayoutJpaFinderService.existsNonDeletedById(command.auditoriumLayoutId).ifFalse {
            throw AuditoriumLayoutNotFoundException()
        }
        auditoriumJpaFinderService.existsById(command.auditoriumId).ifFalse {
            throw AuditoriumForScreeningNotFoundException()
        }
        movieJpaFinderService.existsNonDeletedById(command.movieId).ifFalse {
            throw MovieForScreeningNotFoundException()
        }
        priceCategoryJpaFinderService.existsNonDeletedById(command.priceCategoryId).ifFalse {
            throw PriceCategoryForScreeningNotFoundException()
        }
        command.screeningTypeIds?.let {
            screeningTypeJpaFinderService.existsAllByIdIn(it).ifFalse {
                throw ScreeningTypeNotFoundException()
            }
        }

        return screeningRepository.save(
            screening?.let { mapToExistingScreening(it, command) } ?: mapToNewScreening(command)
        )
    }

    private fun internalDuplicateScreening(
        screening: Screening,
        destinationAuditoriumId: UUID,
        destinationDate: LocalDate? = null,
        destinationTime: LocalTime,
        screeningTypeIds: Set<UUID>?,
        screeningFee: ScreeningFee,
        auditoriumLayoutId: UUID,
        priceCategoryId: UUID,
    ) {
        adminCreateOrUpdateScreening(
            mapToCreateOrUpdateScreeningCommand(
                screening = screening,
                screeningTypeIds = screeningTypeIds,
                screeningFee = screeningFee
            ).copy(
                auditoriumId = destinationAuditoriumId,
                date = destinationDate ?: screening.date,
                time = destinationTime,
                auditoriumLayoutId = auditoriumLayoutId,
                priceCategoryId = priceCategoryId
            )
        )
    }

    private fun createOrUpdateScreeningFee(
        screening: Screening,
        command: CreateOrUpdateScreeningCommand,
        sourceAuditoriumId: UUID? = null,
    ) {
        val screeningFeeCommand = when (command.getAction()) {
            Action.CREATE -> mapToCreateOrUpdateScreeningFeeCommand(screening, command)
            Action.UPDATE -> {
                requireNotNull(sourceAuditoriumId) {
                    "Source auditorium id must be not null when screening is being updated."
                }

                if (sourceAuditoriumId == command.auditoriumId) {
                    mapToCreateOrUpdateScreeningFeeCommand(screening, command)
                } else {
                    val sourceScreeningFee = screeningFeeJpaFinderService.getByScreeningId(screening.id)
                    mapToCreateOrUpdateScreeningFeeCommand(
                        auditoriumDefault = auditoriumDefaultJpaFinderService.getByAuditoriumId(command.auditoriumId),
                        screeningFeeId = sourceScreeningFee.id,
                        screeningId = screening.id,
                        serviceFeeGeneral = sourceScreeningFee.serviceFeeGeneral
                    )
                }
            }
        }

        screeningFeeService.createOrUpdateScreeningFee(screeningFeeCommand)
    }

    private fun mapToCreateOrUpdateScreeningFeeCommand(
        screening: Screening,
        command: CreateOrUpdateScreeningCommand,
    ) = CreateOrUpdateScreeningFeeCommand(
        originalScreeningId = screening.originalId,
        screeningId = screening.id,
        surchargeVip = command.surchargeVip,
        surchargePremium = command.surchargePremium,
        surchargeImax = command.surchargeImax,
        surchargeUltraX = command.surchargeUltraX,
        serviceFeeVip = command.serviceFeeVip,
        serviceFeePremium = command.serviceFeePremium,
        serviceFeeImax = command.serviceFeeImax,
        serviceFeeUltraX = command.serviceFeeUltraX,
        surchargeDBox = command.surchargeDBox,
        serviceFeeGeneral = command.serviceFeeGeneral
    )

    private fun mapToCreateOrUpdateScreeningFeeCommand(
        auditoriumDefault: AuditoriumDefault,
        screeningFeeId: UUID,
        screeningId: UUID,
        serviceFeeGeneral: BigDecimal,
    ) = CreateOrUpdateScreeningFeeCommand(
        id = screeningFeeId,
        screeningId = screeningId,
        originalScreeningId = null,
        surchargeVip = auditoriumDefault.surchargeVip,
        surchargePremium = auditoriumDefault.surchargePremium,
        surchargeImax = auditoriumDefault.surchargeImax,
        surchargeUltraX = auditoriumDefault.surchargeUltraX,
        serviceFeeVip = auditoriumDefault.serviceFeeVip,
        serviceFeePremium = auditoriumDefault.serviceFeePremium,
        serviceFeeImax = auditoriumDefault.serviceFeeImax,
        serviceFeeUltraX = auditoriumDefault.serviceFeeUltraX,
        surchargeDBox = auditoriumDefault.surchargeDBox,
        serviceFeeGeneral = serviceFeeGeneral
    )

    private fun updatePriceCategory(
        screening: Screening,
        screeningDateTime: LocalDateTime? = null,
        command: CreateOrUpdateScreeningCommand,
    ) {
        if (command.getAction() == Action.UPDATE) {
            requireNotNull(screeningDateTime) { "Screening date time must be not null when screening is being updated." }

            // if the screening time is 17:00, and it has non-auto selectable price category, don't override it with autoselect
            if (screeningDateTime.toLocalTime() == FIVE_PM) {
                val currentPriceCategory = priceCategoryJpaFinderService.getNonDeletedById(screening.priceCategoryId)
                if (!currentPriceCategory.isAutoSelectable()) return
            }

            autoSelectPriceCategoryId(
                sourceDate = screeningDateTime.toLocalDate(),
                sourceTime = screeningDateTime.toLocalTime(),
                destinationTime = command.time,
                destinationDate = command.date
            )?.let { priceCategoryId ->
                screeningRepository.save(
                    mapToExistingScreening(
                        screening,
                        command.copy(priceCategoryId = priceCategoryId)
                    )
                )
            }
        }
    }

    private fun deleteAndCreateScreeningTypes(screeningId: UUID, screeningTypeIds: Set<UUID>?) {
        screeningTypeIds?.let {
            screeningTypesService.deleteAndCreateScreeningTypes(
                DeleteAndCreateScreeningTypesCommand(
                    screeningId = screeningId,
                    screeningTypeIds = it
                )
            )
        }
    }

    private fun hasUnmodifiableAttributeChanged(
        screening: Screening,
        command: CreateOrUpdateScreeningCommand,
    ) = command.date != screening.date ||
        command.time != screening.time ||
        command.auditoriumId != screening.auditoriumId ||
        command.auditoriumLayoutId != screening.auditoriumLayoutId

    private fun mapToExistingScreening(screening: Screening, command: CreateOrUpdateScreeningCommand) =
        screening.apply {
            auditoriumId = command.auditoriumId
            auditoriumLayoutId = command.auditoriumLayoutId
            movieId = command.movieId
            priceCategoryId = command.priceCategoryId
            date = command.date
            time = command.time
            saleTimeLimit = command.saleTimeLimit
            stopped = command.stopped
            cancelled = command.cancelled
            proCommission = command.proCommission
            filmFondCommission = command.filmFondCommission
            distributorCommission = command.distributorCommission
            publishOnline = command.publishOnline
            adTimeSlot = command.adTimeSlot
        }

    private fun mapToNewScreening(command: CreateOrUpdateScreeningCommand) = Screening(
        id = command.id ?: UUID.randomUUID(),
        originalId = command.originalId,
        auditoriumId = command.auditoriumId,
        auditoriumLayoutId = command.auditoriumLayoutId,
        movieId = command.movieId,
        priceCategoryId = command.priceCategoryId,
        date = command.date,
        time = command.time,
        saleTimeLimit = command.saleTimeLimit,
        stopped = command.stopped,
        cancelled = command.cancelled,
        proCommission = command.proCommission,
        filmFondCommission = command.filmFondCommission,
        distributorCommission = command.distributorCommission,
        publishOnline = command.publishOnline,
        state = requireNotNull(command.state),
        adTimeSlot = command.adTimeSlot
    )

    private fun mapToCreateOrUpdateScreeningCommand(
        screening: Screening,
        screeningTypeIds: Set<UUID>?,
        screeningFee: ScreeningFee,
    ) = CreateOrUpdateScreeningCommand(
        originalId = null,
        movieId = screening.movieId,
        auditoriumId = screening.auditoriumId,
        priceCategoryId = screening.priceCategoryId,
        auditoriumLayoutId = screening.auditoriumLayoutId,
        screeningTypeIds = screeningTypeIds,
        date = screening.date,
        time = screening.time,
        state = ScreeningState.DRAFT,
        saleTimeLimit = screening.saleTimeLimit,
        adTimeSlot = screening.adTimeSlot,
        surchargeVip = screeningFee.surchargeVip,
        surchargePremium = screeningFee.surchargePremium,
        surchargeImax = screeningFee.surchargeImax,
        surchargeUltraX = screeningFee.surchargeUltraX,
        serviceFeeVip = screeningFee.serviceFeeVip,
        serviceFeePremium = screeningFee.serviceFeePremium,
        serviceFeeImax = screeningFee.serviceFeeImax,
        serviceFeeUltraX = screeningFee.serviceFeeUltraX,
        surchargeDBox = screeningFee.surchargeDBox,
        serviceFeeGeneral = screeningFee.serviceFeeGeneral,
        proCommission = screening.proCommission,
        filmFondCommission = screening.filmFondCommission,
        distributorCommission = screening.distributorCommission,
        publishOnline = screening.publishOnline,
        stopped = screening.stopped,
        cancelled = screening.cancelled
    )

    private fun isScreeningDateTimeInThePast(date: LocalDate, time: LocalTime) =
        LocalDateTime.now(clock).truncatedToMinutes().isAfter(
            LocalDateTime.of(
                date.year,
                date.monthValue,
                date.dayOfMonth,
                time.hour,
                time.minute,
                0,
                0
            )
        )

    private fun existsPublishedInRequestedTimeRangeForCommand(
        command: CreateOrUpdateScreeningCommand,
    ): Boolean = screeningJpaFinderService.existsPublishedInRequestedTimeRange(
        screeningId = command.id,
        auditoriumId = command.auditoriumId,
        movieId = command.movieId,
        date = command.date,
        time = command.time,
        adTimeSlot = command.adTimeSlot,
        cancelOrStopScreening = command.cancelled || command.stopped
    )

    private fun existsPublishedInRequestedTimeRangeForScreening(
        screening: Screening,
    ): Boolean = screeningJpaFinderService.existsPublishedInRequestedTimeRange(
        screeningId = screening.id,
        auditoriumId = screening.auditoriumId,
        movieId = screening.movieId,
        date = screening.date,
        time = screening.time,
        adTimeSlot = screening.adTimeSlot,
        cancelOrStopScreening = screening.cancelled || screening.stopped
    )
}

private val FIVE_PM = LocalTime.of(17, 0)
