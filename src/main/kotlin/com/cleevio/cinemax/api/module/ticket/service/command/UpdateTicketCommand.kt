package com.cleevio.cinemax.api.module.ticket.service.command

import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import java.util.Optional
import java.util.UUID

data class UpdateTicketCommand(
    val ticketId: UUID,
    val priceCategoryItemNumber: PriceCategoryItemNumber? = null,
    val primaryTicketDiscountId: Optional<UUID>? = null,
    val secondaryTicketDiscountId: Optional<UUID>? = null,
    val freeTicket: Boolean,
)
