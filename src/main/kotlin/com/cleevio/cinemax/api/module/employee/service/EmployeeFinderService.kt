package com.cleevio.cinemax.api.module.employee.service

import com.cleevio.cinemax.api.common.service.query.SearchUnfilteredQueryDeprecated
import com.cleevio.cinemax.api.common.util.search
import com.cleevio.cinemax.api.module.employee.entity.Employee
import com.cleevio.cinemax.api.module.employee.exception.EmployeeNotFoundException
import jakarta.validation.Valid
import org.springframework.data.domain.Page
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class EmployeeFinderService(
    private val employeeFinderRepository: EmployeeFinderRepository,
) {

    fun findAll(): List<Employee> {
        return employeeFinderRepository.findAll()
    }

    fun findAllNonDeleted(): List<Employee> {
        return employeeFinderRepository.findAllNonDeleted()
    }

    fun findAllByIdIn(ids: Set<UUID>): List<Employee> {
        return employeeFinderRepository.findAllByIdIn(ids)
    }

    fun findById(id: UUID): Employee? {
        return employeeFinderRepository.findById(id)
    }

    fun getById(id: UUID): Employee {
        return findById(id) ?: throw EmployeeNotFoundException()
    }

    fun findNonDeletedById(id: UUID): Employee? {
        return employeeFinderRepository.findNonDeletedById(id)
    }

    fun getNonDeletedById(id: UUID): Employee {
        return findNonDeletedById(id) ?: throw EmployeeNotFoundException()
    }

    fun findNonDeletedByOriginalId(originalId: Int): Employee? {
        return employeeFinderRepository.findNonDeletedByOriginalId(originalId)
    }

    fun findNonDeletedByUsername(username: String): Employee? {
        return employeeFinderRepository.findNonDeletedByUsername(username)
    }

    fun findByUsername(username: String): Employee? {
        return employeeFinderRepository.findByUsername(username)
    }

    fun getNonDeletedByUsername(username: String): Employee {
        return findNonDeletedByUsername(username) ?: throw EmployeeNotFoundException()
    }

    fun search(@Valid query: SearchUnfilteredQueryDeprecated): Page<Employee> {
        return search(
            query,
            employeeFinderRepository::searchRowsCount,
            employeeFinderRepository::searchRows
        )
    }
}
