package com.cleevio.cinemax.api.module.basket.service.command

import com.cleevio.cinemax.api.common.annotation.NullOrNotBlank
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Positive
import java.math.BigDecimal
import java.util.UUID

data class MessagingCreateBasketItemCommand(
    val id: UUID,
    val basketId: UUID,
    val type: BasketItemType,
    val price: BigDecimal,

    @field:Positive
    val quantity: Int,
    val isCancelled: Boolean,

    @field:NullOrNotBlank
    val discountCardCode: String?,

    @field:NotBlank
    val branchCode: String,

    val product: BasketProductItem?,
    val ticket: BasketTicketItem?,
) {
    init {
        require((this.product != null && this.ticket == null) || (this.product == null && this.ticket != null)) {
            "Invalid CreateMessagingBasketItemCommand."
        }
    }

    data class BasketProductItem(

        @field:NotBlank
        val productCode: String,

        @field:NotBlank
        val productReceiptNumber: String,
    )

    data class BasketTicketItem(
        // TODO: refactor to UUIDs once MSSQL is gone
        val id: UUID,
        val screeningId: UUID,
        val auditoriumOriginalCode: Int,
        val seatOriginalId: Int,
        val basePriceItemNumber: PriceCategoryItemNumber,

        @field:NotBlank
        val ticketReceiptNumber: String,

        @field:NullOrNotBlank
        val ticketDiscountPrimaryCode: String? = null,

        @field:NullOrNotBlank
        val ticketDiscountSecondaryCode: String? = null,
        val isGroupTicket: Boolean,
        val isUsed: Boolean,
        val includes3dGlasses: Boolean,
    )
}
