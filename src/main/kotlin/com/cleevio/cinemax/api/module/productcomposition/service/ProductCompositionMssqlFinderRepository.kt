package com.cleevio.cinemax.api.module.productcomposition.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.DeploymentType
import com.cleevio.cinemax.api.common.util.buildGreaterThanDateTimeMssqlCondition
import com.cleevio.cinemax.mssql.rp_bufete.dbo.Tables.RPOLM
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Rpolm
import org.jooq.DSLContext
import org.jooq.impl.DSL.selectFrom
import org.jooq.impl.DSL.table
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class ProductCompositionMssqlFinderRepository(
    private val mssqlBuffetDslContext: DSLContext,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
) {

    fun findAll(): List<Rpolm> {
        return mssqlBuffetDslContext
            .selectFrom(RPOLM)
            .fetchInto(Rpolm::class.java)
    }

    fun findAllOriginalIds(): Set<Int> {
        return mssqlBuffetDslContext
            .select(RPOLM.RPOLMID)
            .from(RPOLM)
            .fetchInto(Int::class.java)
            .toSet()
    }

    fun findAllByUpdatedAtGt(updatedAt: LocalDateTime? = null): List<Rpolm> {
        return when (cinemaxConfigProperties.deploymentType) {
            DeploymentType.HEADQUARTERS -> {
                mssqlBuffetDslContext
                    .select(table(RPOLM.name).asterisk().except(RPOLM.RMENU2ID))
                    .from(RPOLM.name)
                    .where(buildGreaterThanDateTimeMssqlCondition(updatedAt, RPOLM.ZCAS))
                    .fetchInto(Rpolm::class.java)
            }

            DeploymentType.BRANCH -> {
                mssqlBuffetDslContext
                    .selectFrom(RPOLM)
                    .where(buildGreaterThanDateTimeMssqlCondition(updatedAt, RPOLM.ZCAS))
                    .fetchInto(Rpolm::class.java)
            }
        }
    }

    fun findByOriginalId(originalId: Int): Rpolm? {
        return mssqlBuffetDslContext
            .selectFrom(RPOLM)
            .where(RPOLM.RPOLMID.eq(originalId))
            .fetchOneInto(Rpolm::class.java)
    }

    fun existsProductInProductCompositionForProduct(productOriginalId: Int): Boolean {
        return mssqlBuffetDslContext
            .fetchExists(
                selectFrom(RPOLM)
                    .where(RPOLM.RMENUID.eq(productOriginalId)).and(RPOLM.RMENU2ID.ne(0))
                    .and(RPOLM.RZBOZIRID.eq(0))
            )
    }
}
