package com.cleevio.cinemax.api.module.productcomponent.service.command

import com.cleevio.cinemax.api.common.annotation.NullOrNotBlank
import com.cleevio.cinemax.api.common.service.CreateOrUpdateCommand
import com.cleevio.cinemax.api.common.validation.ValidTaxRate
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.math.BigDecimal
import java.util.UUID

data class CreateOrUpdateProductComponentCommand(
    override val id: UUID? = null,
    val originalId: Int? = null,
    val productComponentCategoryId: UUID,

    @field:NullOrNotBlank
    @field:Size(max = 5)
    val code: String? = null,

    @field:NotBlank
    @field:Size(max = 40)
    val title: String,
    val unit: ProductComponentUnit,
    val purchasePrice: BigDecimal,
    val stockQuantity: BigDecimal? = null,
    val active: Boolean,

    @field:ValidTaxRate
    val taxRateOverride: Int? = null,
) : CreateOrUpdateCommand()
