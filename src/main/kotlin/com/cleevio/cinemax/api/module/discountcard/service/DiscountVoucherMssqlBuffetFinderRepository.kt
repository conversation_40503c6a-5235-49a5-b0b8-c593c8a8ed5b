package com.cleevio.cinemax.api.module.discountcard.service

import com.cleevio.cinemax.api.common.util.buildGreaterThanDateTimeMssqlCondition
import com.cleevio.cinemax.mssql.rp_bufete.dbo.Tables.VOUCHER
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Voucher
import org.jooq.DSLContext
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

@Repository
class DiscountVoucherMssqlBuffetFinderRepository(
    private val mssqlBuffetDslContext: DSLContext,
) {

    fun findAll(): List<Voucher> {
        return mssqlBuffetDslContext
            .selectFrom(VOUCHER)
            .fetchInto(Voucher::class.java)
    }

    fun findAllByCodeIn(codes: Set<String>): List<Voucher> {
        return mssqlBuffetDslContext
            .selectFrom(VOUCHER)
            .where(VOUCHER.CISLO.`in`(codes))
            .fetchInto(Voucher::class.java)
    }

    fun findAllValidByUpdatedAtGt(updatedAt: LocalDateTime? = null): List<Voucher> {
        return mssqlBuffetDslContext
            .selectFrom(VOUCHER)
            .where(buildGreaterThanDateTimeMssqlCondition(updatedAt, VOUCHER.ZCAS))
            .and(VOUCHER.PLATDO.greaterOrEqual(LocalDateTime.now()))
            .fetchInto(Voucher::class.java)
    }
}
