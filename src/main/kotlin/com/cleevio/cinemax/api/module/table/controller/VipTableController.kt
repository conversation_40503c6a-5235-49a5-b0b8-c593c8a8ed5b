package com.cleevio.cinemax.api.module.table.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.CreateVipBasketService
import com.cleevio.cinemax.api.module.basket.service.DeleteVipBasketService
import com.cleevio.cinemax.api.module.basket.service.command.DeleteLatestVipBasketCommand
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.table.constant.TableType
import com.cleevio.cinemax.api.module.table.controller.dto.CreateVipBasketRequest
import com.cleevio.cinemax.api.module.table.controller.dto.VipTableResponse
import com.cleevio.cinemax.api.module.table.controller.mapper.VipTableResponseMapper
import com.cleevio.cinemax.api.module.table.service.GetVipTableBasketStateQueryService
import com.cleevio.cinemax.api.module.table.service.TableFinderService
import com.cleevio.cinemax.api.module.table.service.query.GetVipTableBasketStateQuery
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@RestController
@RequestMapping("/vip-app/tables")
class VipTableController(
    private val tableFinderService: TableFinderService,
    private val deleteVipBasketService: DeleteVipBasketService,
    private val createVipBasketService: CreateVipBasketService,
    private val vipTableResponseMapper: VipTableResponseMapper,
    private val getVipTableBasketStateQueryService: GetVipTableBasketStateQueryService,
) {

    @GetMapping(produces = [ApiVersion.VERSION_1_JSON])
    fun getVipTables(): List<VipTableResponse> {
        return vipTableResponseMapper.mapList(
            tables = tableFinderService.findAllByProductModeAndTableType(
                productMode = ProductMode.VIP,
                tableType = TableType.SEAT
            )
        )
    }

    @GetMapping("/{tableId}/basket-state", produces = [ApiVersion.VERSION_1_JSON])
    fun getVipTableBasketState(
        @PathVariable tableId: UUID,
    ): BasketState {
        return getVipTableBasketStateQueryService(GetVipTableBasketStateQuery(tableId))
    }

    @DeleteMapping("/{tableId}/basket", produces = [ApiVersion.VERSION_1_JSON])
    fun deleteBasketByTableId(
        @PathVariable tableId: UUID,
    ) {
        deleteVipBasketService(DeleteLatestVipBasketCommand(tableId = tableId))
    }

    @PostMapping("/{tableId}/basket", produces = [ApiVersion.VERSION_1_JSON])
    fun createBasketWithBasketItems(
        @PathVariable tableId: UUID,
        @RequestBody request: CreateVipBasketRequest,
    ) {
        createVipBasketService(request.toCommand(tableId))
    }
}
