package com.cleevio.cinemax.api.module.ticketdiscount.service

import com.cleevio.cinemax.api.common.util.buildUnaccentLikeIgnoreCaseCondition
import com.cleevio.cinemax.api.common.util.inOrNullIfNullOrEmpty
import com.cleevio.cinemax.api.common.util.paginationAndSorting
import com.cleevio.cinemax.api.module.ticketdiscount.controller.dto.AdminSearchTicketDiscountsResponse
import com.cleevio.cinemax.api.module.ticketdiscount.service.query.AdminSearchTicketDiscountsQuery
import com.cleevio.cinemax.psql.Tables.TICKET_DISCOUNT
import jakarta.validation.Valid
import org.jooq.DSLContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class AdminSearchTicketDiscountsQueryService(
    private val psqlDslContext: DSLContext,
) {

    operator fun invoke(
        @Valid query: AdminSearchTicketDiscountsQuery,
    ): Page<AdminSearchTicketDiscountsResponse> {
        val conditions = listOfNotNull(
            buildUnaccentLikeIgnoreCaseCondition(query.filter.title, TICKET_DISCOUNT.TITLE),
            TICKET_DISCOUNT.USAGE_TYPE.inOrNullIfNullOrEmpty(query.filter.usageTypes),
            TICKET_DISCOUNT.DELETED_AT.isNull
        )
        val count = psqlDslContext.fetchCount(TICKET_DISCOUNT, conditions)

        return psqlDslContext
            .select(
                TICKET_DISCOUNT.ID,
                TICKET_DISCOUNT.TITLE,
                TICKET_DISCOUNT.CODE,
                TICKET_DISCOUNT.TYPE,
                TICKET_DISCOUNT.USAGE_TYPE,
                TICKET_DISCOUNT.VOUCHER_ONLY,
                TICKET_DISCOUNT.ACTIVE,
                TICKET_DISCOUNT.CREATED_AT,
                TICKET_DISCOUNT.UPDATED_AT
            ).from(TICKET_DISCOUNT)
            .where(conditions)
            .paginationAndSorting(query.pageable, TICKET_DISCOUNT)
            .fetch()
            .map {
                AdminSearchTicketDiscountsResponse(
                    id = it[TICKET_DISCOUNT.ID],
                    title = it[TICKET_DISCOUNT.TITLE],
                    code = it[TICKET_DISCOUNT.CODE],
                    type = it[TICKET_DISCOUNT.TYPE],
                    usageType = it[TICKET_DISCOUNT.USAGE_TYPE],
                    voucherOnly = it[TICKET_DISCOUNT.VOUCHER_ONLY],
                    active = it[TICKET_DISCOUNT.ACTIVE],
                    createdAt = it[TICKET_DISCOUNT.CREATED_AT],
                    updatedAt = it[TICKET_DISCOUNT.UPDATED_AT]
                )
            }.let {
                PageImpl(it, query.pageable, count.toLong())
            }
    }
}
