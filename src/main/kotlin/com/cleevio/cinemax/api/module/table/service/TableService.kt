package com.cleevio.cinemax.api.module.table.service

import com.cleevio.cinemax.api.common.constant.TABLE
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.table.constant.TableLockValues.CREATE_OR_UPDATE_TABLE
import com.cleevio.cinemax.api.module.table.entity.Table
import com.cleevio.cinemax.api.module.table.service.command.CreateOrUpdateTableCommand
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class TableService(
    private val tableFinderRepository: TableFinderRepository,
    private val tableRepository: TableRepository,
) {
    private val log = logger()

    @Transactional
    @Lock(TABLE, CREATE_OR_UPDATE_TABLE)
    fun createOrUpdateTable(
        @Valid
        @LockFieldParameter("originalId")
        command: CreateOrUpdateTableCommand,
    ) {
        log.debug(
            "Attempting to create or update table with original id ${command.originalId}, " +
                "provided command is: $command."
        )

        val table = tableFinderRepository.findByOriginalId(command.originalId)
        table?.let {
            val updated: Table = tableRepository.save(mapToExistingTable(it, command))
            log.debug("Updated table ${updated.id} (${updated.originalId}).")
        } ?: run {
            val created: Table = tableRepository.save(mapToNewTable(command))
            log.debug("Created table ${created.id} (${created.originalId}).")
        }
    }

    private fun mapToExistingTable(
        table: Table,
        command: CreateOrUpdateTableCommand,
    ) = table.apply {
        title = command.title
        label = command.label
        order = command.order
        type = command.type
        productMode = command.productMode
        paymentType = command.paymentType
        discountCardCode = command.discountCardCode
        ipAddress = command.ipAddress
    }

    private fun mapToNewTable(command: CreateOrUpdateTableCommand) = Table(
        id = command.id ?: UUID.randomUUID(),
        originalId = command.originalId,
        title = command.title,
        label = command.label,
        order = command.order,
        type = command.type,
        productMode = command.productMode,
        paymentType = command.paymentType,
        discountCardCode = command.discountCardCode,
        ipAddress = command.ipAddress
    )
}
