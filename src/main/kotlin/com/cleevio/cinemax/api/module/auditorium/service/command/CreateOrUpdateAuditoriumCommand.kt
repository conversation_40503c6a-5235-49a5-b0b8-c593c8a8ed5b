package com.cleevio.cinemax.api.module.auditorium.service.command

import com.cleevio.cinemax.api.common.service.CreateOrUpdateCommand
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Positive
import jakarta.validation.constraints.PositiveOrZero
import jakarta.validation.constraints.Size
import java.math.BigDecimal
import java.util.UUID

data class CreateOrUpdateAuditoriumCommand(
    override val id: UUID? = null,
    val originalId: Int? = null,
    val originalCode: Int,
    val branchId: UUID? = null,

    @field:NotBlank
    @field:Size(max = 40)
    val title: String,

    @field:Positive
    val capacity: Int,

    @field:NotBlank
    @field:Size(max = 30)
    val city: String,

    @field:PositiveOrZero
    val saleTimeLimit: Int,

    @field:PositiveOrZero
    val surchargeVip: BigDecimal,

    @field:PositiveOrZero
    val surchargePremium: BigDecimal,

    @field:PositiveOrZero
    val surchargeImax: BigDecimal,

    @field:PositiveOrZero
    val surchargeUltraX: BigDecimal,

    @field:PositiveOrZero
    val surchargeDBox: BigDecimal,

    @field:PositiveOrZero
    val serviceFeeVip: BigDecimal,

    @field:PositiveOrZero
    val serviceFeePremium: BigDecimal,

    @field:PositiveOrZero
    val serviceFeeImax: BigDecimal,

    @field:PositiveOrZero
    val serviceFeeUltraX: BigDecimal,

    @field:PositiveOrZero
    val proCommission: Int,

    @field:PositiveOrZero
    val filmFondCommission: Int,

    @field:PositiveOrZero
    val distributorCommission: Int,
    val publishOnline: Boolean,
) : CreateOrUpdateCommand()
