package com.cleevio.cinemax.api.module.productcomponentcategory.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.common.dto.IdResult
import com.cleevio.cinemax.api.common.dto.SimplePage
import com.cleevio.cinemax.api.common.dto.toSimplePage
import com.cleevio.cinemax.api.module.productcomponentcategory.controller.dto.AdminSearchProductComponentCategoriesResponse
import com.cleevio.cinemax.api.module.productcomponentcategory.controller.dto.CreateProductComponentCategoryRequest
import com.cleevio.cinemax.api.module.productcomponentcategory.controller.dto.UpdateProductComponentCategoryRequest
import com.cleevio.cinemax.api.module.productcomponentcategory.service.AdminSearchProductComponentCategoriesQueryService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.command.DeleteProductComponentCategoryCommand
import com.cleevio.cinemax.api.module.productcomponentcategory.service.query.AdminSearchProductComponentCategoriesQuery
import com.cleevio.cinemax.psql.tables.ProductComponentCategoryColumnNames
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.data.domain.Pageable
import org.springframework.data.web.PageableDefault
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Manager Product Component Categories")
@RestController
@RequestMapping("/manager-app/product-component-categories")
class AdminProductComponentCategoryController(
    private val productComponentCategoryService: ProductComponentCategoryService,
    private val adminSearchProductComponentCategoriesQueryService: AdminSearchProductComponentCategoriesQueryService,
) {

    @PreAuthorize(Role.MANAGER)
    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    fun createProductComponentCategory(
        @RequestBody request: CreateProductComponentCategoryRequest,
    ): IdResult = IdResult(
        productComponentCategoryService.adminCreateOrUpdateProductComponentCategory(request.toCommand())
    )

    @PreAuthorize(Role.MANAGER)
    @PutMapping("/{productComponentCategoryId}", produces = [ApiVersion.VERSION_1_JSON])
    fun updateProductComponentCategory(
        @PathVariable productComponentCategoryId: UUID,
        @RequestBody request: UpdateProductComponentCategoryRequest,
    ): IdResult = IdResult(
        productComponentCategoryService.adminCreateOrUpdateProductComponentCategory(
            request.toCommand(productComponentCategoryId)
        )
    )

    @PreAuthorize(Role.MANAGER)
    @PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
    fun searchProductComponentCategories(
        @ParameterObject
        @PageableDefault(sort = [ProductComponentCategoryColumnNames.TITLE])
        pageable: Pageable,
    ): SimplePage<AdminSearchProductComponentCategoriesResponse> =
        adminSearchProductComponentCategoriesQueryService(AdminSearchProductComponentCategoriesQuery(pageable = pageable))
            .toSimplePage()

    @PreAuthorize(Role.MANAGER)
    @DeleteMapping("/{productComponentCategoryId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun deleteProductComponentCategory(@PathVariable productComponentCategoryId: UUID): Unit =
        productComponentCategoryService.deleteProductComponentCategory(
            DeleteProductComponentCategoryCommand(productComponentCategoryId)
        )
}
