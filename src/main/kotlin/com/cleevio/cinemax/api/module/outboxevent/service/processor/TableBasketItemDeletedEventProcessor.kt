package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemJooqFinderService
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.outboxevent.service.TableBasketMssqlRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class TableBasketItemDeletedEventProcessor(
    private val basketItemJooqFinderService: BasketItemJooqFinderService,
    private val tableBasketMssqlRepository: TableBasketMssqlRepository,
) {
    private val log = logger()

    @Transactional(transactionManager = "mssqlBuffetTransactionManager")
    fun process(event: OutboxEvent): Int {
        log.info("Starting to process OutboxEvent=$event.")

        val basketItem = basketItemJooqFinderService.getById(event.entityId)
        val queryResult = basketItem.originalId?.let {
            tableBasketMssqlRepository.deleteMssqlBasketItem(it)
        } ?: run {
            log.warn(
                "Tried to sync deletion of basketItem=${basketItem.id} to MSSQL but basketItem.originalId is null." +
                    "Skipping execution."
            )
            0
        }
        return queryResult
    }
}
