package com.cleevio.cinemax.api.module.pricecategory.service.command

import com.cleevio.cinemax.api.common.service.CreateOrUpdateCommand
import com.cleevio.cinemax.api.module.pricecategoryitem.service.command.AdminCreateOrUpdatePriceCategoryItemCommand
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.Size
import java.util.UUID

data class AdminCreateOrUpdatePriceCategoryCommand(
    override val id: UUID? = null,

    @field:NotBlank
    @field:Size(max = 15)
    val title: String,
    val active: Boolean,

    @field:NotEmpty
    val items: List<@Valid AdminCreateOrUpdatePriceCategoryItemCommand>,
) : CreateOrUpdateCommand()
