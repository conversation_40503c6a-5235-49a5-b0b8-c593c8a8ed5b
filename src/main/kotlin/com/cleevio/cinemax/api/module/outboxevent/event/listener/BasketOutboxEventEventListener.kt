package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.basket.event.TableBasketDeletedEvent
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import com.cleevio.cinemax.api.module.table.event.TableBasketInitiatedEvent
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    "features.outbox-event.basket.listener.enabled",
    havingValue = "true",
    matchIfMissing = true
)
class BasketOutboxEventEventListener(
    private val outboxEventService: OutboxEventService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToTableBasketInitiatedEvent(event: TableBasketInitiatedEvent) {
        if (event.initiatedInNewPos) {
            outboxEventService.createOutboxEvent(
                CreateOutboxEventCommand(
                    entityId = event.basketId,
                    type = OutboxEventType.TABLE_BASKET_CREATED
                )
            )
        }
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToTableBasketDeletedEvent(event: TableBasketDeletedEvent) {
        outboxEventService.createOutboxEvent(
            CreateOutboxEventCommand(
                entityId = event.basketId,
                type = OutboxEventType.TABLE_BASKET_DELETED
            )
        )
    }
}
