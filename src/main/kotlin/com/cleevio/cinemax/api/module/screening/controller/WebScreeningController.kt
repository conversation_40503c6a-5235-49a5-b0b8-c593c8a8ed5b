package com.cleevio.cinemax.api.module.screening.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.module.screening.controller.dto.WebCreateGroupReservationRequest
import com.cleevio.cinemax.api.module.screening.controller.dto.WebCreateGroupReservationResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.WebGetAuditoriumResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.WebGetMovieResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.WebGetReservationsResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.WebGetScreeningsResponse
import com.cleevio.cinemax.api.module.screening.service.WebCreateGroupReservationService
import com.cleevio.cinemax.api.module.screening.service.WebDeleteGroupReservationService
import com.cleevio.cinemax.api.module.screening.service.WebGetAuditoriumQueryService
import com.cleevio.cinemax.api.module.screening.service.WebGetMovieQueryService
import com.cleevio.cinemax.api.module.screening.service.WebGetReservationsQueryService
import com.cleevio.cinemax.api.module.screening.service.WebGetScreeningsQueryService
import com.cleevio.cinemax.api.module.screening.service.command.WebDeleteGroupReservationCommand
import com.cleevio.cinemax.api.module.screening.service.query.WebGetAuditoriumQuery
import com.cleevio.cinemax.api.module.screening.service.query.WebGetMovieQuery
import com.cleevio.cinemax.api.module.screening.service.query.WebGetReservationsQuery
import com.cleevio.cinemax.api.module.screening.service.query.WebGetScreeningsQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDate
import java.util.UUID

@Tag(name = "Web Screenings")
@RestController
@RequestMapping("/web-app/screenings")
class WebScreeningController(
    private val webGetScreeningsQueryService: WebGetScreeningsQueryService,
    private val webGetMovieQueryService: WebGetMovieQueryService,
    private val webGetReservationsQueryService: WebGetReservationsQueryService,
    private val webGetAuditoriumQueryService: WebGetAuditoriumQueryService,
    private val webCreateGroupReservationService: WebCreateGroupReservationService,
    private val webDeleteGroupReservationService: WebDeleteGroupReservationService,
) {

    @GetMapping(produces = [ApiVersion.VERSION_1_JSON])
    fun getScreenings(
        @RequestParam("date") date: LocalDate?,
    ): List<WebGetScreeningsResponse> {
        return webGetScreeningsQueryService(
            WebGetScreeningsQuery(
                dateFrom = date ?: LocalDate.now(),
                dateTo = date?.plusDays(DAYS_IN_FUTURE) ?: LocalDate.now().plusDays(DAYS_IN_FUTURE)
            )
        )
    }

    @GetMapping("/{originalId}/reservations", produces = [ApiVersion.VERSION_1_JSON])
    fun getReservations(
        @PathVariable originalId: Int,
    ): List<WebGetReservationsResponse> {
        return webGetReservationsQueryService(
            WebGetReservationsQuery(originalId)
        )
    }

    @GetMapping("/{originalId}/movie", produces = [ApiVersion.VERSION_1_JSON])
    fun getScreeningMovie(
        @PathVariable originalId: Int,
    ): WebGetMovieResponse? {
        return webGetMovieQueryService(
            WebGetMovieQuery(originalId)
        )
    }

    @GetMapping("/{originalId}/auditorium", produces = [ApiVersion.VERSION_1_JSON])
    fun getAuditorium(
        @PathVariable("originalId") originalId: Int,
    ): WebGetAuditoriumResponse {
        return webGetAuditoriumQueryService(
            WebGetAuditoriumQuery(originalId)
        )
    }

    @PostMapping("/{originalId}/group-reservation", produces = [ApiVersion.VERSION_1_JSON])
    fun createGroupReservation(
        @PathVariable originalId: Int,
        @RequestBody request: WebCreateGroupReservationRequest,
    ): WebCreateGroupReservationResponse {
        return webCreateGroupReservationService(request.toCommand(originalId))
    }

    @DeleteMapping("/{originalId}/group-reservation/{groupReservationId}", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun deleteGroupReservation(
        @PathVariable originalId: Int,
        @PathVariable groupReservationId: UUID,
    ) {
        webDeleteGroupReservationService(WebDeleteGroupReservationCommand(groupReservationId))
    }
}

private const val DAYS_IN_FUTURE = 6L
