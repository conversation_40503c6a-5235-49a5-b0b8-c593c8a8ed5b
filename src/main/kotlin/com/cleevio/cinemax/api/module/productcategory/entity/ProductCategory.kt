package com.cleevio.cinemax.api.module.productcategory.entity

import com.cleevio.cinemax.api.common.entity.DeletableEntity
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import jakarta.persistence.Column
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.util.UUID
import jakarta.persistence.Entity as JpaEntity

@JpaEntity
class ProductCategory(
    id: UUID = UUID.randomUUID(),

    @Column(name = "original_id")
    var originalId: Int? = null,

    @Column(name = "code", nullable = false)
    var code: String,

    @Column(name = "title", nullable = false)
    var title: String,

    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    var type: ProductCategoryType,

    @Column(name = "\"order\"")
    var order: Int? = null,

    @Column(name = "tax_rate", nullable = false)
    var taxRate: Int,

    @Column(name = "hex_color_code", nullable = false)
    var hexColorCode: String,

    @Column(name = "image_file_id")
    var imageFileId: UUID? = null,
) : DeletableEntity(id)
