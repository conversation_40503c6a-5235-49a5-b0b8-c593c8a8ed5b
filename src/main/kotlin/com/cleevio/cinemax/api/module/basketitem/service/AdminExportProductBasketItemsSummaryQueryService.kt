package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.module.basketitem.model.ProductBasketItemExportSummaryRecordModel
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminExportProductBasketItemsQuery
import com.cleevio.cinemax.psql.Tables.BASKET_ITEM
import com.cleevio.cinemax.psql.Tables.DISCOUNT_CARD
import com.cleevio.cinemax.psql.Tables.DISCOUNT_CARD_USAGE
import jakarta.validation.Valid
import org.jooq.DSLContext
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class AdminExportProductBasketItemsSummaryQueryService(
    private val psqlDslContext: DSLContext,
) {

    operator fun invoke(
        @Valid query: AdminExportProductBasketItemsQuery,
    ): ProductBasketItemExportSummaryRecordModel {
        val productBasketItemConditions = createProductBasketItemFilterConditions(query.filter)

        return psqlDslContext.select(
            NOT_CANCELLED_ITEMS_COUNT,
            BASKETS_COUNT,
            PURCHASE_PRICE_SUM,
            SALES_CASH_SUM,
            SALES_CASHLESS_SUM,
            NOT_CANCELLED_ITEMS_SUM,
            PRODUCTS_TAX_AMOUNT,
            CANCELLED_ITEMS_COUNT,
            CANCELLED_ITEMS_EXPENSE
        )
            .from(BASKET_ITEM)
            .leftJoin(DISCOUNT_CARD_USAGE).on(
                BASKET_ITEM.ID.`in`(
                    DISCOUNT_CARD_USAGE.PRODUCT_BASKET_ITEM_ID,
                    DISCOUNT_CARD_USAGE.PRODUCT_DISCOUNT_BASKET_ITEM_ID
                )
            )
            .leftJoin(DISCOUNT_CARD).on(DISCOUNT_CARD.ID.eq(DISCOUNT_CARD_USAGE.DISCOUNT_CARD_ID))
            .where(productBasketItemConditions)
            .fetchOne()
            ?.map {
                ProductBasketItemExportSummaryRecordModel(
                    productsCount = it[NOT_CANCELLED_ITEMS_COUNT],
                    basketsCount = it[BASKETS_COUNT],
                    purchasePriceSum = it[PURCHASE_PRICE_SUM],
                    salesCash = it[SALES_CASH_SUM],
                    salesCashless = it[SALES_CASHLESS_SUM],
                    grossSales = it[NOT_CANCELLED_ITEMS_SUM],
                    netSales = it[NOT_CANCELLED_ITEMS_SUM].minus(it[PRODUCTS_TAX_AMOUNT]),
                    grossRevenue = it[NOT_CANCELLED_ITEMS_SUM].minus(it[PURCHASE_PRICE_SUM]),
                    netRevenue = it[NOT_CANCELLED_ITEMS_SUM]
                        .minus(it[PURCHASE_PRICE_SUM])
                        .minus(it[PRODUCTS_TAX_AMOUNT]),
                    taxAmount = it[PRODUCTS_TAX_AMOUNT],
                    cancelledProductsCount = it[CANCELLED_ITEMS_COUNT],
                    cancelledProductsExpense = it[CANCELLED_ITEMS_EXPENSE]
                )
            } ?: error("Result for product basket item summary could not be fetched.")
    }
}
