package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.basket.service.BasketFinderService
import com.cleevio.cinemax.api.module.outboxevent.constant.BUFFET_DB
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.outboxevent.service.TableBasketMssqlRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class TableBasketCreatedEventProcessor(
    private val basketFinderService: BasketFinderService,
    private val tableBasketMssqlRepository: TableBasketMssqlRepository,
) {
    private val log = logger()

    @Transactional(transactionManager = "mssqlBuffetTransactionManager")
    fun process(event: OutboxEvent): Int {
        log.info("Starting to process OutboxEvent=$event.")

        val basket = basketFinderService.getNonDeletedById(event.entityId)
        val mssqlTable = tableBasketMssqlRepository.validateAndGetMssqlTableForBasket(
            basketId = basket.id,
            tableId = basket.tableId
        )

        log.info("Executing UPDATE query to $BUFFET_DB database...")
        val queryResult = tableBasketMssqlRepository.updateMssqlTableState(
            originalTableId = mssqlTable.stolyid,
            openBasketExists = true
        )
        log.info("UPDATE query to $BUFFET_DB database executed with result=$queryResult.")

        return queryResult
    }
}
