package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.module.product.entity.Product
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface ProductRepository : JpaRepository<Product, UUID> {

    fun existsByCodeAndDeletedAtIsNull(originalCode: String): Boolean

    fun findAllByIdIn(productIds: Set<UUID>): List<Product>

    fun findAllByOriginalIdInAndDeletedAtIsNull(originalIds: Set<Int>): List<Product>

    fun existsByIdAndDeletedAtIsNull(productId: UUID): Boolean

    fun existsByProductCategoryIdAndDeletedAtIsNull(productCategoryId: UUID): Boolean

    fun findByIdAndDeletedAtIsNull(productId: UUID): Product?

    fun findByCodeAndDeletedAtIsNull(originalCode: String): Product?

    fun findAllByCodeInAndDeletedAtIsNull(code: Set<String>): List<Product>

    fun findAllByIdInAndDeletedAtIsNull(ids: Set<UUID>): List<Product>

    fun findAllByDeletedAtIsNull(): List<Product>

    @Query(
        """
            SELECT count(p.id) > 0
            FROM product p
            WHERE p.id in (:productIds) AND p.deleted_at is null AND p.is_packaging_deposit = true
        """,
        nativeQuery = true
    )
    fun existsAllByIdInAndDeletedAtIsNullAndPackagingDepositIsTrue(productIds: Set<UUID>): Boolean

    @Query(
        """
        SELECT count(p.id) > 0
        FROM product p
        WHERE p.deleted_at is null AND p.is_packaging_deposit IS TRUE AND (p.id != :id OR CAST(:id AS UUID) IS NULL)
        """,
        nativeQuery = true
    )
    fun existsNonDeletedPackagingDepositAndIdDiffersOrIsNull(id: UUID?): Boolean
}
