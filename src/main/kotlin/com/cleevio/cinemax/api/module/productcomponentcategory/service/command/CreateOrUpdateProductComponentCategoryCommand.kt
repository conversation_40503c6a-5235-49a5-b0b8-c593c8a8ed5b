package com.cleevio.cinemax.api.module.productcomponentcategory.service.command

import com.cleevio.cinemax.api.common.annotation.NullOrNotBlank
import com.cleevio.cinemax.api.common.service.CreateOrUpdateCommand
import com.cleevio.cinemax.api.common.validation.ValidTaxRate
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.util.UUID

data class CreateOrUpdateProductComponentCategoryCommand(
    override val id: UUID? = null,
    val originalId: Int? = null,

    @field:NullOrNotBlank
    @field:Size(max = 2)
    val code: String? = null,

    @field:NotBlank
    @field:Size(max = 30)
    val title: String,

    @field:ValidTaxRate
    val taxRate: Int,
) : CreateOrUpdateCommand()
