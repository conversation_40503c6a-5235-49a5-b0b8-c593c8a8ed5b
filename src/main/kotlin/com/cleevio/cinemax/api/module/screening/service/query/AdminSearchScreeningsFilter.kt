package com.cleevio.cinemax.api.module.screening.service.query

import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import org.springframework.data.domain.Pageable
import java.time.LocalDate
import java.util.UUID

data class AdminSearchScreeningsFilter(
    val dateFrom: LocalDate? = null,
    val dateTo: LocalDate? = null,
    val states: Set<ScreeningState>? = null,
    val auditoriumIds: Set<UUID>? = null,
    val movieIds: Set<UUID>? = null,
    val movieDistributorIds: Set<UUID>? = null,
) {
    fun toQuery(pageable: Pageable) = AdminSearchScreeningsQuery(pageable = pageable, filter = this)
}
