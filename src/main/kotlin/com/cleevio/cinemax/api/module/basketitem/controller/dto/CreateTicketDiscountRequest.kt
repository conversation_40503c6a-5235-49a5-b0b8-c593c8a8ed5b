package com.cleevio.cinemax.api.module.basketitem.controller.dto

import com.cleevio.cinemax.api.module.basket.service.command.CreateTicketDiscountInput
import java.util.UUID

data class CreateTicketDiscountRequest(
    val ticketDiscountId: UUID,
    val discountCardId: UUID? = null,
) {
    fun toInput() = CreateTicketDiscountInput(
        ticketDiscountId = ticketDiscountId,
        discountCardId = discountCardId
    )
}
