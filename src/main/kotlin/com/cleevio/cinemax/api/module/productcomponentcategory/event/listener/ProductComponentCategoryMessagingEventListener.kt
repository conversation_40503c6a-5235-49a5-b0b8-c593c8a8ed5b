package com.cleevio.cinemax.api.module.productcomponentcategory.event.listener

import com.cleevio.cinemax.api.common.service.PublisherService
import com.cleevio.cinemax.api.module.productcomponentcategory.event.AdminProductComponentCategoryCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.productcomponentcategory.event.AdminProductComponentCategoryDeletedEvent
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    "cinemax.configuration.deployment-type",
    havingValue = "HEADQUARTERS"
)
class ProductComponentCategoryMessagingEventListener(
    private val publisherService: PublisherService,
) {

    @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
    fun listenToAdminProductComponentCategoryCreatedOrUpdatedEvent(
        event: AdminProductComponentCategoryCreatedOrUpdatedEvent,
    ) {
        publisherService.publish(messagePayload = event.toMessagePayload())
    }

    @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
    fun listenToAdminProductComponentCategoryDeletedEvent(
        event: AdminProductComponentCategoryDeletedEvent,
    ) {
        publisherService.publish(messagePayload = event.toMessagePayload())
    }
}
