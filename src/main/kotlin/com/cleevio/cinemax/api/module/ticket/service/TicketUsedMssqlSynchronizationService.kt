package com.cleevio.cinemax.api.module.ticket.service

import com.cleevio.cinemax.api.common.constant.TICKET
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.ticket.constant.TicketLockValues.SYNCHRONIZE_FROM_MSSQL
import com.cleevio.cinemax.api.module.ticket.service.command.UpdateTicketUsedCommand
import com.cleevio.cinemax.api.module.ticket.service.command.UpdateTicketsUsedCommand
import com.cleevio.cinemax.api.module.ticket.util.isUsed
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Clock
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime

@Service
class TicketUsedSynchronizationService(
    private val ticketService: TicketService,
    private val ticketJpaFinderService: TicketJpaFinderService,
    private val ticketMssqlFinderRepository: TicketMssqlFinderRepository,
    private val clock: Clock,
) {
    private val log = logger()

    @Transactional
    @TryLock(TICKET, SYNCHRONIZE_FROM_MSSQL)
    fun synchronizeTicketUsedForToday() {
        val dateToday = LocalDate.now(clock)
        log.info("Synchronizing ticket usage status for date: $dateToday.")

        val mssqlTickets = ticketMssqlFinderRepository.findAllMssqlByUpdatedAtIn(
            updatedAtFrom = LocalDateTime.of(dateToday, LocalTime.MIN),
            updatedAtTo = LocalDateTime.of(dateToday, LocalTime.MAX)
        )

        mssqlTickets.forEachIndexed { index, mssqlTicket ->
            try {
                if (ticketJpaFinderService.existsNonDeletedByOriginalId(mssqlTicket.rlistkyid)) {
                    ticketService.updateTicketUsed(
                        UpdateTicketUsedCommand(
                            originalId = mssqlTicket.rlistkyid,
                            isUsed = mssqlTicket.isUsed()
                        )
                    )
                }
                log.info("Processed ${index + 1} of ${mssqlTickets.size} ticket usage status.")
            } catch (ex: Exception) {
                log.error("Failed to update ticket usage status for originalId=${mssqlTicket.rlistkyid}", ex)
            }
        }

        log.info("Completed synchronizing ticket usage status for date: $dateToday.")
    }

    @Transactional
    @TryLock(TICKET, SYNCHRONIZE_FROM_MSSQL)
    fun synchronizeTicketUsedForTimeRange(updatedAtFrom: LocalDateTime, updatedAtTo: LocalDateTime) {
        log.info("Synchronizing ticket usage status from $updatedAtFrom to $updatedAtTo.")

        val mssqlTickets = ticketMssqlFinderRepository.findAllMssqlByUpdatedAtIn(
            updatedAtFrom = updatedAtFrom,
            updatedAtTo = updatedAtTo,
            legacyTicketsOnly = false
        ).filter { it.vstup == 1.toShort() }.chunked(TICKETS_BATCH_SIZE)

        mssqlTickets.forEach { mssqlTicketsBatch ->
            log.info("Executing ticket usage update of batch of $TICKETS_BATCH_SIZE...")
            ticketService.updateTicketsUsed(
                UpdateTicketsUsedCommand(
                    originalIds = mssqlTicketsBatch.map { it.rlistkyid }.toSet()
                )
            )
        }

        log.info("Completed synchronizing ticket usage for range from $updatedAtFrom to $updatedAtTo.")
    }
}

private const val TICKETS_BATCH_SIZE = 10_000
