package com.cleevio.cinemax.api.module.supplier.service

import com.cleevio.cinemax.api.common.constant.SUPPLIER
import com.cleevio.cinemax.api.common.service.CodeGenerator
import com.cleevio.cinemax.api.common.util.ifTrue
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.supplier.constant.SupplierLockValues.CREATE_OR_UPDATE_SUPPLIER
import com.cleevio.cinemax.api.module.supplier.entity.Supplier
import com.cleevio.cinemax.api.module.supplier.event.SupplierCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.supplier.event.toMessagingDeleteEvent
import com.cleevio.cinemax.api.module.supplier.event.toMessagingEvent
import com.cleevio.cinemax.api.module.supplier.exception.SupplierCodeAlreadyExistsException
import com.cleevio.cinemax.api.module.supplier.service.command.CreateOrUpdateSupplierCommand
import com.cleevio.cinemax.api.module.supplier.service.command.DeleteSupplierCommand
import com.cleevio.cinemax.api.module.supplier.service.command.MessagingCreateOrUpdateSupplierCommand
import com.cleevio.cinemax.api.module.supplier.service.command.MessagingDeleteSupplierCommand
import com.cleevio.cinemax.api.module.supplier.service.command.UpdateSupplierOriginalIdCommand
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import jakarta.validation.Valid
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.util.UUID
import kotlin.jvm.optionals.getOrDefault
import kotlin.jvm.optionals.getOrNull

@Service
@Validated
class SupplierService(
    private val supplierRepository: SupplierRepository,
    private val supplierJpaFinderService: SupplierJpaFinderService,
    private val codeGenerator: CodeGenerator,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    private val log = logger()

    @Transactional
    @Lock(SUPPLIER, CREATE_OR_UPDATE_SUPPLIER)
    fun syncCreateOrUpdateSupplier(
        @Valid
        @LockFieldParameter("originalId")
        command: CreateOrUpdateSupplierCommand,
    ) {
        requireNotNull(command.originalId) { "Attribute 'originalId' must not be null." }
        requireNotNull(command.code) { "Attribute 'code' must not be null." }

        val supplier = supplierRepository.findByOriginalId(command.originalId)
        if (supplier != null && supplier.isDeleted()) {
            log.warn("Supplier with originalId ${supplier.originalId} has been already deleted. Skipping.")
            return
        }
        supplier?.let {
            if (command.code != it.code) {
                log.error("Attribute code cannot be updated when syncing entity from MSSQL.")
                return
            }
        }
        supplierJpaFinderService.existsNonDeletedByCodeAndIdDiffersOrIsNull(
            code = command.code,
            id = supplier?.id
        ).ifTrue { throw SupplierCodeAlreadyExistsException() }

        internalCreateOrUpdate(
            supplier = supplier,
            command = command
        )
    }

    @Transactional
    @Lock(SUPPLIER, CREATE_OR_UPDATE_SUPPLIER)
    fun adminCreateOrUpdateSupplier(
        @Valid
        @LockFieldParameter("id")
        command: CreateOrUpdateSupplierCommand,
    ): UUID = internalCreateOrUpdate(
        supplier = command.id?.let { supplierJpaFinderService.getNonDeletedById(it) },
        command = command
    ).also {
        val updatedSupplier = supplierJpaFinderService.getNonDeletedById(it.id)

        applicationEventPublisher.publishEvent(SupplierCreatedOrUpdatedEvent(it.id))
        applicationEventPublisher.publishEvent(updatedSupplier.toMessagingEvent())
    }.id

    private fun internalCreateOrUpdate(supplier: Supplier?, command: CreateOrUpdateSupplierCommand): Supplier {
        supplier?.let {
            return supplierRepository.save(mapToExistingSupplier(it, command))
        } ?: run {
            return supplierRepository.save(mapToNewSupplier(command))
        }
    }

    @Transactional
    @Lock(SUPPLIER, CREATE_OR_UPDATE_SUPPLIER)
    fun updateSupplierOriginalId(
        @Valid
        @LockFieldParameter("supplierId")
        command: UpdateSupplierOriginalIdCommand,
    ) {
        supplierJpaFinderService.getNonDeletedById(command.supplierId).also {
            supplierRepository.save(
                it.apply {
                    originalId = command.originalId
                }
            )
        }
    }

    @Transactional
    @Lock(SUPPLIER, CREATE_OR_UPDATE_SUPPLIER)
    fun deleteSupplier(
        @Valid
        @LockFieldParameter("supplierId")
        command: DeleteSupplierCommand,
    ) {
        supplierJpaFinderService.getNonDeletedById(command.supplierId).let {
            supplierRepository.save(it.apply { markDeleted() })

            applicationEventPublisher.publishEvent(it.toMessagingDeleteEvent())
        }
    }

    @Transactional
    @Lock(SUPPLIER, CREATE_OR_UPDATE_SUPPLIER)
    fun messagingCreateOrUpdateSupplier(
        @Valid
        @LockFieldParameter("code")
        command: MessagingCreateOrUpdateSupplierCommand,
    ) {
        val supplier = supplierJpaFinderService.findNonDeletedByCode(command.code)?.let {
            supplierRepository.save(mapToExistingMessagingSupplier(it, command))
        } ?: run {
            supplierRepository.save(mapToNewMessagingSupplier(command))
        }

        applicationEventPublisher.publishEvent(SupplierCreatedOrUpdatedEvent(supplier.id))
    }

    @Transactional
    @Lock(SUPPLIER, CREATE_OR_UPDATE_SUPPLIER)
    fun messagingDeleteSupplier(
        @Valid
        @LockFieldParameter("code")
        command: MessagingDeleteSupplierCommand,
    ) {
        supplierJpaFinderService.getNonDeletedByCode(command.code).let {
            supplierRepository.save(it.apply { markDeleted() })
        }
    }

    private fun mapToNewSupplier(command: CreateOrUpdateSupplierCommand) = Supplier(
        id = command.id ?: UUID.randomUUID(),
        originalId = command.originalId,
        code = command.code ?: codeGenerator.generateCode(
            Supplier::class.java,
            supplierRepository::existsByCodeAndDeletedAtIsNull
        ),
        title = command.title,
        addressStreet = command.addressStreet?.getOrNull(),
        addressCity = command.addressCity?.getOrNull(),
        addressPostCode = command.addressPostCode?.getOrNull(),
        contactName = command.contactName?.getOrNull(),
        contactPhone = command.contactPhone?.getOrNull(),
        contactEmails = command.contactEmails?.getOrDefault(emptySet()) ?: emptySet(),
        bankName = command.bankName?.getOrNull(),
        bankAccount = command.bankAccount?.getOrNull(),
        idNumber = command.idNumber?.getOrNull(),
        taxIdNumber = command.taxIdNumber?.getOrNull()
    )

    private fun mapToExistingSupplier(supplier: Supplier, command: CreateOrUpdateSupplierCommand) =
        supplier.apply {
            title = command.title
            command.addressStreet?.let { addressStreet = it.getOrNull() }
            command.addressCity?.let { addressCity = it.getOrNull() }
            command.addressPostCode?.let { addressPostCode = it.getOrNull() }
            command.contactName?.let { contactName = it.getOrNull() }
            command.contactPhone?.let { contactPhone = it.getOrNull() }
            command.contactEmails?.let { contactEmails = it.getOrDefault(emptySet()) }
            command.bankName?.let { bankName = it.getOrNull() }
            command.bankAccount?.let { bankAccount = it.getOrNull() }
            command.idNumber?.let { idNumber = it.getOrNull() }
            command.taxIdNumber?.let { taxIdNumber = it.getOrNull() }
        }

    private fun mapToNewMessagingSupplier(command: MessagingCreateOrUpdateSupplierCommand) = Supplier(
        originalId = null,
        code = command.code,
        title = command.title,
        addressStreet = command.addressStreet?.getOrNull(),
        addressCity = command.addressCity?.getOrNull(),
        addressPostCode = command.addressPostCode?.getOrNull(),
        contactName = command.contactName?.getOrNull(),
        contactPhone = command.contactPhone?.getOrNull(),
        contactEmails = command.contactEmails?.getOrDefault(emptySet()) ?: emptySet(),
        bankName = command.bankName?.getOrNull(),
        bankAccount = command.bankAccount?.getOrNull(),
        idNumber = command.idNumber?.getOrNull(),
        taxIdNumber = command.taxIdNumber?.getOrNull()
    )

    private fun mapToExistingMessagingSupplier(supplier: Supplier, command: MessagingCreateOrUpdateSupplierCommand) =
        supplier.apply {
            code = command.code
            title = command.title
            command.addressStreet?.let { addressStreet = it.getOrNull() }
            command.addressCity?.let { addressCity = it.getOrNull() }
            command.addressPostCode?.let { addressPostCode = it.getOrNull() }
            command.contactName?.let { contactName = it.getOrNull() }
            command.contactPhone?.let { contactPhone = it.getOrNull() }
            command.contactEmails?.let { contactEmails = it.getOrDefault(emptySet()) }
            command.bankName?.let { bankName = it.getOrNull() }
            command.bankAccount?.let { bankAccount = it.getOrNull() }
            command.idNumber?.let { idNumber = it.getOrNull() }
            command.taxIdNumber?.let { taxIdNumber = it.getOrNull() }
        }
}
