package com.cleevio.cinemax.api.module.discountcard.controller.mapper

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.TaxRateType
import com.cleevio.cinemax.api.module.discountcard.constant.VipDiscountCardResponse
import com.cleevio.cinemax.api.module.discountcard.constant.VipProductDiscountResponse
import com.cleevio.cinemax.api.module.discountcard.entity.DiscountCard
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.exception.ProductNotFoundException
import com.cleevio.cinemax.api.module.product.service.ProductJooqFinderService
import org.springframework.stereotype.Component

@Component
class VipDiscountCardResponseMapper(
    private val productJooqFinderService: ProductJooqFinderService,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
) {

    fun mapSingle(discountCard: DiscountCard): VipDiscountCardResponse {
        val productDiscount = discountCard.productDiscountId?.let {
            CARD_TITLE_TO_PRODUCT_DISCOUNT_TITLE[discountCard.title]?.let {
                productJooqFinderService.findNonDeletedByTitleAndSoldInBuffet(it)
            }
        }

        return VipDiscountCardResponse(
            id = discountCard.id,
            originalId = discountCard.originalId,
            title = discountCard.title,
            code = discountCard.code,
            taxRate = cinemaxConfigProperties.getTaxRate(TaxRateType.STANDARD),
            productDiscount = productDiscount?.let { mapToProductDiscountResponse(it) }
                ?: throw ProductNotFoundException()
        )
    }

    private fun mapToProductDiscountResponse(productDiscount: Product) = VipProductDiscountResponse(
        id = productDiscount.id,
        title = productDiscount.title,
        percentage = productDiscount.discountPercentage!!
    )
}
