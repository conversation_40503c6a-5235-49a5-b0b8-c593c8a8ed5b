package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.distributor.entity.Distributor
import com.cleevio.cinemax.api.module.distributor.service.DistributorJpaFinderService
import com.cleevio.cinemax.api.module.distributor.service.DistributorMssqlFinderRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.distributor.service.command.UpdateDistributorOriginalIdCommand
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.records.RdistrRecord
import org.jooq.DSLContext
import org.jooq.types.UByte
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.time.LocalDateTime

@Service
class DistributorCreatedOrUpdatedEventProcessor(
    private val mssqlCinemaxDslContext: DSLContext,
    private val tableDistributorMssqlFinderRepository: DistributorMssqlFinderRepository,
    private val distributorService: DistributorService,
    private val distributorJpaFinderService: DistributorJpaFinderService,
) {
    private val log = logger()

    @Transactional(transactionManager = "mssqlCinemaxTransactionManager")
    fun process(event: OutboxEvent): Int {
        log.info("Starting to process OutboxEvent=$event.")

        distributorJpaFinderService.findNonDeletedById(event.entityId)?.let {
            it.originalId?.let { originalId ->
                if (!tableDistributorMssqlFinderRepository.existsByOriginalId(originalId)) {
                    log.error("MSSQL rdistr record with originalId $originalId not found.")
                    return 0
                }
                return updateMssqlDistributorRecord(it)
            } ?: run {
                createMssqlDistributorRecord(it)?.let { originalId ->
                    distributorService.updateDistributorOriginalId(
                        UpdateDistributorOriginalIdCommand(
                            distributorId = it.id,
                            originalId = originalId
                        )
                    )
                    return 1
                } ?: run {
                    log.error("MSSQL rdistr record was not created or its originalId could not be fetched.")
                    return 0
                }
            }
        } ?: run {
            log.error("Distributor with id=${event.entityId} not found.")
            return 0
        }
    }

    private fun createMssqlDistributorRecord(it: Distributor): Int? = mssqlCinemaxDslContext.insertInto(Tables.RDISTR)
        .set(prepareMssqlDistributorRecord(it))
        .returningResult(Tables.RDISTR.RDISTRID)
        .fetchOneInto(Int::class.java)

    private fun updateMssqlDistributorRecord(distributor: Distributor): Int = mssqlCinemaxDslContext.update(Tables.RDISTR)
        .set(prepareMssqlDistributorRecord(distributor))
        .where(Tables.RDISTR.RDISTRID.eq(distributor.originalId))
        .execute()

    private fun prepareMssqlDistributorRecord(distributor: Distributor): RdistrRecord =
        mssqlCinemaxDslContext.newRecord(Tables.RDISTR).also {
            it.distr = distributor.code
            it.nazevd = distributor.title
            it.ulice = distributor.addressStreet ?: ""
            it.misto = distributor.addressCity ?: ""
            it.psc = distributor.addressPostCode ?: ""
            it.jmeno1 = distributor.contactName1 ?: ""
            it.jmeno2 = distributor.contactName2 ?: ""
            it.jmeno3 = distributor.contactName3 ?: ""
            it.telefon1 = distributor.contactPhone1 ?: ""
            it.telefon2 = distributor.contactPhone2 ?: ""
            it.telefon3 = distributor.contactPhone3 ?: ""
            it.mail = distributor.contactEmails.joinToString(separator = ";")
            it.banka = distributor.bankName ?: ""
            it.ucet = distributor.bankAccount ?: ""
            it.ico = distributor.idNumber ?: ""
            it.dic = distributor.taxIdNumber ?: ""
            it.dph = UByte.valueOf(distributor.vatRate ?: 0)
            it.pozn = distributor.note ?: ""
            it.zuziv = distributor.updatedBy
            it.zcas = LocalDateTime.now()

            // unused columns need to be set with default values
            it.kod = ""
            it.provize = BigDecimal.valueOf(50)
            it.fax = ""
            it.znak = ""
            it.fix = 0
            it.ldan = false
            it.vipcena = BigDecimal.ZERO
            it.limit = 0.toShort()
        }
}
