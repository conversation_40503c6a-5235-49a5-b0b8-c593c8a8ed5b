package com.cleevio.cinemax.api.module.screening.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.CONFLICT)
class ScreeningDateTimeConflictException : ApiException(
    Module.SCREENING,
    ScreeningErrorType.SCREENING_DATE_TIME_CONFLICT
)
