package com.cleevio.cinemax.api.module.basketitem.service.command

import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import jakarta.validation.constraints.Positive
import java.util.UUID

data class CreateBasketItemCommand(
    val basketId: UUID,
    val type: BasketItemType,

    @field:Positive
    val quantity: Int,
    val screeningId: UUID? = null,
    val reservationSeatId: UUID? = null,
    val productId: UUID? = null,
    val productIsolatedWithId: UUID? = null,
    val priceCategoryItemNumber: PriceCategoryItemNumber? = null,
    val primaryTicketDiscountId: UUID? = null,
    val secondaryTicketDiscountId: UUID? = null,
    val primaryTicketDiscountCardId: UUID? = null,
    val secondaryTicketDiscountCardId: UUID? = null,
    val productDiscountCardId: UUID? = null,
    val isGroupTicket: Boolean? = null,
    val originalId: Int? = null,
)
