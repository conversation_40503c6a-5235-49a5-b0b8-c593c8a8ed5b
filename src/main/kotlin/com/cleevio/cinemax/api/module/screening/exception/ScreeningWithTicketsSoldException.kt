package com.cleevio.cinemax.api.module.screening.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
class ScreeningWithTicketsSoldException : ApiException(
    Module.SCREENING,
    ScreeningErrorType.SCREENING_WITH_TICKETS_SOLD
)
