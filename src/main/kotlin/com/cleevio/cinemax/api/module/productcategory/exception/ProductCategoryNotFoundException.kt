package com.cleevio.cinemax.api.module.productcategory.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.NOT_FOUND)
class ProductCategoryNotFoundException : ApiException(
    Module.PRODUCT_CATEGORY,
    ProductCategoryErrorType.PRODUCT_CATEGORY_NOT_FOUND
)
