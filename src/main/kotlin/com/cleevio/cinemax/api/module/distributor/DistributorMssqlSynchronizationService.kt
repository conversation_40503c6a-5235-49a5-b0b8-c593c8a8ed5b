package com.cleevio.cinemax.api.module.distributor

import com.cleevio.cinemax.api.common.constant.DISTRIBUTOR
import com.cleevio.cinemax.api.common.service.AbstractMssqlSynchronizationService
import com.cleevio.cinemax.api.common.util.parseEmails
import com.cleevio.cinemax.api.common.util.setNullIfZero
import com.cleevio.cinemax.api.common.util.trimIfNotBlank
import com.cleevio.cinemax.api.module.distributor.constant.DistributorLockValues.SYNCHRONIZE_FROM_MSSQL
import com.cleevio.cinemax.api.module.distributor.service.DistributorMssqlFinderRepository
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.distributor.service.command.CreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlService
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables.RDISTR
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Rdistr
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.stereotype.Service
import java.util.Optional

@Service
class DistributorMssqlSynchronizationService(
    private val distributorService: DistributorService,
    private val distributorMssqlFinderRepository: DistributorMssqlFinderRepository,
    private val synchronizationFromMssqlFinderService: SynchronizationFromMssqlFinderService,
    synchronizationFromMssqlService: SynchronizationFromMssqlService,
) : AbstractMssqlSynchronizationService<Rdistr>(
    mssqlEntityName = RDISTR.name,
    synchronizationFromMssqlService = synchronizationFromMssqlService,
    synchronizationType = SynchronizationFromMssqlType.DISTRIBUTOR
) {

    @TryLock(DISTRIBUTOR, SYNCHRONIZE_FROM_MSSQL)
    fun synchronizeAll() = synchronizeAll {
        distributorMssqlFinderRepository.findAllByUpdatedAtGt(
            updatedAt = synchronizationFromMssqlFinderService.findByType(SynchronizationFromMssqlType.DISTRIBUTOR)
        )
    }

    override fun validateAndPersist(mssqlEntity: Rdistr) = distributorService.syncCreateOrUpdateDistributor(
        mapToCreateOrUpdateDistributorCommand(
            mssqlDistributor = mssqlEntity
        )
    )

    private fun mapToCreateOrUpdateDistributorCommand(mssqlDistributor: Rdistr) = CreateOrUpdateDistributorCommand(
        originalId = mssqlDistributor.rdistrid,
        code = mssqlDistributor.distr.trim(),
        disfilmCode = null,
        title = mssqlDistributor.nazevd.trim(),
        addressStreet = mssqlDistributor.ulice.trimIfNotBlank()?.let { Optional.of(it) },
        addressCity = mssqlDistributor.misto.trimIfNotBlank()?.let { Optional.of(it) },
        addressPostCode = mssqlDistributor.psc.trimIfNotBlank()?.let { Optional.of(it) },
        contactName1 = mssqlDistributor.jmeno1.trimIfNotBlank()?.let { Optional.of(it) },
        contactName2 = mssqlDistributor.jmeno2.trimIfNotBlank()?.let { Optional.of(it) },
        contactName3 = mssqlDistributor.jmeno3.trimIfNotBlank()?.let { Optional.of(it) },
        contactPhone1 = mssqlDistributor.telefon1.trimIfNotBlank()?.let { Optional.of(it) },
        contactPhone2 = mssqlDistributor.telefon2.trimIfNotBlank()?.let { Optional.of(it) },
        contactPhone3 = mssqlDistributor.telefon3.trimIfNotBlank()?.let { Optional.of(it) },
        contactEmails = mssqlDistributor.mail.parseEmails().let { Optional.of(it) },
        bankName = mssqlDistributor.banka.trimIfNotBlank()?.let { Optional.of(it) },
        bankAccount = mssqlDistributor.ucet.trimIfNotBlank()?.let { Optional.of(it) },
        idNumber = mssqlDistributor.ico.trimIfNotBlank()?.let { Optional.of(it) },
        taxIdNumber = mssqlDistributor.dic.trimIfNotBlank()?.let { Optional.of(it) },
        vatRate = mssqlDistributor.dph.toInt().setNullIfZero()?.let { Optional.of(it) },
        note = mssqlDistributor.pozn.trimIfNotBlank()?.let { Optional.of(it) }
    )
}
