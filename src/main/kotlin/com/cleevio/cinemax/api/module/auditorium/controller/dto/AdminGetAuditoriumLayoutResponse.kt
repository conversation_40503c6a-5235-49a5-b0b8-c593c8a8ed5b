package com.cleevio.cinemax.api.module.auditorium.controller.dto

import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.seat.constant.DoubleSeatType
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import java.util.UUID

data class AdminGetAuditoriumLayoutResponse(
    val id: UUID,
    val code: String,
    val capacity: Int,
    val layout: GetAuditoriumLayoutDetail,
)

data class GetAuditoriumLayoutDetail(
    val id: UUID,
    val code: String,
    val title: String,
    val seats: List<GetAuditoriumLayoutSeatDetail>,
)

data class GetAuditoriumLayoutSeatDetail(
    val id: UUID,
    val type: SeatType,
    val doubleSeatType: DoubleSeatType? = null,
    val row: String,
    val number: String,
    val positionLeft: Int,
    val positionTop: Int,
    val defaultReservationState: ReservationState?,
)
