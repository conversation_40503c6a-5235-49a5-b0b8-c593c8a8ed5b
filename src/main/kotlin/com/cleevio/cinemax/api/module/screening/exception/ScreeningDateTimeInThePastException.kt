package com.cleevio.cinemax.api.module.screening.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.BAD_REQUEST)
class ScreeningDateTimeInThePastException : ApiException(
    Module.SCREENING,
    ScreeningErrorType.SCREENING_DATE_TIME_IN_THE_PAST
)
