package com.cleevio.cinemax.api.module.productcomponent.exception

import com.cleevio.cinemax.api.common.exception.ErrorType

enum class ProductComponentErrorType(
    override val code: String,
    override val message: String? = null,
) : ErrorType {
    PRODUCT_COMPONENT_NOT_FOUND("100", "Product component not found."),
    PRODUCT_COMPONENT_ORIGINAL_CODE_ALREADY_EXISTS("101", "Product component original code already exists."),
}
