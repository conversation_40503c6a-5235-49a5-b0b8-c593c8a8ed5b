package com.cleevio.cinemax.api.module.table.service.command

import com.cleevio.cinemax.api.common.annotation.NullOrNotBlank
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.service.CreateOrUpdateCommand
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.table.constant.TableType
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.util.UUID

data class CreateOrUpdateTableCommand(
    override val id: UUID? = null,
    val originalId: Int,

    @field:NotBlank
    @field:Size(max = 40)
    val title: String,

    @field:NotBlank
    @field:Size(max = 40)
    val label: String,
    val order: Int? = null,
    val type: TableType,
    val productMode: ProductMode,
    val paymentType: PaymentType,

    @field:NullOrNotBlank
    @field:Size(max = 10)
    val discountCardCode: String? = null,

    @field:NullOrNotBlank
    @field:Size(max = 50)
    val ipAddress: String? = null,
) : CreateOrUpdateCommand()
