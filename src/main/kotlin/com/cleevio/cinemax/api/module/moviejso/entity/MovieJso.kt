package com.cleevio.cinemax.api.module.moviejso.entity

import com.cleevio.cinemax.api.common.entity.CreatableEntity
import jakarta.persistence.Column
import jakarta.persistence.Table
import java.util.UUID
import jakarta.persistence.Entity as JpaEntity

@Table(name = "movie_jso")
@JpaEntity
class MovieJso(
    id: UUID = UUID.randomUUID(),

    @Column(name = "movie_id", nullable = false)
    var movieId: UUID,

    @Column(name = "jso_id", nullable = false)
    var jsoId: UUID,
) : CreatableEntity(id)
