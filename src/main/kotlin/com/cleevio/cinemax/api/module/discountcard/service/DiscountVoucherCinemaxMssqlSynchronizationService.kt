package com.cleevio.cinemax.api.module.discountcard.service

import com.cleevio.cinemax.api.common.constant.DISCOUNT_CARD
import com.cleevio.cinemax.api.common.service.AbstractMssqlSynchronizationService
import com.cleevio.cinemax.api.common.util.forceNegate
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardLockValues.SYNCHRONIZE_VOUCHER_FROM_MSSQL
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountCardCodeAlreadyExistsException
import com.cleevio.cinemax.api.module.discountcard.service.command.CreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.module.product.service.ProductJooqFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlService
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountJooqFinderService
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables.VOUCHER
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Voucher
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class DiscountVoucherCinemaxMssqlSynchronizationService(
    private val discountCardService: DiscountCardService,
    private val discountCardJooqFinderService: DiscountCardJooqFinderService,
    private val discountVoucherMssqlCinemaxFinderRepository: DiscountVoucherMssqlCinemaxFinderRepository,
    private val discountVoucherMssqlBuffetFinderRepository: DiscountVoucherMssqlBuffetFinderRepository,
    private val ticketDiscountJooqFinderService: TicketDiscountJooqFinderService,
    private val productJooqFinderService: ProductJooqFinderService,
    private val synchronizationFromMssqlFinderService: SynchronizationFromMssqlFinderService,
    synchronizationFromMssqlService: SynchronizationFromMssqlService,
) : AbstractMssqlSynchronizationService<Voucher>(
    mssqlEntityName = VOUCHER.name,
    synchronizationFromMssqlService = synchronizationFromMssqlService,
    synchronizationType = SynchronizationFromMssqlType.DISCOUNT_VOUCHER
) {
    private val log = logger()
    private val voucherCodeToDiscountProductCodePair: MutableMap<String, Pair<String, String>> = mutableMapOf()

    @TryLock(DISCOUNT_CARD, SYNCHRONIZE_VOUCHER_FROM_MSSQL)
    fun synchronizeAll() {
        val cinemaxVouchers = discountVoucherMssqlCinemaxFinderRepository.findAllValidByUpdatedAtGt(
            updatedAt = synchronizationFromMssqlFinderService.findByType(SynchronizationFromMssqlType.DISCOUNT_VOUCHER)
        )
        val cinemaxVoucherCodeBatches = cinemaxVouchers.mapToSet { it.cislo }.chunked(BUFFET_VOUCHERS_CHUNK_SIZE)
        val buffetVouchers = cinemaxVoucherCodeBatches.flatMap {
            log.info("Processing batch of $BUFFET_VOUCHERS_CHUNK_SIZE buffet vouchers.")
            return@flatMap discountVoucherMssqlBuffetFinderRepository.findAllByCodeIn(it.toSet())
        }

        voucherCodeToDiscountProductCodePair.putAll(
            buffetVouchers.associate {
                it.cislo.trim() to Pair(it.cmenu.trim(), it.slevac.trim())
            }
        )

        synchronizeAll { cinemaxVouchers }

        voucherCodeToDiscountProductCodePair.clear()
    }

    override fun validateAndPersist(mssqlEntity: Voucher) {
        val code = mssqlEntity.cislo.trim()

        val originalId = discountCardJooqFinderService.findByCode(code)?.let {
            if (it.type == DiscountCardType.CARD) {
                log.error("Discount card (CARD) with code=$code already exists. Synchronization of voucher aborted.")
                throw DiscountCardCodeAlreadyExistsException()
            }
            return@let it.originalId
        }

        if (mssqlEntity.sleva.isBlank()) {
            log.warn("Discount voucher originalId=${mssqlEntity.voucherid} has blank 'sleva' attribute, skipping.")
            return
        }

        val ticketDiscount = ticketDiscountJooqFinderService.findNonDeletedByCode(mssqlEntity.sleva.trim())
        val productOriginalCodePair = voucherCodeToDiscountProductCodePair[code]
        val productDiscount = productOriginalCodePair?.let { productJooqFinderService.findNonDeletedByOriginalCode(it.second) }
        val product = productOriginalCodePair?.let { productJooqFinderService.findNonDeletedByOriginalCode(it.first) }

        discountCardService.createOrUpdateDiscountCard(
            mapToCreateOrUpdateDiscountCardCommand(
                mssqlVoucherOriginalId = originalId,
                mssqlVoucher = mssqlEntity,
                ticketDiscountId = ticketDiscount?.id,
                productDiscountId = productDiscount?.id,
                productId = product?.id
            )
        )
    }

    private fun mapToCreateOrUpdateDiscountCardCommand(
        mssqlVoucherOriginalId: Int?,
        mssqlVoucher: Voucher,
        ticketDiscountId: UUID? = null,
        productDiscountId: UUID? = null,
        productId: UUID? = null,
    ) = CreateOrUpdateDiscountCardCommand(
        originalId = (mssqlVoucherOriginalId ?: mssqlVoucher.voucherid).forceNegate(),
        ticketDiscountId = ticketDiscountId,
        productDiscountId = productDiscountId,
        productId = productId,
        type = DiscountCardType.VOUCHER,
        title = mssqlVoucher.typ.trim(),
        code = mssqlVoucher.cislo.trim(),
        validFrom = mssqlVoucher.platod.toLocalDate(),
        validUntil = mssqlVoucher.platdo.toLocalDate(),
        applicableToBasket = mssqlVoucher.maxpo,
        applicableToScreening = mssqlVoucher.maxpo,
        applicableToScreeningsPerDay = null
    )
}

private const val BUFFET_VOUCHERS_CHUNK_SIZE = 5000
