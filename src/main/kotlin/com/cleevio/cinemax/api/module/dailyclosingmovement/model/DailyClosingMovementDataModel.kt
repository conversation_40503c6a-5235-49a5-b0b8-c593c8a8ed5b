package com.cleevio.cinemax.api.module.dailyclosingmovement.model

import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import java.math.BigDecimal

data class DailyClosingMovementDataModel(
    val basketItemType: BasketItemType,
    val basketItemPrice: BigDecimal,
    val basketPaymentType: PaymentType,
    val ticketTotalPrice: BigDecimal = BigDecimal.ZERO,
    val ticketSeatServiceFee: BigDecimal = BigDecimal.ZERO,
    val ticketAuditoriumServiceFee: BigDecimal = BigDecimal.ZERO,
    val ticketServiceFeeGeneral: BigDecimal = BigDecimal.ZERO,
    val isCancelled: Boolean,
)

fun DailyClosingMovementDataList.toDailyClosingMovementDataModels() =
    this.map { map ->
        DailyClosingMovementDataModel(
            basketItemType = BasketItemType.valueOf(map["basket_item_type"] as String),
            basketItemPrice = map["basket_item_price"] as BigDecimal,
            basketPaymentType = PaymentType.valueOf(map["basket_payment_type"] as String),
            ticketTotalPrice = map["ticket_total_price"] as BigDecimal? ?: BigDecimal.ZERO,
            ticketSeatServiceFee = map["ticket_seat_service_fee"] as BigDecimal? ?: BigDecimal.ZERO,
            ticketAuditoriumServiceFee = map["ticket_auditorium_service_fee"] as BigDecimal? ?: BigDecimal.ZERO,
            ticketServiceFeeGeneral = map["ticket_service_fee_general"] as BigDecimal? ?: BigDecimal.ZERO,
            isCancelled = map["is_cancelled"] as Boolean
        )
    }

typealias DailyClosingMovementDataList = List<Map<String, Any>>
