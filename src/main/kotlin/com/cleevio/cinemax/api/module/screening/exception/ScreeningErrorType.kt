package com.cleevio.cinemax.api.module.screening.exception

import com.cleevio.cinemax.api.common.exception.ErrorType

enum class ScreeningErrorType(
    override val code: String,
    override val message: String? = null,
) : ErrorType {
    SCREENING_NOT_FOUND("100", "Screening not found."),
    AUDITORIUM_FOR_SCREENING_NOT_FOUND("101", "Auditorium for screening not found."),
    MOVIE_FOR_SCREENING_NOT_FOUND("102", "Movie for screening not found."),
    PRICE_CATEGORY_FOR_SCREENING_NOT_FOUND("103", "Price category for screening not found."),
    SCREENING_DATE_TIME_IN_THE_PAST("104", "Screening date time is in the past."),
    SCREENING_DATE_TIME_CONFLICT("105", "Screening date time conflicts with an existing screening."),
    SCREENING_WITH_TICKETS_SOLD("106", "Screening has already related sold tickets."),
    DESTINATION_DATE_IN_THE_PAST("107", "Destination date is in the past."),
    SCREENINGS_DATE_TIME_CONFLICT("108"),
    SCREENINGS_WITH_TICKETS_SOLD("109"),
    SCREENING_SALE_TIME_LIMIT_OVER("110", "Screening's sale time limit is over."),
    SCREENINGS_NOT_FOUND("111"),
    SCREENINGS_SALE_TIME_LIMIT_OVER("112"),
    INVALID_DATE_INTERVAL_EXPORT_FILTER("113", "Invalid date interval export filter."),
    PUBLISHING_SCREENING_ONLINE_FAILED("114"),
    UNPUBLISHING_SCREENING_ONLINE_FAILED("115"),
    INVALID_SCREENING_STATE("116", "Invalid screening state."),
    SCREENING_IN_PUBLISHED_STATE("117", "Screening is in PUBLISHED state."),
    SCREENING_NOT_PUBLISHED("118", "Screening is not published."),
    SCREENING_CANCELLED("119", "Screening was cancelled."),
    SCREENING_STOPPED("120", "Screening was stopped."),
    SCREENING_WEB_PRICE_NOT_FOUND("121", "Web price for seats in screening not found."),
}
