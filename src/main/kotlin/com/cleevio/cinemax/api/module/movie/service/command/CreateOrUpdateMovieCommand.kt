package com.cleevio.cinemax.api.module.movie.service.command

import com.cleevio.cinemax.api.common.annotation.NullOrNotBlank
import com.cleevio.cinemax.api.common.service.CreateOrUpdateCommand
import com.cleevio.cinemax.api.module.movie.constant.MovieRating
import com.cleevio.cinemax.api.module.screening.constant.MovieFormat
import com.cleevio.cinemax.api.module.screening.constant.MovieLanguage
import com.cleevio.cinemax.api.module.screening.constant.MovieTechnology
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size
import java.time.LocalDate
import java.util.Optional
import java.util.UUID

data class CreateOrUpdateMovieCommand(
    override val id: UUID? = null,
    val originalId: Int? = null,

    @field:NullOrNotBlank
    @field:Size(max = 50)
    val title: String? = null,

    @field:NotBlank
    @field:Size(max = 50)
    val rawTitle: String,

    val originalTitle: Optional<@NullOrNotBlank @Size(max = 45) String>? = null,

    @field:NullOrNotBlank
    @field:Size(max = 6)
    val code: String? = null,

    @field:NullOrNotBlank
    @field:Size(max = 10)
    val disfilmCode: String? = null,

    val premiereDate: Optional<LocalDate>? = null,
    val duration: Int? = null,
    val parsedRating: MovieRating? = null,
    val parsedFormat: MovieFormat? = null,
    val parsedTechnology: MovieTechnology? = null,
    val parsedLanguage: MovieLanguage? = null,
    val distributorId: UUID,
    val productionId: Optional<UUID>? = null,
    val primaryGenreId: Optional<UUID>? = null,
    val secondaryGenreId: Optional<UUID>? = null,
    val ratingId: Optional<UUID>? = null,
    val technologyId: UUID? = null,
    val languageId: UUID? = null,
    val tmsLanguageId: UUID? = null,

    @field:Size(max = 3)
    val jsoIds: Set<UUID>? = null,
) : CreateOrUpdateCommand()
