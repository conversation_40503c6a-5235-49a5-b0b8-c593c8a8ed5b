package com.cleevio.cinemax.api.module.table.controller.dto

import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.basket.service.command.CreateVipBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.CreateVipBasketItemInput
import java.util.UUID

data class CreateVipBasketRequest(
    val preferredPaymentType: PaymentType,
    val discountCardId: UUID?,
    val items: List<CreateVipBasketItemRequest>,
) {
    fun toCommand(tableId: UUID) = CreateVipBasketCommand(
        tableId = tableId,
        preferredPaymentType = this.preferredPaymentType,
        discountCardId = this.discountCardId,
        basketItems = this.items.map { it.toCommand() }
    )
}

data class CreateVipBasketItemRequest(
    val productId: UUID,
    val quantity: Int,
) {
    fun toCommand() = CreateVipBasketItemInput(
        productId = this.productId,
        quantity = this.quantity
    )
}
