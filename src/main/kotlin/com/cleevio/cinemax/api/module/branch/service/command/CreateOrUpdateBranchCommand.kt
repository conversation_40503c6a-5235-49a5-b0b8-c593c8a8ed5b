package com.cleevio.cinemax.api.module.branch.service.command

import com.cleevio.cinemax.api.common.service.CreateOrUpdateCommand
import jakarta.validation.constraints.NotBlank
import java.util.UUID

data class CreateOrUpdateBranchCommand(
    override val id: UUID? = null,
    val originalId: Int,
    val productSalesCode: Int,

    @field:NotBlank
    val code: String,

    @field:NotBlank
    val auditoriumOriginalCodePrefix: String,

    @field:NotBlank
    val name: String,
) : CreateOrUpdateCommand()
