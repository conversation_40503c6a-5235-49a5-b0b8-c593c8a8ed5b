package com.cleevio.cinemax.api.module.supplier.service

import com.cleevio.cinemax.api.module.supplier.entity.Supplier
import com.cleevio.cinemax.api.module.supplier.exception.SupplierNotFoundException
import org.springframework.stereotype.Service
import java.util.UUID
import kotlin.jvm.optionals.getOrNull

@Service
class SupplierJpaFinderService(
    private val repository: SupplierRepository,
) {

    fun findById(id: UUID): Supplier? = repository.findById(id).getOrNull()

    fun getNonDeletedById(id: UUID): Supplier = findNonDeletedById(id) ?: throw SupplierNotFoundException()

    fun existsNonDeletedByCodeAndIdDiffersOrIsNull(
        code: String,
        id: UUID?,
    ): Boolean = repository.existsNonDeletedByCodeAndIdDiffersOrIsNull(code = code, id = id)

    fun findNonDeletedById(id: UUID): Supplier? = repository.findByIdAndDeletedAtIsNull(id)

    fun findNonDeletedByOriginalId(originalCode: Int): Supplier? =
        repository.findByOriginalIdAndDeletedAtIsNull(originalCode)

    fun existsNonDeletedById(supplierId: UUID): Boolean = repository.existsByIdAndDeletedAtIsNull(supplierId)

    fun findNonDeletedByCode(code: String): Supplier? =
        repository.findByCodeAndDeletedAtIsNull(code)

    fun getNonDeletedByCode(code: String): Supplier = findNonDeletedByCode(code)
        ?: throw SupplierNotFoundException()
}
