package com.cleevio.cinemax.api.module.jso.service

import com.cleevio.cinemax.api.module.jso.entity.Jso
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class JsoJpaFinderService(
    private val jsoRepository: JsoRepository,
) {

    fun findAll(): List<Jso> = jsoRepository.findAll()

    fun findById(id: UUID): Jso? {
        return jsoRepository.findById(id).orElse(null)
    }

    fun findByCode(code: String): Jso? {
        return jsoRepository.findByCode(code)
    }

    fun findAllByIdIn(ids: Set<UUID>): List<Jso> {
        return jsoRepository.findAllByIdIn(ids)
    }

    fun findAllByCodeIn(codes: Set<String>): List<Jso> {
        return jsoRepository.findAllByCodeIn(codes)
    }
}
