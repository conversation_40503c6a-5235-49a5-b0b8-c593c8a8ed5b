package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.controller.dto.WebGetReservationsResponse
import com.cleevio.cinemax.api.module.screening.service.query.WebGetReservationsQuery
import com.cleevio.cinemax.psql.Tables.AUDITORIUM
import com.cleevio.cinemax.psql.Tables.RESERVATION
import com.cleevio.cinemax.psql.Tables.SCREENING
import com.cleevio.cinemax.psql.Tables.SEAT
import jakarta.validation.Valid
import org.jooq.DSLContext
import org.jooq.impl.DSL.field
import org.jooq.impl.DSL.select
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class WebGetReservationsQueryService(
    private val psqlDslContext: DSLContext,
) {

    operator fun invoke(
        @Valid query: WebGetReservationsQuery,
    ): List<WebGetReservationsResponse> {
        val screeningConditions = listOf(
            SCREENING.ORIGINAL_ID.eq(query.originalId),
            SCREENING.DELETED_AT.isNull,
            SCREENING.STOPPED.isFalse,
            SCREENING.CANCELLED.isFalse,
            SCREENING.PUBLISH_ONLINE.isTrue,
            SCREENING.STATE.eq(ScreeningState.PUBLISHED),
            SCREENING.AUDITORIUM_LAYOUT_ID.eq(SEAT.AUDITORIUM_LAYOUT_ID)
        )

        val reservation = field(
            select(RESERVATION.STATE)
                .from(RESERVATION)
                .where(RESERVATION.SCREENING_ID.eq(SCREENING.ID))
                .and(RESERVATION.SEAT_ID.eq(SEAT.ID))
                .and(RESERVATION.DELETED_AT.isNull)
                .orderBy(RESERVATION.UPDATED_AT.desc())
                .limit(1)
        )

        return psqlDslContext.select(
            SEAT.ID,
            SEAT.ORIGINAL_ID,
            reservation
        )
            .from(SEAT)
            .leftJoin(AUDITORIUM).on(SEAT.AUDITORIUM_ID.eq(AUDITORIUM.ID))
            .leftJoin(SCREENING).on(AUDITORIUM.ID.eq(SCREENING.AUDITORIUM_ID))
            .where(screeningConditions)
            .fetch()
            .map {
                WebGetReservationsResponse(
                    seatOriginalId = it[SEAT.ORIGINAL_ID],
                    reservationState = it[reservation] ?: ReservationState.FREE
                )
            }
    }
}
