package com.cleevio.cinemax.api.module.discountcardusage.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
class PosConfigurationForDiscountCardUsageNotFoundException : ApiException(
    Module.DISCOUNT_CARD_USAGE,
    DiscountCardUsageErrorType.POS_CONFIGURATION_FOR_DISCOUNT_CARD_USAGE_NOT_FOUND
)
