package com.cleevio.cinemax.api.module.discountcard.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
class DiscountCardIsNotValidException : ApiException(
    Module.DISCOUNT_CARD,
    DiscountCardErrorType.DISCOUNT_CARD_NOT_VALID
)
