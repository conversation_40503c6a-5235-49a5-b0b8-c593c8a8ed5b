package com.cleevio.cinemax.api.module.posconfiguration.service

import com.cleevio.cinemax.api.module.posconfiguration.entity.PosConfiguration
import com.cleevio.cinemax.api.module.posconfiguration.exception.PosConfigurationNotFoundException
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class PosConfigurationJpaFinderService(
    private val repository: PosConfigurationRepository,
) {
    fun findAll(): List<PosConfiguration> = repository.findAll()

    fun findById(id: UUID): PosConfiguration? = repository.findByIdOrNull(id)

    fun getById(id: UUID): PosConfiguration? = findById(id) ?: throw PosConfigurationNotFoundException()

    fun getOnlinePosConfigurationId(): UUID =
        repository.findOnlinePosConfiguration()?.id ?: throw PosConfigurationNotFoundException()

    fun findOnlinePosConfiguration(): PosConfiguration? = repository.findOnlinePosConfiguration()

    fun validateIsPhysicalPOS(id: UUID): Boolean = repository.validateIsPhysicalPOS(id)

    fun findByTitle(title: String): PosConfiguration? = repository.findByTitle(title)
}
