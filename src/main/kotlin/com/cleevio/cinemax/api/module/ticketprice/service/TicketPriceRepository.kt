package com.cleevio.cinemax.api.module.ticketprice.service

import com.cleevio.cinemax.api.module.ticketprice.entity.TicketPrice
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface TicketPriceRepository : JpaRepository<TicketPrice, UUID> {

    @Query(
        """
            SELECT tp.* FROM ticket_price tp
            JOIN ticket t ON t.ticket_price_id = tp.id
            WHERE tp.screening_id = :screeningId AND t.deleted_at IS NULL;
        """,
        nativeQuery = true
    )
    fun findAllByScreeningIdAndTicketIsNotDeleted(screeningId: UUID): List<TicketPrice>
}
