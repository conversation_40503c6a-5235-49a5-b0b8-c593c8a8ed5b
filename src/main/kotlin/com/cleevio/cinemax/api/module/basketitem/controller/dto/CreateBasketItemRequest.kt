package com.cleevio.cinemax.api.module.basketitem.controller.dto

import com.cleevio.cinemax.api.module.basket.service.command.CreateBasketItemInput
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType

data class CreateBasketItemRequest(
    val type: BasketItemType,

    val quantity: Int,
    val product: CreateProductRequest? = null,
    val ticket: CreateTicketRequest? = null,
) {

    fun toInput() = CreateBasketItemInput(
        type = type,
        quantity = quantity,
        product = product?.toInput(),
        ticket = ticket?.toInput()
    )
}
