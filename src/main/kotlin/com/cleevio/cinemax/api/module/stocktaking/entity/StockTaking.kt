package com.cleevio.cinemax.api.module.stocktaking.entity

import com.cleevio.cinemax.api.common.entity.UpdatableEntity
import jakarta.persistence.Column
import java.math.BigDecimal
import java.util.UUID
import jakarta.persistence.Entity as JpaEntity

@JpaEntity
class StockTaking(
    id: UUID = UUID.randomUUID(),

    @Column(name = "original_id")
    var originalId: Int? = null,

    @Column(name = "product_component_id", nullable = false)
    var productComponentId: UUID,

    @Column(name = "stock_quantity", nullable = false)
    var stockQuantity: BigDecimal,

    @Column(name = "stock_quantity_actual", nullable = false)
    var stockQuantityActual: BigDecimal,

    @Column(name = "stock_quantity_difference", nullable = false)
    var stockQuantityDifference: BigDecimal,

    @Column(name = "purchase_price", nullable = false)
    var purchasePrice: BigDecimal,

    @Column(name = "purchase_price_difference", nullable = false)
    var purchasePriceDifference: BigDecimal,
) : UpdatableEntity(id)
