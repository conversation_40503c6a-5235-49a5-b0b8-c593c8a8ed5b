package com.cleevio.cinemax.api.module.screening.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.common.dto.SimplePage
import com.cleevio.cinemax.api.common.service.query.SearchQueryDeprecated
import com.cleevio.cinemax.api.common.util.SimplePageMapper
import com.cleevio.cinemax.api.module.screening.controller.dto.ScreeningSearchResponse
import com.cleevio.cinemax.api.module.screening.controller.mapper.ScreeningSearchResponseMapper
import com.cleevio.cinemax.api.module.screening.service.ScreeningJooqFinderService
import com.cleevio.cinemax.api.module.screening.service.query.ScreeningFilter
import com.cleevio.cinemax.psql.tables.ScreeningColumnNames
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.data.domain.Pageable
import org.springframework.data.web.PageableDefault
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@Tag(name = "POS Screenings")
@RestController
@RequestMapping("/pos-app/screenings")
class ScreeningController(
    private val screeningJooqFinderService: ScreeningJooqFinderService,
    private val screeningSearchResponseMapper: ScreeningSearchResponseMapper,
) {

    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
    fun searchScreenings(
        @ParameterObject
        @PageableDefault(size = Integer.MAX_VALUE, sort = [ScreeningColumnNames.TIME])
        pageable: Pageable,
        @RequestBody filter: ScreeningFilter,
    ): SimplePage<ScreeningSearchResponse> {
        val screeningPage = screeningJooqFinderService.search(SearchQueryDeprecated(pageable, filter))
        val screeningResponses = screeningSearchResponseMapper.mapList(screeningPage.content)
        return SimplePageMapper.mapToSimplePage(screeningPage, screeningResponses)
    }
}
