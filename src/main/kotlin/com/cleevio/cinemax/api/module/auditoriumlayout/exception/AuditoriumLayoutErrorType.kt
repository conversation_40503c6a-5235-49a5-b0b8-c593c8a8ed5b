package com.cleevio.cinemax.api.module.auditoriumlayout.exception

import com.cleevio.cinemax.api.common.exception.ErrorType

enum class AuditoriumLayoutErrorType(
    override val code: String,
    override val message: String? = null,
) : ErrorType {
    AUDITORIUM_LAYOUT_ALREADY_EXISTS("100", "Auditorium layout already exists for given auditorium and code."),
    AUDITORIUM_LAYOUT_NOT_FOUND("101", "Auditorium layout not found."),
    AUDITORIUM_LAYOUT_SEATS_ALREADY_EXIST("102", "Seats already exist in auditorium layout."),
}
