package com.cleevio.cinemax.api.module.productcomponent.service.query

import org.springframework.data.domain.Pageable
import java.util.UUID

data class AdminSearchProductComponentsFilter(
    val productComponentIds: Set<UUID>? = null,
    val title: String? = null,
    val code: String? = null,
    val active: Boolean? = null,
    val zeroStock: Boolean? = null,
) {

    fun toQuery(pageable: Pageable) = AdminSearchProductComponentsQuery(filter = this, pageable = pageable)
}
