package com.cleevio.cinemax.api.module.table.util

import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Stoly

fun parseLabelAndOrder(mssqlTable: Stoly): Pair<String, Int?> {
    val name = mssqlTable.nazev?.trim() ?: return Pair("N/A", null)

    if (isTable(mssqlTable)) { // is table (not seat)
        val nameParts = name.split(" ")
        val namePart1 = nameParts[0]

        // name consists of one word only ('Buffet') -> first letter is assigned to label
        if (nameParts.size == 1) {
            return Pair(namePart1[0].toString().uppercase(), null)
        }
        val namePart2 = nameParts[1]
        return try {
            // if second part of table name contains number ('Table 1'), assign it to both label and order
            val order = namePart2.trim().toInt()
            val label = namePart2.trim()
            Pair(label, order)
        } catch (ex: NumberFormatException) {
            // if second part of table name is a single character, assign it to label ('Table A')
            // otherwise use first letter of table name ('Test server')
            val label = if (namePart2.trim().length == 1) namePart2.trim() else namePart1[0].toString().uppercase()
            Pair(label, null)
        }
    }

    return Pair(name, null)
}
