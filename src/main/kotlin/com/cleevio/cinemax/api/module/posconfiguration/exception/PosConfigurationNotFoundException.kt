package com.cleevio.cinemax.api.module.posconfiguration.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.NOT_FOUND)
class PosConfigurationNotFoundException : ApiException(
    Module.POS_CONFIGURATION,
    PosConfigurationErrorType.POS_CONFIGURATION_NOT_FOUND
)
