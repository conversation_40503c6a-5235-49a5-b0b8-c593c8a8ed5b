package com.cleevio.cinemax.api.module.productcomponent.controller.dto

import com.cleevio.cinemax.api.common.util.jackson.BigDecimalQuantitySerializer
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class AdminSearchProductComponentsResponse(
    val id: UUID,
    val code: String,
    val productComponentCategory: ProductComponentCategoryResponse,
    @field:JsonSerialize(using = BigDecimalQuantitySerializer::class)
    val stockQuantity: BigDecimal,
    val title: String,
    val unit: ProductComponentUnit,
    val purchasePrice: BigDecimal,
    val active: Boolean,
    val taxRateOverride: Int?,
    val purchasePriceOverride: BigDecimal?,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
)

@Schema(name = "AdminSearchProductComponentsProductComponentCategoryResponse")
data class ProductComponentCategoryResponse(
    val id: UUID,
    val title: String,
    val taxRate: Int,
)
