package com.cleevio.cinemax.api.module.terminalpayment.entity

import com.cleevio.cinemax.api.common.entity.UpdatableNonAuditableEntity
import com.cleevio.cinemax.api.module.terminalpayment.constant.TerminalPaymentResult
import jakarta.persistence.Column
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.util.UUID
import jakarta.persistence.Entity as JpaEntity

@JpaEntity
class TerminalPayment(
    id: UUID = UUID.randomUUID(),

    @Column(name = "basket_id", nullable = false)
    var basketId: UUID,

    @Column(name = "result")
    @Enumerated(EnumType.STRING)
    var result: TerminalPaymentResult? = null,

    @Column(name = "request", nullable = false)
    var request: String,

    @Column(name = "response")
    var response: String? = null,

    @Column(name = "terminal_ip_address", nullable = false)
    var terminalIpAddress: String,
) : UpdatableNonAuditableEntity(id)
