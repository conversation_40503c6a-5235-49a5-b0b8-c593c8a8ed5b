package com.cleevio.cinemax.api.module.posconfiguration.service.command

import com.cleevio.cinemax.api.common.annotation.NullOrNotBlank
import com.cleevio.cinemax.api.common.service.CreateOrUpdateCommand
import com.cleevio.cinemax.api.module.posconfiguration.constant.PosConfigurationType
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.posconfiguration.constant.TablesType
import jakarta.validation.constraints.NotBlank
import java.util.Optional
import java.util.UUID

data class CreateOrUpdatePosConfigurationCommand(
    override val id: UUID? = null,

    @field:NotBlank
    val macAddress: String,

    @field:NotBlank
    val title: String,

    @field:NotBlank
    val receiptsDirectory: String,

    val terminalDirectory: Optional<@NullOrNotBlank String>? = null,
    val terminalIpAddress: Optional<@NullOrNotBlank String>? = null,
    val terminalPort: Optional<Int>? = null,
    val ticketSalesEnabled: Boolean,
    val productModes: Set<ProductMode>,
    val tablesType: TablesType,
    val seatsEnabled: Boolean,
    val type: PosConfigurationType = PosConfigurationType.PHYSICAL,
) : CreateOrUpdateCommand()
