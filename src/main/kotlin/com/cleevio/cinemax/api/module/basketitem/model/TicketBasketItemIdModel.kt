package com.cleevio.cinemax.api.module.basketitem.model

import java.util.UUID

interface TicketBasketItemIdModelProjection {
    val basketItemId: UUID
    val ticketId: UUID
    val ticketPriceId: UUID
    val discountCardUsageId: UUID?
}

data class TicketBasketItemIdModel(
    override val basketItemId: UUID,
    override val ticketId: UUID,
    override val ticketPriceId: UUID,
    override val discountCardUsageId: UUID?,
) : TicketBasketItemIdModelProjection
