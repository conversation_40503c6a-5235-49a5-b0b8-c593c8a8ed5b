package com.cleevio.cinemax.api.module.productdiscount.controller.mapper

import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemJpaFinderService
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.productdiscount.constant.ProductDiscountType
import com.cleevio.cinemax.api.module.productdiscount.controller.dto.ProductDiscountSearchResponse
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class ProductDiscountResponseMapper(
    private val basketItemJpaFinderService: BasketItemJpaFinderService,
) {

    fun mapList(
        products: List<Product>,
        basketId: UUID? = null,
    ): List<ProductDiscountSearchResponse> {
        val basketProductDiscountIds = basketId?.let {
            basketItemJpaFinderService.findAllNonDeletedByBasketIdAndType(it, BasketItemType.PRODUCT_DISCOUNT)
                .map { item -> item.productId }
                .toSet()
        } ?: setOf()

        return products
            .map { mapSingle(it, !basketProductDiscountIds.contains(it.id)) }
            .sortedWith(compareBy({ it.type }, { it.amount }, { it.percentage }))
            .toList()
    }

    private fun mapSingle(
        product: Product,
        availableForBasket: Boolean = true,
    ) = ProductDiscountSearchResponse(
        id = product.id,
        title = product.title,
        type = product.discountPercentage?.let { ProductDiscountType.PERCENTAGE } ?: ProductDiscountType.ABSOLUTE,
        amount = product.discountAmount,
        percentage = product.discountPercentage,
        availableForBasket = availableForBasket
    )
}
