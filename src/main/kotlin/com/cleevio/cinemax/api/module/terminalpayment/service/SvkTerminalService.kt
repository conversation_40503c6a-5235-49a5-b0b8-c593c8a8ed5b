package com.cleevio.cinemax.api.module.terminalpayment.service

import com.cleevio.cinemax.api.common.constant.TERMINAL_PAYMENT
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.basket.exception.BasketPaymentPosConfigurationIdMissingException
import com.cleevio.cinemax.api.module.basket.service.BasketFinderService
import com.cleevio.cinemax.api.module.basket.util.validatePaymentInProgressBasketState
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationJooqFinderService
import com.cleevio.cinemax.api.module.terminalpayment.constant.TerminalConnectionType
import com.cleevio.cinemax.api.module.terminalpayment.constant.TerminalPaymentLockValues.EXECUTE_TERMINAL_PAYMENT
import com.cleevio.cinemax.api.module.terminalpayment.constant.TerminalPaymentResult
import com.cleevio.cinemax.api.module.terminalpayment.exception.InvalidTerminalConfigurationException
import com.cleevio.cinemax.api.module.terminalpayment.service.command.ExecuteTerminalPaymentCommand
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class SvkTerminalService(
    private val basketFinderService: BasketFinderService,
    private val posConfigurationJooqFinderService: PosConfigurationJooqFinderService,
    private val terminalSerialService: SvkTerminalSerialService,
    private val terminalEthernetService: SvkTerminalEthernetService,
) {
    private val log = logger()

    @Lock(TERMINAL_PAYMENT, EXECUTE_TERMINAL_PAYMENT)
    fun executeTerminalPayment(
        @Valid
        @LockFieldParameter("basketId")
        command: ExecuteTerminalPaymentCommand,
    ): TerminalPaymentResult {
        log.info("Executing terminal payment request for basketId=${command.basketId}.")

        val basket = basketFinderService.getNonDeletedById(command.basketId).also {
            validatePaymentInProgressBasketState(it)
        }
        val paymentPosConfigurationId = basket.paymentPosConfigurationId
            ?: throw BasketPaymentPosConfigurationIdMissingException()
        val posConfiguration = posConfigurationJooqFinderService.getById(paymentPosConfigurationId)

        return when (posConfiguration.determineTerminalConnectionType()) {
            TerminalConnectionType.SERIAL -> {
                terminalSerialService.executePayment(
                    basketId = basket.id,
                    terminalDirectory = posConfiguration.terminalDirectory ?: throw InvalidTerminalConfigurationException(),
                    request = generateTerminalSaleRequest(
                        price = basket.totalPrice,
                        terminalConnectionType = TerminalConnectionType.SERIAL
                    )
                )
            }

            TerminalConnectionType.ETHERNET -> terminalEthernetService.executePayment(
                basketId = basket.id,
                terminalIpAddress = posConfiguration.terminalIpAddress ?: throw InvalidTerminalConfigurationException(),
                terminalPort = posConfiguration.terminalPort ?: throw InvalidTerminalConfigurationException(),
                request = generateTerminalSaleRequest(
                    price = basket.totalPrice,
                    terminalConnectionType = TerminalConnectionType.ETHERNET
                )
            )
        }
    }
}
