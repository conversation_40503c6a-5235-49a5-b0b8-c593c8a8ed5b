package com.cleevio.cinemax.api.module.reservation.service

import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Rsedadla
import org.springframework.stereotype.Service

@Service
class ReservationMssqlFinderService(
    private val reservationMssqlFinderRepository: ReservationMssqlFinderRepository,
) {

    fun findByOriginalScreeningIdAndOriginalSeatId(originalScreeningId: Int, originalSeatId: Int): Rsedadla? {
        return reservationMssqlFinderRepository.findByOriginalScreeningIdAndOriginalSeatId(
            originalScreeningId = originalScreeningId,
            originalSeatId = originalSeatId
        )
    }
}
