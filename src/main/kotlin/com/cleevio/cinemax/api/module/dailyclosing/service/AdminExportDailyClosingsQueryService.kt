package com.cleevio.cinemax.api.module.dailyclosing.service

import com.cleevio.cinemax.api.common.util.Symbol
import com.cleevio.cinemax.api.common.util.buildDateCondition
import com.cleevio.cinemax.api.common.util.inOrNullIfNullOrEmpty
import com.cleevio.cinemax.api.common.util.paginationAndSorting
import com.cleevio.cinemax.api.module.dailyclosing.constant.DailyClosingState
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingsExportRecordModel
import com.cleevio.cinemax.api.module.dailyclosing.service.query.AdminExportDailyClosingsQuery
import com.cleevio.cinemax.psql.Tables.DAILY_CLOSING
import jakarta.validation.Valid
import org.jooq.Condition
import org.jooq.DSLContext
import org.jooq.impl.DSL.field
import org.jooq.impl.DSL.select
import org.jooq.impl.DSL.trueCondition
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.time.LocalDate

@Service
@Validated
class AdminExportDailyClosingsQueryService(
    private val psqlDslContext: DSLContext,
) {

    operator fun invoke(
        @Valid query: AdminExportDailyClosingsQuery,
    ): List<DailyClosingsExportRecordModel> {
        val closedAtField = field("{0}::date", LocalDate::class.java, DAILY_CLOSING.CLOSED_AT)
        val filterConditions = listOfNotNull(
            buildDateCondition(
                date = query.filter.closedAtFrom,
                column = closedAtField,
                symbol = Symbol.GREATER_OR_EQUAL
            ),
            buildDateCondition(
                date = query.filter.closedAtTo,
                column = closedAtField,
                symbol = Symbol.LESS_OR_EQUAL
            ),
            DAILY_CLOSING.posConfiguration().ID.inOrNullIfNullOrEmpty(query.filter.posConfigurationIds),
            DAILY_CLOSING.STATE.eq(DailyClosingState.CLOSED)
        )
        val aggregates = AGGREGATE_SEARCH_SELECT(fetchFilteredDailyClosingIds(filterConditions))

        return psqlDslContext.with(aggregates)
            .select(
                DAILY_CLOSING.CLOSED_AT,
                DAILY_CLOSING.FIXED_PRICE_TICKETS_AMOUNT,
                DAILY_CLOSING.posConfiguration().ID,
                DAILY_CLOSING.posConfiguration().TITLE,
                aggregates.salesCash(),
                aggregates.salesCashless(),
                aggregates.salesTotal(),
                aggregates.serviceFeesCash(),
                aggregates.serviceFeesCashless(),
                aggregates.cancelledCash(),
                aggregates.cancelledCashless(),
                aggregates.otherMovementsExpenses(),
                aggregates.otherMovementsRevenues(),
                aggregates.deduction(),
                aggregates.netSales()
            )
            .from(DAILY_CLOSING)
            .join(aggregates).on(aggregates.dailyClosingId()!!.eq(DAILY_CLOSING.ID))
            .where(trueCondition())
            .paginationAndSorting(Pageable.unpaged(), DAILY_CLOSING, DAILY_CLOSING.posConfiguration())
            .fetch()
            .map {
                DailyClosingsExportRecordModel(
                    closedAtDate = it[DAILY_CLOSING.CLOSED_AT].toLocalDate(),
                    closedAtTime = it[DAILY_CLOSING.CLOSED_AT].toLocalTime(),
                    posConfigurationTitle = it[DAILY_CLOSING.posConfiguration().TITLE],
                    salesCash = it[aggregates.salesCash()],
                    salesCashless = it[aggregates.salesCashless()],
                    salesTotal = it[aggregates.salesTotal()],
                    serviceFeesCash = it[aggregates.serviceFeesCash()],
                    serviceFeesCashless = it[aggregates.serviceFeesCashless()],
                    cancelledCash = it[aggregates.cancelledCash()],
                    cancelledCashless = it[aggregates.cancelledCashless()],
                    otherMovementsExpenses = it[aggregates.otherMovementsExpenses()],
                    otherMovementsRevenues = it[aggregates.otherMovementsRevenues()],
                    fixedPriceTicketsAmount = it[DAILY_CLOSING.FIXED_PRICE_TICKETS_AMOUNT],
                    deduction = it[aggregates.deduction()],
                    netSales = it[aggregates.netSales()]
                )
            }
    }

    private fun fetchFilteredDailyClosingIds(filterConditions: List<Condition>) = psqlDslContext.fetch(
        select(
            DAILY_CLOSING.ID
        )
            .from(DAILY_CLOSING)
            .join(DAILY_CLOSING.posConfiguration())
            .where(filterConditions)
    )
}
