package com.cleevio.cinemax.api.module.screening.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
class AuditoriumForScreeningNotFoundException : ApiException(
    Module.SCREENING,
    ScreeningErrorType.AUDITORIUM_FOR_SCREENING_NOT_FOUND
)
