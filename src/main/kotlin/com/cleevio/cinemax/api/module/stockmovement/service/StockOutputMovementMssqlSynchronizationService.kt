package com.cleevio.cinemax.api.module.stockmovement.service

import com.cleevio.cinemax.api.common.constant.STOCK_MOVEMENT
import com.cleevio.cinemax.api.common.service.AbstractMssqlSynchronizationService
import com.cleevio.cinemax.api.common.util.forceNegate
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.trimIfNotBlank
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentJpaFinderService
import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementLockValues.SYNCHRONIZE_STOCK_OUTPUT_MOVEMENT_FROM_MSSQL
import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementType
import com.cleevio.cinemax.api.module.stockmovement.service.command.CreateOrUpdateStockMovementCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlService
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.Rvydej.RVYDEJ
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Rvydej
import com.cleevio.library.lockinghandler.service.TryLock
import org.jooq.types.UByte
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class StockOutputMovementMssqlSynchronizationService(
    private val stockMovementService: StockMovementService,
    private val stockOutputMovementMssqlFinderRepository: StockOutputMovementMssqlFinderRepository,
    private val synchronizationFromMssqlFinderService: SynchronizationFromMssqlFinderService,
    private val productComponentJpaFinderService: ProductComponentJpaFinderService,
    synchronizationFromMssqlService: SynchronizationFromMssqlService,
) : AbstractMssqlSynchronizationService<Rvydej>(
    mssqlEntityName = RVYDEJ.name,
    synchronizationFromMssqlService = synchronizationFromMssqlService,
    synchronizationType = SynchronizationFromMssqlType.STOCK_OUTPUT_MOVEMENT
) {
    private val log = logger()

    @TryLock(STOCK_MOVEMENT, SYNCHRONIZE_STOCK_OUTPUT_MOVEMENT_FROM_MSSQL)
    fun synchronizeAll() = synchronizeAll {
        stockOutputMovementMssqlFinderRepository.findAllStockOutputMovementsByUpdatedAtGtAndType(
            updatedAt = synchronizationFromMssqlFinderService.findByType(SynchronizationFromMssqlType.STOCK_OUTPUT_MOVEMENT),
            acceptedTypes = SYNCABLE_MSSQL_STOCK_MOVEMENT_TYPES
        )
    }

    override fun validateAndPersist(mssqlEntity: Rvydej) {
        val productComponent = productComponentJpaFinderService.findNonDeletedByOriginalId(mssqlEntity.rzboziid) ?: run {
            log.warn(
                "ProductComponent with originalId=${mssqlEntity.rzboziid} not found. " +
                    "Skipping sync of output StockMovement originalId=${mssqlEntity.rvydejid}."
            )
            return
        }

        stockMovementService.syncCreateOrUpdateStockMovement(
            mapToCreateOrUpdateStockMovementCommand(
                mssqlEntity = mssqlEntity,
                productComponentId = productComponent.id
            )
        )
    }

    private fun mapToCreateOrUpdateStockMovementCommand(
        mssqlEntity: Rvydej,
        productComponentId: UUID,
    ) = CreateOrUpdateStockMovementCommand(
        // rvydej and rprijem sync to shared psql table, so we negate originalId in case of rvydej to avoid conflicts
        originalId = mssqlEntity.rvydejid.forceNegate(),
        productComponentId = productComponentId,
        type = mssqlEntity.getStockMovementType(),
        quantity = mssqlEntity.mnoz.toBigDecimal(),
        price = mssqlEntity.cena.toBigDecimal(),
        note = mssqlEntity.pozn.trimIfNotBlank(),
        recordedAt = mssqlEntity.zaloz
    )

    private fun Rvydej.getStockMovementType(): StockMovementType =
        MSSQL_STOCK_MOVEMENT_TYPE_TO_STOCK_MOVEMENT_TYPE_MAP[this.druhv.toInt()]
            ?: error("Unknown Stock Movement Type for druhv=${this.druhv.toInt()}.")
}

private val SYNCABLE_MSSQL_STOCK_MOVEMENT_TYPES = arrayOf(
    UByte.valueOf(2),
    UByte.valueOf(3)
)

private val MSSQL_STOCK_MOVEMENT_TYPE_TO_STOCK_MOVEMENT_TYPE_MAP = mapOf(
    2 to StockMovementType.WRITE_OFF_WARRANTY,
    3 to StockMovementType.CORRECTION,
    4 to StockMovementType.PRODUCT_SALES
)
