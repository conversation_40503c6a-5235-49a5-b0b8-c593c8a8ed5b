package com.cleevio.cinemax.api.module.screeningtypes.service

import com.cleevio.cinemax.api.module.screeningtypes.entity.ScreeningTypes
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface ScreeningTypesRepository : JpaRepository<ScreeningTypes, UUID> {

    fun findAllByScreeningIdAndBlacklistedFalse(screeningId: UUID): List<ScreeningTypes>

    fun findAllByScreeningId(screeningId: UUID): List<ScreeningTypes>

    fun findAllByScreeningIdIn(screeningIds: Set<UUID>): List<ScreeningTypes>

    fun findAllByScreeningIdInAndBlacklistedFalse(screeningIds: Set<UUID>): List<ScreeningTypes>

    fun findByScreeningIdAndScreeningTypeId(screeningId: UUID, screeningTypeId: UUID): ScreeningTypes?
}
