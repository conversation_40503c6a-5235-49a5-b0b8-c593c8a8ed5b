package com.cleevio.cinemax.api.module.basket.event.listener

import com.cleevio.cinemax.api.module.basket.event.BasketModifiedInPaymentInProgressStateEvent
import com.cleevio.cinemax.api.module.basket.event.BasketPaymentInitiatedEvent
import com.cleevio.cinemax.api.module.basket.service.BasketReceiptNumbersService
import com.cleevio.cinemax.api.module.basket.service.command.GenerateBasketReceiptNumbersCommand
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    "features.basket.receipt-numbers-generated-listener.enabled",
    havingValue = "true",
    matchIfMissing = true
)
class BasketBasketEventListener(
    private val basketReceiptNumbersService: BasketReceiptNumbersService,
) {
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToBasketPaymentInitiatedEvent(event: BasketPaymentInitiatedEvent) {
        basketReceiptNumbersService.generateBasketReceiptNumbers(
            GenerateBasketReceiptNumbersCommand(
                basketId = event.basketId
            )
        )
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToBasketModifiedInPaymentInProgressStateEvent(event: BasketModifiedInPaymentInProgressStateEvent) {
        basketReceiptNumbersService.generateBasketReceiptNumbers(
            GenerateBasketReceiptNumbersCommand(
                basketId = event.basketId
            )
        )
    }
}
