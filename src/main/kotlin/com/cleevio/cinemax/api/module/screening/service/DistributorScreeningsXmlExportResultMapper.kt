package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.serializeToXml
import com.cleevio.cinemax.api.module.screening.service.model.DistributorScreeningsExportXmlModel
import com.cleevio.cinemax.api.module.screening.service.model.DistributorScreeningsXmlExportRecordModel
import com.cleevio.cinemax.api.module.screening.service.model.ExportType
import com.cleevio.cinemax.api.module.screening.service.model.FormatTypes
import com.cleevio.cinemax.api.module.screening.service.model.LanguageTypes
import com.cleevio.cinemax.api.module.screening.service.model.PriceAdmissionsType
import com.cleevio.cinemax.api.module.screening.service.model.PriceType
import com.cleevio.cinemax.api.module.screening.service.model.ResultType
import com.cleevio.cinemax.api.module.screening.service.model.Results
import org.springframework.stereotype.Component
import java.io.ByteArrayInputStream
import java.time.LocalDateTime

@Component
class DistributorScreeningsXmlExportResultMapper(
    private val cinemaxConfigProperties: CinemaxConfigProperties,
) {
    fun mapToExportResultModel(
        data: List<DistributorScreeningsXmlExportRecordModel>,
    ): ExportResultModel = mapToDistributorScreeningsExportXmlModel(
        data = data,
        branchName = cinemaxConfigProperties.branchName,
        cinemaCode = cinemaxConfigProperties.branchCode
    ).serializeToXml()
        .toByteArray()
        .let { xmlByteArray ->
            ExportResultModel(
                inputStream = ByteArrayInputStream(xmlByteArray),
                size = xmlByteArray.size.toLong()
            )
        }

    private fun mapToDistributorScreeningsExportXmlModel(
        data: List<DistributorScreeningsXmlExportRecordModel>,
        branchName: String,
        cinemaCode: String,
    ): DistributorScreeningsExportXmlModel =
        DistributorScreeningsExportXmlModel(
            export = ExportType(
                code = cinemaCode,
                cinema = "CINEMAX $branchName",
                securityCode = "",
                exportDate = LocalDateTime.now()
            ),
            results = Results(
                resultList = data.flatMap {
                    it.screenings.map { screening ->
                        ResultType(
                            id = screening.screeningOriginalId.toString(),
                            date = screening.screeningDateTime,
                            filmCode = screening.movieDisfilmCode ?: screening.movieCode,
                            title = screening.movieRawTitle,
                            format = resolveFormatType(screening.movieTechnologyTitle),
                            language = resolveLanguageType(screening.movieLanguageCode),
                            boxOffice = screening.groTicketSales,
                            admissions = screening.ticketsCount,
                            price = screening.ticketBasePrice,
                            priceAdmissions = PriceAdmissionsType(
                                screening.tickets.map { ticket ->
                                    PriceType(
                                        admissions = ticket.ticketsCount,
                                        price = ticket.ticketPrice
                                    )
                                }
                            )
                        )
                    }
                }
            )
        )

    fun resolveFormatType(movieFormatCode: String?): FormatTypes? =
        movieFormatCode?.let { FormatTypes.entries.associateBy { type -> type.value.lowercase() }[it.lowercase()] }

    fun resolveLanguageType(languageCode: String): LanguageTypes =
        LanguageTypes.entries.associateBy { it.value }[languageCode]
            ?: run {
                logger().warn("Parsing movie language type $languageCode is not supported, setting ORIG type.")
                return LanguageTypes.ORIG
            }
}
