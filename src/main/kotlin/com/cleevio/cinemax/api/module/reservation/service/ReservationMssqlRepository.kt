package com.cleevio.cinemax.api.module.reservation.service

import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables.RSEDADLA
import org.jooq.DSLContext
import org.springframework.stereotype.Repository

@Repository
class ReservationMssqlRepository(
    private val mssqlCinemaxDslContext: DSLContext,
) {

    fun updateGroupReservationId(originalId: Int, groupReservationId: Int) {
        mssqlCinemaxDslContext
            .update(RSEDADLA)
            .set(RSEDADLA.REZERVACEID, groupReservationId)
            .where(RSEDADLA.REZERVACEID.eq(originalId))
            .execute()
    }
}
