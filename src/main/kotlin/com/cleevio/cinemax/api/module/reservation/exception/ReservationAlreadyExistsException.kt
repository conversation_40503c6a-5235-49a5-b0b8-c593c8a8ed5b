package com.cleevio.cinemax.api.module.reservation.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.CONFLICT)
class ReservationAlreadyExistsException : ApiException(
    Module.RESERVATION,
    ReservationErrorType.RESERVATION_ALREADY_EXISTS
)
