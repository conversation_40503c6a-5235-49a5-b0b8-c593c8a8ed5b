package com.cleevio.cinemax.api.module.pricecategory.service

import com.cleevio.cinemax.api.module.pricecategory.controller.dto.AdminGetPriceCategoryResponse
import com.cleevio.cinemax.api.module.pricecategory.controller.dto.PriceCategoryItemResponse
import com.cleevio.cinemax.api.module.pricecategory.exception.PriceCategoryNotFoundException
import com.cleevio.cinemax.api.module.pricecategory.service.query.AdminGetPriceCategoryQuery
import com.cleevio.cinemax.psql.Tables.PRICE_CATEGORY
import com.cleevio.cinemax.psql.Tables.PRICE_CATEGORY_ITEM
import jakarta.validation.Valid
import org.jooq.DSLContext
import org.jooq.impl.DSL.multiset
import org.jooq.impl.DSL.select
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class AdminGetPriceCategoryQueryService(
    private val psqlDslContext: DSLContext,
) {

    operator fun invoke(
        @Valid query: AdminGetPriceCategoryQuery,
    ): AdminGetPriceCategoryResponse {
        val priceCategoryItems = multiset(
            select(
                PRICE_CATEGORY_ITEM.NUMBER,
                PRICE_CATEGORY_ITEM.TITLE,
                PRICE_CATEGORY_ITEM.PRICE,
                PRICE_CATEGORY_ITEM.DISCOUNTED
            ).from(PRICE_CATEGORY_ITEM)
                .where(PRICE_CATEGORY_ITEM.PRICE_CATEGORY_ID.eq(PRICE_CATEGORY.ID))
        )

        return psqlDslContext.select(
            PRICE_CATEGORY.ID,
            PRICE_CATEGORY.TITLE,
            PRICE_CATEGORY.ACTIVE,
            PRICE_CATEGORY.CREATED_AT,
            PRICE_CATEGORY.UPDATED_AT,
            priceCategoryItems
        ).from(PRICE_CATEGORY)
            .where(PRICE_CATEGORY.ID.eq(query.priceCategoryId))
            .fetchOne()
            ?.map {
                AdminGetPriceCategoryResponse(
                    id = it[PRICE_CATEGORY.ID],
                    title = it[PRICE_CATEGORY.TITLE],
                    active = it[PRICE_CATEGORY.ACTIVE],
                    createdAt = it[PRICE_CATEGORY.CREATED_AT],
                    updatedAt = it[PRICE_CATEGORY.UPDATED_AT],
                    items = it[priceCategoryItems].map { item ->
                        PriceCategoryItemResponse(
                            number = item[PRICE_CATEGORY_ITEM.NUMBER],
                            title = item[PRICE_CATEGORY_ITEM.TITLE],
                            price = item[PRICE_CATEGORY_ITEM.PRICE],
                            discounted = item[PRICE_CATEGORY_ITEM.DISCOUNTED]
                        )
                    }
                )
            } ?: throw PriceCategoryNotFoundException()
    }
}
