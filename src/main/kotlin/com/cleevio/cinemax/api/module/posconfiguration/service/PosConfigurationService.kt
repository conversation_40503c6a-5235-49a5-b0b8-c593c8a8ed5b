package com.cleevio.cinemax.api.module.posconfiguration.service

import com.cleevio.cinemax.api.common.constant.POS_CONFIGURATION
import com.cleevio.cinemax.api.module.posconfiguration.constant.PosConfigurationLockValues.CREATE_OR_UPDATE_POS_CONFIGURATION
import com.cleevio.cinemax.api.module.posconfiguration.entity.PosConfiguration
import com.cleevio.cinemax.api.module.posconfiguration.exception.InvalidPosConfigurationException
import com.cleevio.cinemax.api.module.posconfiguration.service.command.CreateOrUpdatePosConfigurationCommand
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.util.UUID
import kotlin.jvm.optionals.getOrNull

@Service
@Validated
class PosConfigurationService(
    private val posConfigurationRepository: PosConfigurationRepository,
) {

    @Transactional
    @Lock(POS_CONFIGURATION, CREATE_OR_UPDATE_POS_CONFIGURATION)
    fun createOrUpdatePosConfiguration(
        @Valid
        @LockFieldParameter("macAddress")
        command: CreateOrUpdatePosConfigurationCommand,
    ): PosConfiguration {
        if (command.terminalDirectory != null && command.terminalIpAddress != null) {
            throw InvalidPosConfigurationException()
        }

        posConfigurationRepository.findByMacAddress(command.macAddress)?.let {
            return posConfigurationRepository.save(
                mapToExistingPosConfiguration(
                    posConfiguration = it,
                    command = command
                )
            )
        } ?: run {
            return posConfigurationRepository.save(mapToNewPosConfiguration(command))
        }
    }

    private fun mapToExistingPosConfiguration(
        posConfiguration: PosConfiguration,
        command: CreateOrUpdatePosConfigurationCommand,
    ): PosConfiguration {
        command.terminalDirectory?.let { posConfiguration.terminalDirectory = it.getOrNull() }
        command.terminalIpAddress?.let { posConfiguration.terminalIpAddress = it.getOrNull() }
        command.terminalPort?.let { posConfiguration.terminalPort = it.getOrNull() }

        return posConfiguration.apply {
            title = command.title
            receiptsDirectory = command.receiptsDirectory
            ticketSalesEnabled = command.ticketSalesEnabled
            productModes = command.productModes
            tablesType = command.tablesType
            seatsEnabled = command.seatsEnabled
        }.also {
            it.setOrDeleteDefaultTerminalPortIfNeeded()
        }
    }

    private fun mapToNewPosConfiguration(
        command: CreateOrUpdatePosConfigurationCommand,
    ) = PosConfiguration(
        id = command.id ?: UUID.randomUUID(),
        macAddress = command.macAddress,
        title = command.title,
        receiptsDirectory = command.receiptsDirectory,
        terminalDirectory = command.terminalDirectory?.orElse(null),
        terminalIpAddress = command.terminalIpAddress?.orElse(null),
        terminalPort = command.terminalPort?.orElse(null),
        ticketSalesEnabled = command.ticketSalesEnabled,
        productModes = command.productModes,
        tablesType = command.tablesType,
        seatsEnabled = command.seatsEnabled,
        type = command.type
    ).also {
        it.setOrDeleteDefaultTerminalPortIfNeeded()
    }
}
