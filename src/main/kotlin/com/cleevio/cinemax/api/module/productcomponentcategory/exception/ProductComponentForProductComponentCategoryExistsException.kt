package com.cleevio.cinemax.api.module.productcomponentcategory.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
class ProductComponentForProductComponentCategoryExistsException : ApiException(
    Module.PRODUCT_COMPONENT_CATEGORY,
    ProductComponentCategoryErrorType.PRODUCT_COMPONENT_FOR_PRODUCT_COMPONENT_CATEGORY_EXISTS
)
