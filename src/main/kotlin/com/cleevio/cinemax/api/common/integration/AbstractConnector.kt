package com.cleevio.cinemax.api.common.integration

import com.cleevio.cinemax.api.common.util.logger
import org.slf4j.Logger
import org.springframework.http.HttpEntity
import org.springframework.http.HttpRequest
import org.springframework.http.HttpStatusCode
import org.springframework.http.client.BufferingClientHttpRequestFactory
import org.springframework.http.client.ClientHttpRequestExecution
import org.springframework.http.client.ClientHttpRequestInterceptor
import org.springframework.http.client.ClientHttpResponse
import org.springframework.http.client.SimpleClientHttpRequestFactory
import org.springframework.web.client.RestClient
import org.springframework.web.client.toEntity
import java.io.BufferedReader
import java.io.InputStreamReader
import java.nio.charset.StandardCharsets

abstract class AbstractConnector(
    baseUrl: String,
    restClientBuilder: (RestClient.Builder) -> Unit = {},
    enableRequestLogging: Boolean,
) {

    private val log = logger()

    protected val restClient: RestClient = RestClient
        .builder()
        .baseUrl(baseUrl)
        .apply {
            // creates a reusable copy of the response body, allowing to read it multiple times
            it.requestFactory(BufferingClientHttpRequestFactory(SimpleClientHttpRequestFactory()))

            if (enableRequestLogging) {
                it.requestInterceptor(LoggingInterceptor(logger = log))
            }
        }
        .also(restClientBuilder)
        .build()

    protected open val errorStatusPredicate: (HttpStatusCode) -> Boolean = { !it.is2xxSuccessful }

    protected fun RestClient.ResponseSpec.errorStatusHandler(
        statusPredicate: (HttpStatusCode) -> Boolean = errorStatusPredicate,
    ): RestClient.ResponseSpec = onStatus(statusPredicate, errorhandler)

    protected inline fun <reified T : Any> RestClient.RequestHeadersSpec<*>.retrieveResponseWithErrorHandler(): T =
        retrieve()
            .errorStatusHandler()
            .toEntity<T>()
            .getBodyOrThrow()

    protected fun RestClient.RequestHeadersSpec<*>.retrieveWithErrorHandler() {
        retrieve()
            .errorStatusHandler()
            .toBodilessEntity()
    }

    protected fun <T> HttpEntity<T>.getBodyOrThrow(): T =
        body ?: error("${this.javaClass.simpleName} could not parse response body")

    private val errorhandler = RestClient.ResponseSpec.ErrorHandler { request, response ->
        log.error(
            "An error code occurred while calling ${request.uri}. " +
                "Response [code: ${response.statusCode}, " +
                "status: ${response.statusText}, " +
                "body: ${BufferedReader(response.body.reader()).readLines()}]"
        )

        error("${this.javaClass.simpleName} integration error")
    }
}

class LoggingInterceptor(
    val logger: Logger,
) : ClientHttpRequestInterceptor {

    override fun intercept(
        request: HttpRequest,
        body: ByteArray,
        execution: ClientHttpRequestExecution,
    ): ClientHttpResponse {
        logRequest(request, body)

        val startTime = System.currentTimeMillis()
        val response = execution.execute(request, body)
        val duration = System.currentTimeMillis() - startTime

        logResponse(response, duration)

        return response
    }

    private fun logRequest(request: HttpRequest, body: ByteArray) {
        logger.info("=== HTTP REQUEST ===")
        logger.info("URI: ${request.method} ${request.uri}")
        logger.info("Headers: ${request.headers}")
        if (body.isNotEmpty()) {
            logger.info("Body: ${String(body, StandardCharsets.UTF_8)}")
        }
    }

    private fun logResponse(response: ClientHttpResponse, durationMs: Long) {
        // val body = try {
        //     BufferedReader(InputStreamReader(response.body, StandardCharsets.UTF_8)).use { it.readText() }
        // } catch (ex: Exception) {
        //     "Could not read response body: ${ex.message}"
        // }

        logger.info("=== HTTP RESPONSE === (${durationMs}ms)")
        logger.info("Status: ${response.statusCode}")
        logger.info("Headers: ${response.headers}")
        if (response.statusCode.is2xxSuccessful) {
            logger.info("Body: ${BufferedReader(InputStreamReader(response.body, StandardCharsets.UTF_8)).use { it.readText() }}")
        }
    }
}
