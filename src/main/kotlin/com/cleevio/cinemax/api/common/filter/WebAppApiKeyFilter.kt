package com.cleevio.cinemax.api.common.filter

import com.cleevio.cinemax.api.common.constant.API_KEY_HEADER_STRING
import com.cleevio.cinemax.api.common.constant.WEB_APP_API_KEY_MATCHER
import com.cleevio.cinemax.api.common.validation.ApiKeyValidator
import com.cleevio.cinemax.api.module.employee.exception.AuthenticationFailedException
import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter

@Component
class WebAppApiKeyFilter(
    @Value("\${cinemax.configuration.security.web-app-api-key}") private val apiKey: String,
) : OncePerRequestFilter() {

    override fun shouldNotFilter(request: HttpServletRequest): Boolean = !WEB_APP_API_KEY_MATCHER.matches(request)

    private val apiKeyValidator = ApiKeyValidator(supportedApiKey = apiKey)

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain,
    ) {
        val providedApiKey = request.getHeader(API_KEY_HEADER_STRING)
            ?: throw AuthenticationFailedException()
        apiKeyValidator.validate(providedApiKey)

        filterChain.doFilter(request, response)
    }
}
