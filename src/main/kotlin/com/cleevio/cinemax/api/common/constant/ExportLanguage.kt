package com.cleevio.cinemax.api.common.constant

enum class ExportLanguage {
    SLOVAK,
    CZECH,
    ROMANIAN,
    ;

    companion object {
        fun fromBusinessCountry(businessCountry: BusinessCountry): ExportLanguage {
            return when (businessCountry) {
                BusinessCountry.SK -> SLOVAK
                BusinessCountry.CZ -> CZECH
                BusinessCountry.RO -> ROMANIAN
            }
        }
    }
}
