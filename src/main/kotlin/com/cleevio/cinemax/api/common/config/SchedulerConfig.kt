package com.cleevio.cinemax.api.common.config

import org.springframework.boot.autoconfigure.condition.ConditionalOnThreading
import org.springframework.boot.autoconfigure.thread.Threading
import org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.scheduling.concurrent.SimpleAsyncTaskScheduler

@Configuration
@EnableScheduling
class SchedulerConfig {

    /**
     * This is not needed unless you are using @EnableWebSocketMessageBroker
     * Because AbstractMessageBrokerConfiguration TaskScheduler bean kept overriding default Spring TaskScheduler
     */
    @Bean
    @ConditionalOnThreading(Threading.VIRTUAL)
    fun taskScheduler(builder: SimpleAsyncTaskSchedulerBuilder): SimpleAsyncTaskScheduler = builder.build()
}
