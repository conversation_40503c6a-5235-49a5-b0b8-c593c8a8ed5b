package com.cleevio.cinemax.api.common.config

import com.cleevio.cinemax.api.common.dto.ErrorResponse
import com.cleevio.cinemax.api.common.exception.ApiException
import com.cleevio.cinemax.api.common.util.logger
import jakarta.validation.ConstraintViolation
import jakarta.validation.ConstraintViolationException
import jakarta.validation.ValidationException
import org.springframework.core.annotation.AnnotationUtils
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.security.access.AccessDeniedException
import org.springframework.validation.BindException
import org.springframework.validation.FieldError
import org.springframework.validation.ObjectError
import org.springframework.web.HttpMediaTypeNotAcceptableException
import org.springframework.web.HttpMediaTypeNotSupportedException
import org.springframework.web.HttpRequestMethodNotSupportedException
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.MissingServletRequestParameterException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException
import java.util.stream.Collectors

@ControllerAdvice
class ExceptionConfig {

    private val logger = logger()

    @ExceptionHandler(ApiException::class)
    fun handleApiException(ex: ApiException): ResponseEntity<ErrorResponse> {
        val status = AnnotationUtils.findAnnotation(ex.javaClass, ResponseStatus::class.java)
        return handleException(
            ex.message,
            status?.code ?: HttpStatus.INTERNAL_SERVER_ERROR,
            ex.errorCode
        )
    }

    /**
     * This exception occurs if request body is missing or has invalid format
     */
    @ExceptionHandler(HttpMessageNotReadableException::class)
    fun handleBodyMissingException(): ResponseEntity<ErrorResponse> {
        return handleException("Invalid or missing body.", HttpStatus.BAD_REQUEST)
    }

    /**
     * This exception occurs if http method is not supported
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException::class)
    fun handleMethodNotAllowedException(): ResponseEntity<ErrorResponse> {
        return handleException("Method not allowed.", HttpStatus.METHOD_NOT_ALLOWED)
    }

    /**
     * This exception occurs if body does not meet required constraints
     */
    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun handleBodyInvalidException(ex: MethodArgumentNotValidException): ResponseEntity<ErrorResponse> {
        val output = ex.bindingResult.fieldErrors.stream()
            .map { e: FieldError -> e.field + " " + e.defaultMessage }
            .collect(Collectors.toSet())
        output.addAll(
            ex.bindingResult.globalErrors.stream()
                .map { obj: ObjectError -> obj.defaultMessage }
                .collect(Collectors.toSet())
        )
        return handleException(output, HttpStatus.BAD_REQUEST)
    }

    /**
     * This exception occurs if body does not meet required constraints
     */
    @ExceptionHandler(ConstraintViolationException::class)
    fun handleConstraintException(ex: ConstraintViolationException): ResponseEntity<ErrorResponse> {
        val output = ex.constraintViolations.stream()
            .map { v: ConstraintViolation<*> -> v.propertyPath.toString() + " " + v.message }
            .collect(Collectors.toSet())
        return handleException(output, HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException::class, BindException::class, ValidationException::class)
    fun handleMethodInvalidException(ex: Exception): ResponseEntity<ErrorResponse> {
        return if (ex.message == null) {
            handleException(emptySet(), HttpStatus.BAD_REQUEST)
        } else {
            handleException(ex.message!!, HttpStatus.BAD_REQUEST)
        }
    }

    /**
     * This exception occurs if user is not allowed to perform requester action
     */
    @ExceptionHandler(AccessDeniedException::class)
    fun handleAclException(): ResponseEntity<ErrorResponse> {
        return handleException("Access denied.", HttpStatus.FORBIDDEN)
    }

    /**
     * This exception occurs if client didn't provide required request param
     */
    @ExceptionHandler(MissingServletRequestParameterException::class)
    fun handleMissingServletRequestParameterException(): ResponseEntity<ErrorResponse> {
        return handleException("Missing required request param(s).", HttpStatus.BAD_REQUEST)
    }

    @ExceptionHandler(HttpMediaTypeNotAcceptableException::class, HttpMediaTypeNotSupportedException::class)
    fun handleHttpMediaTypeNotAcceptableException(): ResponseEntity<ErrorResponse> {
        return handleException("Unsupported media type requested.", HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    }

    /**
     * Handle other exceptions
     */
    @ExceptionHandler(Exception::class)
    fun handleOther(ex: Exception?): ResponseEntity<ErrorResponse> {
        logger.error("Internal error occurred.", ex)
        return handleException("Internal server error.", HttpStatus.INTERNAL_SERVER_ERROR)
    }

    protected fun handleException(
        message: String,
        status: HttpStatus,
        code: String = DEFAULT_ERROR_CODE,
    ): ResponseEntity<ErrorResponse> {
        return handleException(setOf(message), status, code)
    }

    protected fun handleException(
        messages: Collection<String>,
        status: HttpStatus,
        code: String = DEFAULT_ERROR_CODE,
    ): ResponseEntity<ErrorResponse> {
        return ResponseEntity(ErrorResponse(messages, code), HttpHeaders(), status)
    }
}

private const val DEFAULT_ERROR_CODE = "0"
