package com.cleevio.cinemax.api.common.util.jooq

import org.jooq.Converter
import java.time.LocalDateTime

class CustomLocalDateTimeConverter : Converter<LocalDateTime?, LocalDateTime?> {

    override fun from(databaseObject: LocalDateTime?): LocalDateTime? = databaseObject
    override fun to(userObject: LocalDateTime?): LocalDateTime? = userObject
    override fun fromType() = LocalDateTime::class.java as Class<LocalDateTime?>
    override fun toType() = LocalDateTime::class.java as Class<LocalDateTime?>
}
