package com.cleevio.cinemax.api.common.util.jackson

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.JsonSerializer
import com.fasterxml.jackson.databind.SerializerProvider
import java.math.BigDecimal
import java.math.RoundingMode

class BigDecimalReceiptSerializer : JsonSerializer<BigDecimal?>() {

    override fun serialize(value: BigDecimal?, gen: JsonGenerator, serializers: SerializerProvider) {
        value?.let {
            gen.writeNumber(value.setScale(6, RoundingMode.HALF_UP))
        } ?: gen.writeNull()
    }
}
