package com.cleevio.cinemax.api.common.util.jackson

import com.cleevio.cinemax.api.common.util.toCents
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.JsonSerializer
import com.fasterxml.jackson.databind.SerializerProvider
import java.math.BigDecimal

class BigDecimalSerializer : JsonSerializer<BigDecimal?>() {

    override fun serialize(value: BigDecimal?, gen: JsonGenerator, serializers: SerializerProvider) {
        value?.let {
            gen.writeNumber(value.toCents())
        } ?: gen.writeNull()
    }
}
