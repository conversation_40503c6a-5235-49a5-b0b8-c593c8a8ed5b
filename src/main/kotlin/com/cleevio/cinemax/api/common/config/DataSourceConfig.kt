package com.cleevio.cinemax.api.common.config

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.data.transaction.ChainedTransactionManager
import org.springframework.jdbc.datasource.DataSourceTransactionManager
import org.springframework.orm.jpa.JpaTransactionManager
import javax.sql.DataSource

@Configuration
class DataSourceConfig(
    @Qualifier("psqlDataSourceProperties")
    private val psqlDataSourceProperties: DataSourceProperties,

    @Qualifier("mssqlCinemaxDataSourceProperties")
    private val mssqlCinemaxDataSourceProperties: DataSourceProperties,

    @Qualifier("mssqlBuffetDataSourceProperties")
    private val mssqlBuffetDataSourceProperties: DataSourceProperties,
) {

    @Bean
    @Primary
    fun psqlDataSource(): DataSource {
        return psqlDataSourceProperties
            .initializeDataSourceBuilder()
            .build()
    }

    @Bean
    @Primary
    fun transactionManager(): JpaTransactionManager {
        return JpaTransactionManager().apply {
            dataSource = psqlDataSource()
        }
    }

    @Bean
    fun mssqlCinemaxDataSource(): DataSource {
        return mssqlCinemaxDataSourceProperties
            .initializeDataSourceBuilder()
            .build()
    }

    @Bean
    fun mssqlCinemaxTransactionManager(): DataSourceTransactionManager {
        return DataSourceTransactionManager(mssqlCinemaxDataSource())
    }

    @Bean
    fun mssqlBuffetDataSource(): DataSource {
        return mssqlBuffetDataSourceProperties
            .initializeDataSourceBuilder()
            .build()
    }

    @Bean
    fun mssqlBuffetTransactionManager(): DataSourceTransactionManager {
        return DataSourceTransactionManager(mssqlBuffetDataSource())
    }

    @Bean
    fun mssqlChainedTransactionManager(): ChainedTransactionManager {
        return ChainedTransactionManager(mssqlCinemaxTransactionManager(), mssqlBuffetTransactionManager())
    }
}
