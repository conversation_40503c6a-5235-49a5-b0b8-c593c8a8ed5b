package com.cleevio.cinemax.api.common.integration.disfilm

import com.cleevio.cinemax.api.common.integration.AbstractConnector
import com.cleevio.cinemax.api.common.integration.disfilm.xml.DISFilmMovie
import com.cleevio.cinemax.api.common.integration.disfilm.xml.DISFilmMovieData
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.MediaType
import org.springframework.http.converter.xml.MappingJackson2XmlHttpMessageConverter
import org.springframework.stereotype.Component
import org.springframework.web.client.RestClient
import org.springframework.web.client.body

@Component
class DISFilmConnector(
    @Value("\${integration.disfilm.base-url}")
    baseUrl: String,
) : AbstractConnector(
    baseUrl = baseUrl,
    restClientBuilder = xmlRestClientBuilder,
    enableRequestLogging = false
) {

    fun getMovies(): List<DISFilmMovie> {
        return restClient.get()
            .uri {
                it.path("/movies")
                    .queryParam("by", "format")
                    .build()
            }
            .accept(MediaType.TEXT_XML)
            .retrieve()
            .body<DISFilmMovieData>()?.data ?: error("DISFilm integration error.")
    }
}

private val xmlRestClientBuilder: (RestClient.Builder) -> Unit = { builder ->
    builder.messageConverters {
        it.add(MappingJackson2XmlHttpMessageConverter())
    }
}
