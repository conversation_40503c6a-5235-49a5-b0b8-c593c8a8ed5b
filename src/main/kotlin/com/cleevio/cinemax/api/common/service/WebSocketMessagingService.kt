package com.cleevio.cinemax.api.common.service

import com.cleevio.cinemax.api.common.constant.WebSocketEndpoint
import com.cleevio.cinemax.api.common.message.WebSocketMessage
import com.cleevio.cinemax.api.common.util.logger
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.messaging.simp.SimpMessagingTemplate
import org.springframework.stereotype.Service

@Service
class WebSocketMessagingService(
    private val simpMessagingTemplate: SimpMessagingTemplate,
) {
    private val log = logger()

    fun sendMessage(message: WebSocketMessage, destination: String = WebSocketEndpoint.RESERVATIONS_TOPIC_ENDPOINT) {
        log.debug("Sending message=$message to $destination.")
        simpMessagingTemplate.convertAndSend(destination, mapMessageToString(message))
    }

    private fun mapMessageToString(message: WebSocketMessage): String {
        return OBJECT_MAPPER.writeValueAsString(message)
    }
}

private val OBJECT_MAPPER = ObjectMapper()
