package com.cleevio.cinemax.api.common.util

import java.math.BigDecimal
import java.math.RoundingMode
import java.text.DecimalFormat
import java.util.Locale

fun BigDecimal?.toIntDown(): Int = this?.setScale(0, RoundingMode.DOWN)?.intValueExact() ?: 0
fun BigDecimal.negateIf(flag: Boolean): BigDecimal = if (flag) this.negate() else this
fun BigDecimal.toCents(): BigDecimal = this.setScale(2, RoundingMode.HALF_UP)
fun BigDecimal.toDecimalCommaString(): String {
    DECIMAL_FORMAT.minimumFractionDigits = 2
    DECIMAL_FORMAT.maximumFractionDigits = this.scale().coerceAtLeast(0)
    DECIMAL_FORMAT.isGroupingUsed = false
    return DECIMAL_FORMAT.format(this)
}
fun BigDecimal.isPositive(): Boolean = this isGreaterThan 0.toBigDecimal()
fun BigDecimal.toSixPlaces(): BigDecimal = this.setScale(6, RoundingMode.HALF_UP)

infix fun BigDecimal?.isEqualTo(other: BigDecimal?) = this?.let { it.compareTo(other) == 0 } ?: (other == null)
infix fun BigDecimal?.isNotEqualTo(other: BigDecimal?) = this?.let { it.compareTo(other) != 0 } ?: (other == null)

fun BigDecimal.addTax(taxRate: Int): BigDecimal =
    this.multiply(taxRate.toOnePlusConstant()).setScale(2, RoundingMode.HALF_UP)
fun BigDecimal.subtractTax(taxRate: Int): BigDecimal =
    this.divide(taxRate.toOnePlusConstant(), 2, RoundingMode.HALF_UP)

fun BigDecimal.isWholeNumber(): Boolean = this.rem(1.toBigDecimal()) isEqualTo 0.toBigDecimal()
fun BigDecimal.forceNegate(): BigDecimal = if (this.isLessThan(0.toBigDecimal())) this else this.negate()

private val DECIMAL_FORMAT = DecimalFormat.getInstance(Locale.GERMAN)
