package com.cleevio.cinemax.api.common.holiday

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.BusinessCountry
import de.focus_shift.jollyday.core.HolidayManager
import de.focus_shift.jollyday.core.parameter.UrlManagerParameter
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.Year
import java.util.Properties

@Component
class HolidayResolver(
    private val cinemaxConfigProperties: CinemaxConfigProperties,
) {

    fun isNationalHoliday(date: LocalDate): Boolean {
        val fileName = when (cinemaxConfigProperties.businessCountry) {
            BusinessCountry.CZ -> CZECHIA_SOURCE_FILE
            BusinessCountry.SK -> SLOVAKIA_SOURCE_FILE
            BusinessCountry.RO -> ROMANIA_SOURCE_FILE
        }
        val url = this::class.java.classLoader.getResource(fileName)
            ?: throw IllegalArgumentException("Holiday file $fileName not found in resources")
        val urlParameter = UrlManagerParameter(url, Properties())
        val holidayManager = HolidayManager.getInstance(urlParameter)

        val holidays = holidayManager.getHolidays(Year.from(date)).map { it.date }
        return date in holidays
    }
}

private const val CZECHIA_SOURCE_FILE = "holidays/czechia.xml"
private const val SLOVAKIA_SOURCE_FILE = "holidays/slovakia.xml"
private const val ROMANIA_SOURCE_FILE = "holidays/romania.xml"
