package com.cleevio.cinemax.api.common.service.model

import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.ExportLanguage
import com.cleevio.cinemax.api.common.util.createContentHttpHeaders
import org.apache.poi.ss.usermodel.BorderStyle
import org.apache.poi.xssf.usermodel.XSSFCellStyle
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import java.io.InputStream
import java.time.LocalDate

data class ListExportModel<T>(
    val header: Map<ExportLanguage, ExportHeaderModel>,
    val columnNames: Map<ExportLanguage, List<String>>,
    val columnData: List<T>,
) : ExportModel

data class ExportMainHeaderModel(
    val branch: String,
    val dateLabel: String,
    val branchLabel: String,
    val userLabel: String,
    val username: String,
    val exportTitle: String,
    val exportSubtitleFrom: String?,
    val exportSubtitleTo: String?,
    val timeLabel: String,
    val exportDateFrom: LocalDate?,
    val exportDateTo: LocalDate?,
    val dataColumnsCount: Int,
) : ExportHeaderModel

data class ExportResultModel(
    val inputStream: InputStream,
    val size: Long,
)

data class TableCell(
    val value: Any,
    val cellIndex: Int,
    val cellWidth: Int = 1,
    val styleAdjustment: XSSFCellStyle.() -> Unit = {},
)

data class BorderStyles(
    val top: BorderStyle? = null,
    val bottom: BorderStyle? = null,
    val left: BorderStyle? = null,
    val right: BorderStyle? = null,
) {

    constructor(borderStyle: BorderStyle) : this(
        top = borderStyle,
        bottom = borderStyle,
        left = borderStyle,
        right = borderStyle
    )
}

fun ExportResultModel.toStreamingResponseEntity(
    exportTitle: String,
    exportFormat: ExportFormat,
): ResponseEntity<StreamingResponseBody> =
    ResponseEntity(
        StreamingResponseBody { outputStream ->
            this.inputStream.use { inputStream -> inputStream.copyTo(outputStream) }
        },
        createContentHttpHeaders(size = this.size, exportTitle = exportTitle, format = exportFormat),
        HttpStatus.OK
    )
