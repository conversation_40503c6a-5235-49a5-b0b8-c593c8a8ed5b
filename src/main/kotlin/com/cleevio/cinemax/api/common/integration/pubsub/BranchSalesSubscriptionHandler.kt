package com.cleevio.cinemax.api.common.integration.pubsub

import com.cleevio.cinemax.api.common.config.PubSubProperties
import com.cleevio.cinemax.api.common.integration.pubsub.constant.Subscription
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Configuration

@Configuration
@ConditionalOnProperty(
    "cinemax.configuration.deployment-type",
    havingValue = "HEADQUARTERS"
)
class BranchSalesSubscriptionHandler(
    pubSubProperties: PubSubProperties,
    private val branchSalesDispatcherService: BranchSalesDispatcherService,
) : AbstractSubscriptionHandler(
    subscriptionName = pubSubProperties.getSubscriptionName(Subscription.BRANCH_SALES),
    dispatcherFunction = branchSalesDispatcherService::dispatch
)
