package com.cleevio.cinemax.api.common.util

import com.cleevio.cinemax.api.common.service.query.WithPageable
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl

inline fun <T, U : WithPageable> search(
    query: U,
    countFinder: (U) -> Int?,
    rowsFinder: (U) -> List<T>,
): Page<T> {
    val count = countFinder(query)
    if (count == null || count == 0) {
        return PageImpl(emptyList(), query.pageable, 0)
    }
    val rows = rowsFinder(query)
    return PageImpl(rows, query.pageable, count.toLong())
}
