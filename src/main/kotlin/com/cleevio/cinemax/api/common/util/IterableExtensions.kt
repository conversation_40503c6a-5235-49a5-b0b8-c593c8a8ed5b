package com.cleevio.cinemax.api.common.util

inline fun <T, K : Any, V : Any> Iterable<T>.associateNotNull(transform: (T) -> Pair<K?, V?>): Map<K, V> {
    val destination = mutableMapOf<K, V>()
    for (element in this) {
        val (key, value) = transform(element)
        if (key != null && value != null) {
            destination[key] = value
        }
    }
    return destination
}

inline fun <K, V> Iterable<K>.associateWithNotNull(
    valueTransform: (K) -> V?,
): Map<K, V> = buildMap {
    for (key in this@associateWithNotNull) {
        val value = valueTransform(key) ?: continue
        this[key] = value
    }
}

inline fun <T, K : Any> Iterable<T>.associateByNotNull(
    keySelector: (T) -> K?,
): Map<K, T> {
    val destination = mutableMapOf<K, T>()
    for (element in this) {
        keySelector(element)?.let {
            destination[it] = element
        }
    }
    return destination
}

inline fun <T, C : Collection<T>> C.ifNotEmpty(block: C.() -> Unit): C {
    if (this.isNotEmpty()) {
        block(this)
    }
    return this
}

fun <T, R> Iterable<T>.mapToSet(transform: (T) -> R): Set<R> = mapTo(mutableSetOf(), transform)
fun <T, R : Any> Iterable<T>.mapNotNullToSet(transform: (T) -> R?): Set<R> = mapNotNullTo(mutableSetOf(), transform)
