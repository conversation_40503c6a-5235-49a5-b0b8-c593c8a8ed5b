package com.cleevio.cinemax.api.common.util

import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort

/**
 * Extends Pageable and makes it possible to specify nulls handling directly in its PageRequest implementation.
 */
fun Pageable.toPageRequest(nullsFirst: <PERSON>olean = false): PageRequest {
    val nullsHandling = if (nullsFirst) Sort.NullHandling.NULLS_FIRST else Sort.NullHandling.NULLS_LAST
    val sortOrders = this.sort.map { Sort.Order(it.direction, it.property, nullsHandling) }.toList()
    return PageRequest.of(
        this.pageNumber,
        this.pageSize,
        Sort.by(sortOrders)
    )
}
