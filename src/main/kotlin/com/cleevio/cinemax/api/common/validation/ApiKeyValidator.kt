package com.cleevio.cinemax.api.common.validation

import org.springframework.security.authentication.AuthenticationServiceException

class ApiKeyValidator(private val supportedApiKey: String) {

    fun validate(apiKey: String) {
        if (apiKey != supportedApiKey) {
            throw AuthenticationServiceException("Error has occurred during authentication via Cinamex API key.")
        }
    }
}
