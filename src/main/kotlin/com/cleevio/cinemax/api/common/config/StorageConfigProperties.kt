package com.cleevio.cinemax.api.common.config

import jakarta.validation.constraints.NotBlank
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.validation.annotation.Validated

@Validated
@ConfigurationProperties(prefix = "storage")
data class StorageConfigProperties(

    @field:NotBlank
    val type: String,

    @field:NotBlank
    val localPath: String,

    @field:NotBlank
    val localUrl: String,

    @field:NotBlank
    val persistentVolumeMountPath: String,
)
