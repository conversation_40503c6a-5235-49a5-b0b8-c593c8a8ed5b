package com.cleevio.cinemax.api.common.validation

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import org.springframework.stereotype.Component
import kotlin.reflect.KClass

@Target(AnnotationTarget.FIELD, AnnotationTarget.PROPERTY_GETTER, AnnotationTarget.VALUE_PARAMETER)
@Retention(AnnotationRetention.RUNTIME)
@Constraint(validatedBy = [ValidTaxRateValidator::class])
annotation class ValidTaxRate(
    val message: String = "Invalid tax rate provided.",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = [],
)

@Component
class ValidTaxRateValidator(
    private val cinemaxConfigProperties: CinemaxConfigProperties,
) : ConstraintValidator<ValidTaxRate, Int?> {

    override fun isValid(value: Int?, context: ConstraintValidatorContext): Boolean {
        if (value == null) return true

        return if (cinemaxConfigProperties.taxRates.containsValue(value)) {
            true
        } else {
            context.disableDefaultConstraintViolation()
            context.buildConstraintViolationWithTemplate("Tax rate $value is not a valid tax rate.")
                .addConstraintViolation()
            false
        }
    }
}
