package com.cleevio.cinemax.api.common.integration.web

import com.cleevio.cinemax.api.common.integration.AbstractConnector
import com.cleevio.cinemax.api.common.integration.web.dto.PublishScreeningOnlineRequest
import com.cleevio.cinemax.api.common.integration.web.dto.PublishScreeningsOnlineResponse
import com.cleevio.cinemax.api.common.integration.web.dto.UnpublishScreeningOnlineRequest
import com.cleevio.cinemax.api.common.integration.web.dto.UnpublishScreeningsOnlineResponse
import com.cleevio.cinemax.api.common.util.MAPPER
import org.springframework.http.MediaType
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.web.client.RestClient

abstract class AbstractCinemaxWebConnector(
    username: String,
    password: String,
    baseUrl: String,
) : AbstractConnector(
    baseUrl = baseUrl,
    restClientBuilder = {
        it.setCinemaxWebHeaders(
            username = username,
            password = password
        )
    },
    enableRequestLogging = true
) {

    fun publishScreeningsOnline(
        request: List<PublishScreeningOnlineRequest>,
    ): PublishScreeningsOnlineResponse {
        return restClient.post()
            .uri("/api/programApi")
            .body(request)
            .retrieveResponseWithErrorHandler()
    }

    fun unpublishScreeningsOnline(
        request: List<UnpublishScreeningOnlineRequest>,
    ): UnpublishScreeningsOnlineResponse {
        return restClient.post()
            .uri("/api/programApi/delete")
            .body(request)
            .retrieveResponseWithErrorHandler()
    }
}

private fun RestClient.Builder.setCinemaxWebHeaders(username: String, password: String) {
    this.defaultHeaders { headers ->
        headers.setBasicAuth(username, password)
        headers.contentType = MediaType.APPLICATION_JSON
        headers.accept = listOf(MediaType.APPLICATION_JSON)
    }.messageConverters { it.add(MappingJackson2HttpMessageConverter(MAPPER)) }
}
