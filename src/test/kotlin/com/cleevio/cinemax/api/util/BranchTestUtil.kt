package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.module.branch.constant.BranchType
import com.cleevio.cinemax.api.module.branch.entity.Branch
import com.cleevio.cinemax.api.module.branch.service.command.CreateOrUpdateBranchCommand
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

fun createBranch(
    originalId: Int = 1,
    productSalesCode: Int = 1,
    code: String = "510000",
    auditoriumOriginalCodePrefix: String = "5000",
    name: String = "Dunajska Streda",
    type: BranchType = BranchType.REGULAR,
) = Branch(
    originalId = originalId,
    productSalesCode = productSalesCode,
    code = code,
    auditoriumOriginalCodePrefix = auditoriumOriginalCodePrefix,
    name = name,
    type = type
)

fun mapToCreateOrUpdateBranchCommand(branch: Branch) = CreateOrUpdateBranchCommand(
    id = branch.id,
    originalId = branch.originalId,
    productSalesCode = branch.productSalesCode,
    code = branch.code,
    auditoriumOriginalCodePrefix = branch.auditoriumOriginalCodePrefix,
    name = branch.name
)

fun assertBranchEquals(expected: Branch, actual: Branch) {
    assertEquals(expected.originalId, actual.originalId)
    assertEquals(expected.productSalesCode, actual.productSalesCode)
    assertEquals(expected.code, actual.code)
    assertEquals(expected.name, actual.name)
    assertNotNull(actual.createdAt)
    assertNotNull(actual.updatedAt)
    assert(actual.createdBy.isNotBlank())
    assert(actual.updatedBy.isNotBlank())
}
