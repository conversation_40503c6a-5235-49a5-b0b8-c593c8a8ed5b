package com.cleevio.cinemax.api.util

import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.controller.dto.BasketResponse
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basket.service.command.CreateProductSalesBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitBasketCommand
import com.cleevio.cinemax.api.module.basketitem.controller.dto.BasketItemResponse
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateBasketItemRequest
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.entity.Screening
import org.junit.jupiter.api.Assertions.assertTrue
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNull

fun mapToInitBasketCommand(items: List<CreateBasketItemRequest> = listOf()) = InitBasketCommand(
    items = items
)

fun createBasket(
    id: UUID = UUID.randomUUID(),
    tableId: UUID? = null,
    totalPrice: BigDecimal = BigDecimal.TEN,
    state: BasketState,
    paymentType: PaymentType? = null,
    paymentPosConfigurationId: UUID? = null,
    paidAt: LocalDateTime? = null,
    variableSymbol: String? = null,
    entityModifier: (Basket) -> Unit = {},
) = Basket(
    id = id,
    tableId = tableId,
    totalPrice = totalPrice,
    state = state,
    paymentType = paymentType,
    paymentPosConfigurationId = paymentPosConfigurationId,
    paidAt = paidAt,
    variableSymbol = variableSymbol
).also(entityModifier)

fun createScreeningWithCurrentDateTime(
    originalId: Int = 1,
    auditoriumId: UUID,
    auditoriumLayoutId: UUID = UUID.randomUUID(),
    priceCategoryId: UUID = UUID.randomUUID(),
    movieId: UUID,
    proCommission: Int = 3,
    filmFondCommission: Int = 6,
    distributorCommission: Int = 70,
    publishOnline: Boolean = true,
    state: ScreeningState = ScreeningState.PUBLISHED,
) = Screening(
    id = UUID.randomUUID(),
    originalId = originalId,
    auditoriumId = auditoriumId,
    auditoriumLayoutId = auditoriumLayoutId,
    priceCategoryId = priceCategoryId,
    movieId = movieId,
    date = LocalDate.now(),
    time = LocalTime.now(),
    saleTimeLimit = 30,
    stopped = false,
    cancelled = false,
    proCommission = proCommission,
    filmFondCommission = filmFondCommission,
    distributorCommission = distributorCommission,
    publishOnline = publishOnline,
    state = state
)

fun createScreeningWithCustomDateTimeLimit(
    originalId: Int,
    auditoriumId: UUID,
    auditoriumLayoutId: UUID = UUID.randomUUID(),
    priceCategoryId: UUID = UUID.randomUUID(),
    movieId: UUID,
    date: LocalDate,
    time: LocalTime,
    saleTimeLimit: Int,
    proCommission: Int = 3,
    filmFondCommission: Int = 6,
    distributorCommission: Int = 70,
    publishOnline: Boolean = true,
    state: ScreeningState = ScreeningState.PUBLISHED,
) = Screening(
    id = UUID.randomUUID(),
    originalId = originalId,
    auditoriumId = auditoriumId,
    auditoriumLayoutId = auditoriumLayoutId,
    priceCategoryId = priceCategoryId,
    movieId = movieId,
    date = date,
    time = time,
    saleTimeLimit = saleTimeLimit,
    stopped = false,
    cancelled = false,
    proCommission = proCommission,
    filmFondCommission = filmFondCommission,
    distributorCommission = distributorCommission,
    publishOnline = publishOnline,
    state = state
)

fun assertBasketEquals(expected: Basket, actual: BasketResponse) {
    assertEquals(expected.id, actual.id)
    assertEquals(expected.createdAt, actual.createdAt)
    assertEquals(expected.tableId, actual.tableId)
    assertEquals(expected.state, actual.state)
    assertTrue(expected.totalPrice isEqualTo actual.totalPrice)
}

fun assertEqualsInitOrUpdateProductSalesBasketCommandToBasket(
    command: CreateProductSalesBasketCommand,
    expectedPrice: BigDecimal,
    basket: Basket,
) {
    assertNull(basket.tableId)
    assertTrue(basket.totalPrice isEqualTo expectedPrice)
    assertEquals(BasketState.PAID, basket.state)
    assertEquals(command.paymentType, basket.paymentType)
    assertNull(basket.preferredPaymentType)
    assertNull(basket.paymentPosConfigurationId)
    assertEquals(command.paidAt.truncatedToSeconds(), basket.paidAt!!.truncatedToSeconds())
    assertNull(basket.variableSymbol)
}

fun mapToBasketResponse(basket: Basket, itemResponses: List<BasketItemResponse>) = BasketResponse(
    id = basket.id,
    createdAt = basket.createdAt,
    tableId = basket.tableId,
    state = basket.state,
    totalPrice = basket.totalPrice,
    items = itemResponses
)
