package com.cleevio.cinemax.api.module.product.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.NO_TAX_RATE
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.toCents
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.event.ProductCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.product.service.query.AdminExportProductMarginRatesFilter
import com.cleevio.cinemax.api.module.product.service.query.AdminExportProductMarginRatesQuery
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.entity.ProductComposition
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateProductCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class AdminExportProductMarginRatesQueryServiceIT @Autowired constructor(
    private val underTest: AdminExportProductMarginRatesQueryService,
    private val productService: ProductService,
    private val productComponentService: ProductComponentService,
    private val productCategoryService: ProductCategoryService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val basketItemRepository: BasketItemRepository,
    private val basketRepository: BasketRepository,
    private val posConfigurationRepository: PosConfigurationRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        every { applicationEventPublisherMock.publishEvent(any<ProductCreatedOrUpdatedEvent>()) } just Runs
    }

    @Test
    fun `test AdminExportProductMarginRatesQuery - no products found - should return empty list`() {
        val result = underTest(
            AdminExportProductMarginRatesQuery(
                filter = AdminExportProductMarginRatesFilter(),
                exportFormat = ExportFormat.XLSX,
                username = "testUser"
            )
        )

        assertEquals(0, result.size)
    }

    @Test
    fun `test AdminExportProductMarginRatesQuery - no filter - should return all products with correct values`() {
        initData()

        val result = underTest(
            AdminExportProductMarginRatesQuery(
                filter = AdminExportProductMarginRatesFilter(),
                exportFormat = ExportFormat.XLSX,
                username = "testUser"
            )
        )

        val resultMap = result.associateBy { it.title }
        assertEquals(5, resultMap.size)

        // Assertions for PRODUCT_1
        val product1 = resultMap[PRODUCT_1.title]
        assertNotNull(product1)
        assertEquals(PRODUCT_1.title, product1.title)
        assertEquals(1, product1.totalCount)
        assertTrue(4.00.toBigDecimal() isEqualTo product1.costs)
        assertTrue(23.00.toBigDecimal() isEqualTo product1.cashSales)
        assertTrue(0.00.toBigDecimal() isEqualTo product1.cashlessSales)
        assertTrue(23.00.toBigDecimal() isEqualTo product1.grossRevenue)
        assertEquals(23, product1.vatPercentage)
        assertTrue(4.30.toBigDecimal() isEqualTo product1.vatAmount.toCents())
        assertTrue(18.70.toBigDecimal() isEqualTo product1.netRevenue.toCents())
        assertTrue(14.70.toBigDecimal() isEqualTo product1.profit.toCents())

        // Assertions for PRODUCT_3
        val product3 = resultMap[PRODUCT_3.title]
        assertNotNull(product3)
        assertEquals(PRODUCT_3.title, product3.title)
        assertEquals(2, product3.totalCount)
        assertTrue(13.00.toBigDecimal() isEqualTo product3.costs)
        assertTrue(0.00.toBigDecimal() isEqualTo product3.cashSales)
        assertTrue(50.00.toBigDecimal() isEqualTo product3.cashlessSales)
        assertTrue(50.00.toBigDecimal() isEqualTo product3.grossRevenue)
        assertEquals(19, product3.vatPercentage)
        assertTrue(7.98.toBigDecimal() isEqualTo product3.vatAmount.toCents())
        assertTrue(42.02.toBigDecimal() isEqualTo product3.netRevenue.toCents())
        assertTrue(29.02.toBigDecimal() isEqualTo product3.profit.toCents())

        // Assertions for PRODUCT_4
        val product4 = resultMap[PRODUCT_4.title]
        assertNotNull(product4)
        assertEquals(PRODUCT_4.title, product4.title)
        assertEquals(3, product4.totalCount)
        assertTrue(31.50.toBigDecimal() isEqualTo product4.costs)
        assertTrue(180.00.toBigDecimal() isEqualTo product4.cashSales)
        assertTrue(0.00.toBigDecimal() isEqualTo product4.cashlessSales)
        assertTrue(180.00.toBigDecimal() isEqualTo product4.grossRevenue)
        // TODO product in product VAT rate
        // assertEquals(5, product4.vatPercentage)
        // assertTrue(8.57.toBigDecimal() isEqualTo product4.vatAmount.toCents())
        // assertTrue(171.43.toBigDecimal() isEqualTo product4.netRevenue.toCents())
        // assertTrue(139.93.toBigDecimal() isEqualTo product4.profit.toCents())

        // Assertions for PRODUCT_5
        val product5 = resultMap[PRODUCT_5.title]
        assertNotNull(product5)
        assertEquals(PRODUCT_5.title, product5.title)
        assertEquals(1, product5.totalCount)
        assertTrue(11.50.toBigDecimal() isEqualTo product5.costs)
        assertTrue(0.00.toBigDecimal() isEqualTo product5.cashSales)
        assertTrue(70.00.toBigDecimal() isEqualTo product5.cashlessSales)
        assertTrue(70.00.toBigDecimal() isEqualTo product5.grossRevenue)
        // TODO product in product VAT rate
        // assertEquals(23, product5.vatPercentage)
        // assertTrue(13.09.toBigDecimal() isEqualTo product5.vatAmount.toCents())
        // assertTrue(56.91.toBigDecimal() isEqualTo product5.netRevenue.toCents())
        // assertTrue(45.41.toBigDecimal() isEqualTo product5.profit.toCents())

        // Assertions for PRODUCT_6
        val product6 = resultMap[PRODUCT_6.title]
        assertNotNull(product6)
        assertEquals(PRODUCT_6.title, product6.title)
        assertEquals(2, product6.totalCount)
        assertTrue(0.00.toBigDecimal() isEqualTo product6.costs)
        assertTrue((-5.50).toBigDecimal() isEqualTo product6.cashSales)
        assertTrue((-2.25).toBigDecimal() isEqualTo product6.cashlessSales)
        assertTrue((-7.75).toBigDecimal() isEqualTo product6.grossRevenue)
        assertEquals(0, product6.vatPercentage)
        assertTrue(0.00.toBigDecimal() isEqualTo product6.vatAmount)
        assertTrue((-7.75).toBigDecimal() isEqualTo product6.netRevenue)
        assertTrue((-7.75).toBigDecimal() isEqualTo product6.profit)
    }

    @ParameterizedTest
    @MethodSource("dateIntervalProvider")
    fun `test AdminExportProductMarginRatesQuery - date filter - should return values within date interval`(
        dateFrom: LocalDate?,
        dateTo: LocalDate?,
        titles: Set<String>,
    ) {
        initData()

        val result = underTest(
            AdminExportProductMarginRatesQuery(
                filter = AdminExportProductMarginRatesFilter(
                    dateFrom = dateFrom,
                    dateTo = dateTo
                ),
                exportFormat = ExportFormat.XLSX,
                username = "testUser"
            )
        )

        assertEquals(result.map { it.title }.toSet(), titles)
    }

    companion object {
        @JvmStatic
        fun dateIntervalProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    LocalDate.parse("2024-12-03"),
                    null,
                    setOf(PRODUCT_1.title, PRODUCT_4.title, PRODUCT_6.title)
                ),
                Arguments.of(
                    null,
                    LocalDate.parse("2024-12-03"),
                    setOf(PRODUCT_3.title, PRODUCT_5.title, PRODUCT_6.title)
                ),
                Arguments.of(
                    null,
                    null,
                    setOf(
                        PRODUCT_1.title,
                        PRODUCT_3.title,
                        PRODUCT_4.title,
                        PRODUCT_5.title,
                        PRODUCT_6.title
                    )
                ),
                Arguments.of(
                    LocalDate.parse("2024-12-01"),
                    LocalDate.parse("2024-12-05"),
                    setOf(PRODUCT_3.title, PRODUCT_5.title, PRODUCT_6.title)
                ),
                Arguments.of(
                    LocalDate.parse("2024-12-02"),
                    LocalDate.parse("2024-12-06"),
                    setOf(PRODUCT_1.title, PRODUCT_4.title, PRODUCT_6.title)
                )
            )
        }
    }

    private fun initData(): Map<UUID, UUID> {
        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(
                mapToCreateOrUpdateProductCategoryCommand(it)
            )
        }

        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(
            PRODUCT_COMPONENT_1,
            PRODUCT_COMPONENT_2,
            PRODUCT_COMPONENT_3,
            PRODUCT_COMPONENT_4,
            PRODUCT_COMPONENT_5
        ).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(
                mapToCreateOrUpdateProductComponentCommand(it)
            )
        }

        val product1Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_1,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_1)
            )
        )

        val product2Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_2,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_2)
            )
        )

        val product3Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_3,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_3, PRODUCT_COMPOSITION_4, PRODUCT_COMPOSITION_5)
            )
        )

        val product4Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_4,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_6(product1Id), PRODUCT_COMPOSITION_7(product3Id))
            )
        )

        val product5Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_5,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_8(product2Id), PRODUCT_COMPOSITION_9(product3Id))
            )
        )

        val product6Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_6,
                productCategoryId = PRODUCT_CATEGORY_2.id,
                productCompositions = emptyList()
            )
        )

        posConfigurationRepository.save(POS_CONFIGURATION)
        basketRepository.saveAll(
            listOf(
                BASKET_1,
                BASKET_2
            )
        )
        basketItemRepository.saveAll(
            listOf(
                BASKET_ITEM_PRODUCT_CASH(product1Id),
                BASKET_ITEM_PRODUCT_CASHLESS(product3Id),
                BASKET_ITEM_PRODUCT_IN_PRODUCT_CASH(product4Id),
                BASKET_ITEM_PRODUCT_IN_PRODUCT_CASHLESS(product5Id),
                BASKET_ITEM_PRODUCT_DISCOUNT_CASH(product6Id),
                BASKET_ITEM_PRODUCT_DISCOUNT_CASHLESS(product6Id),
                BASKET_ITEM_PRODUCT_CASH_DELETED(product5Id)
            )
        )

        return mapOf(
            PRODUCT_1.id to product1Id,
            PRODUCT_2.id to product2Id,
            PRODUCT_3.id to product3Id,
            PRODUCT_4.id to product4Id,
            PRODUCT_5.id to product5Id,
            PRODUCT_6.id to product6Id
        )
    }
}

private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()

private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "1",
    title = "Kategorie produktu",
    type = ProductCategoryType.PRODUCT,
    taxRate = STANDARD_TAX_RATE
)

private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "2",
    title = "Kategorie slev",
    type = ProductCategoryType.DISCOUNT,
    taxRate = NO_TAX_RATE
)

private val PRODUCT_1 = createProduct(
    originalId = 1,
    code = "1",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca Cola 0.33l",
    type = ProductType.PRODUCT
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    code = "2",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Sprite 0.33l",
    type = ProductType.PRODUCT
)
private val PRODUCT_3 = createProduct(
    originalId = 3,
    code = "3",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    type = ProductType.PRODUCT,
    taxRate = REDUCED_TAX_RATE
)
private val PRODUCT_4 = createProduct(
    originalId = 4,
    code = "4",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Combo Popcorn XXL + 3x Coca Cola 0,33l",
    type = ProductType.PRODUCT_IN_PRODUCT,
    taxRate = null
)
private val PRODUCT_5 = createProduct(
    originalId = 5,
    code = "5",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Combo Popcorn XXL + Sprite 0,33l",
    type = ProductType.PRODUCT_IN_PRODUCT,
    taxRate = null
)
private val PRODUCT_6 = createProduct(
    originalId = 6,
    code = "6",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Additional Sale",
    type = ProductType.ADDITIONAL_SALE,
    discountAmount = BigDecimal.valueOf(5)
)
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "1",
    title = "Coca Cola 0,33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = BigDecimal.valueOf(90),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 4.toBigDecimal()
)
private val PRODUCT_COMPONENT_2 = createProductComponent(
    originalId = 2,
    code = "2",
    title = "Sprite 0,33l",
    unit = ProductComponentUnit.KS,
    stockQuantity = BigDecimal.valueOf(5.0),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 5.toBigDecimal()
)
private val PRODUCT_COMPONENT_3 = createProductComponent(
    originalId = 3,
    code = "3",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(1000),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 3.toBigDecimal()
)
private val PRODUCT_COMPONENT_4 = createProductComponent(
    originalId = 4,
    code = "4",
    title = "Soľ",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(100),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 0.5.toBigDecimal()
)
private val PRODUCT_COMPONENT_5 = createProductComponent(
    originalId = 5,
    code = "5",
    title = "Tuk",
    unit = ProductComponentUnit.KG,
    stockQuantity = BigDecimal.valueOf(50),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id,
    purchasePrice = 1.toBigDecimal()
)

// Coca Cola 0.33l  - Coca Cola 0.33l (1x)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = BigDecimal.valueOf(1)
)

// Sprite 0.33l - Sprite 0.33l (1x)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_2.id,
    amount = BigDecimal.valueOf(1)
)

// Popcorn XXL - Kukurica (1x)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_3.id,
    amount = BigDecimal.valueOf(1),
    productInProductId = null
)

// Popcorn XXL - Soľ (3x)
private val PRODUCT_COMPOSITION_4 = createProductComposition(
    originalId = 4,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_4.id,
    amount = BigDecimal.valueOf(3)
)

// Popcorn XXL - Tuk (2x)
private val PRODUCT_COMPOSITION_5 = createProductComposition(
    originalId = 5,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_5.id,
    amount = BigDecimal.valueOf(2)
)

// Combo Popcorn XXL + MEGA Coca Cola 0,33l -> Coca Cola 0,33l (3x)
private val PRODUCT_COMPOSITION_6: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 6,
        productId = PRODUCT_4.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = BigDecimal.valueOf(1),
        productInProductPrice = BigDecimal.ONE
    )
}

// Combo Popcorn XXL + MEGA Cola 0,33l -> Popcorn XXL (1x)
private val PRODUCT_COMPOSITION_7: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 7,
        productId = PRODUCT_4.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = BigDecimal.valueOf(1),
        productInProductPrice = 2.toBigDecimal()
    )
}

// Combo Popcorn XXL + Sprite 0,33l -> Sprite 0,33l (1x)
private val PRODUCT_COMPOSITION_8: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 8,
        productId = PRODUCT_5.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = BigDecimal.valueOf(1),
        productInProductPrice = 1.8.toBigDecimal()
    )
}

// Combo Popcorn XXL + Coca Cola 0,33l -> Popcorn XXL (1x)
private val PRODUCT_COMPOSITION_9: (UUID) -> ProductComposition = { productInProductId: UUID ->
    createProductComposition(
        originalId = 9,
        productId = PRODUCT_5.id,
        productInProductId = productInProductId,
        productComponentId = null,
        amount = BigDecimal.valueOf(1),
        productInProductPrice = 2.5.toBigDecimal()
    )
}

// POS CONFIGURATIONS
private val POS_CONFIGURATION = createPosConfiguration(macAddress = "AA:BB:CC:DD:EE")

// BASKETS
private val BASKET_1 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION.id,
    paidAt = LocalDateTime.of(2024, 12, 6, 0, 0)
)
private val BASKET_2 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASHLESS,
    paymentPosConfigurationId = POS_CONFIGURATION.id,
    paidAt = LocalDateTime.of(2024, 12, 1, 0, 0)
)

// BASKET ITEMS
private val BASKET_ITEM_PRODUCT_CASH: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        basketId = BASKET_1.id,
        type = BasketItemType.PRODUCT,
        price = 23.toBigDecimal(),
        productId = productId,
        quantity = 1
    )
}
private val BASKET_ITEM_PRODUCT_CASHLESS: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        basketId = BASKET_2.id,
        type = BasketItemType.PRODUCT,
        price = 25.toBigDecimal(),
        productId = productId,
        quantity = 2
    )
}
private val BASKET_ITEM_PRODUCT_IN_PRODUCT_CASH: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        basketId = BASKET_1.id,
        type = BasketItemType.PRODUCT,
        price = 60.toBigDecimal(),
        productId = productId,
        quantity = 3
    )
}
private val BASKET_ITEM_PRODUCT_IN_PRODUCT_CASHLESS: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        basketId = BASKET_2.id,
        type = BasketItemType.PRODUCT,
        price = 70.toBigDecimal(),
        productId = productId,
        quantity = 1
    )
}

private val BASKET_ITEM_PRODUCT_DISCOUNT_CASH: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        basketId = BASKET_1.id,
        type = BasketItemType.PRODUCT_DISCOUNT,
        price = (-5.5).toBigDecimal(),
        productId = productId,
        quantity = 1
    )
}
private val BASKET_ITEM_PRODUCT_DISCOUNT_CASHLESS: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        basketId = BASKET_2.id,
        type = BasketItemType.PRODUCT_DISCOUNT,
        price = (-2.25).toBigDecimal(),
        productId = productId,
        quantity = 1
    )
}
private val BASKET_ITEM_PRODUCT_CASH_DELETED: (UUID) -> BasketItem = { productId: UUID ->
    createBasketItem(
        basketId = BASKET_1.id,
        type = BasketItemType.PRODUCT,
        price = 100.toBigDecimal(),
        productId = productId,
        quantity = 100
    ) { it.markDeleted() }
}
