package com.cleevio.cinemax.api.module.basket.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.toCents
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.event.BasketPaidMessagingEvent
import com.cleevio.cinemax.api.module.basket.service.command.CompleteBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.GenerateBasketReceiptNumbersCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitiateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.RecalculateBasketTotalPriceCommand
import com.cleevio.cinemax.api.module.basket.service.command.ReportBasketToHeadquartersCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateBasketItemRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateProductRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateReservationRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketDiscountRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketPriceRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketRequest
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemJpaFinderService
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemService
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageService
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.UpdateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.posconfiguration.constant.TablesType
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.posconfiguration.service.command.CreateOrUpdatePosConfigurationCommand
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.module.ticket.service.TicketJpaFinderService
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountService
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createScreeningWithCurrentDateTime
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.mapToCreateBasketItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.util.UUID
import kotlin.test.assertEquals

class BasketMessagingServiceIT @Autowired constructor(
    private val underTest: BasketMessagingService,
    private val basketService: BasketService,
    private val basketItemService: BasketItemService,
    private val basketItemFinderService: BasketItemJpaFinderService,
    private val basketFinderService: BasketFinderService,
    private val basketReceiptNumbersService: BasketReceiptNumbersService,
    private val auditoriumService: AuditoriumService,
    private val movieService: MovieService,
    private val screeningService: ScreeningService,
    private val seatService: SeatService,
    private val screeningFeeService: ScreeningFeeService,
    private val priceCategoryService: PriceCategoryService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val productService: ProductService,
    private val productCategoryService: ProductCategoryService,
    private val productComponentService: ProductComponentService,
    private val productCompositionService: ProductCompositionService,
    private val posConfigurationService: PosConfigurationService,
    private val distributorService: DistributorService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val ticketDiscountService: TicketDiscountService,
    private val discountCardService: DiscountCardService,
    private val discountCardUsageService: DiscountCardUsageService,
    private val ticketJpaFinderService: TicketJpaFinderService,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1))
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        setOf(SEAT_1, SEAT_2).forEach {
            seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it))
        }
        priceCategoryItemService.createOrUpdatePriceCategoryItem(
            mapToCreateOrUpdatePriceCategoryItemCommand(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_1.id)
        )
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1))
        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(mapToCreateOrUpdateProductCategoryCommand(it))
        }
        setOf(PRODUCT_1, PRODUCT_2, PRODUCT_3).forEach {
            productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_1.id))
        }
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        productComponentService.syncCreateOrUpdateProductComponent(
            mapToCreateOrUpdateProductComponentCommand(PRODUCT_COMPONENT_1)
        )
        setOf(PRODUCT_COMPOSITION_1, PRODUCT_COMPOSITION_2, PRODUCT_COMPOSITION_3).forEach {
            productCompositionService.createOrUpdateProductComposition(mapToCreateOrUpdateProductCompositionCommand(it))
        }
        posConfigurationService.createOrUpdatePosConfiguration(POS_CONFIGURATION_COMMAND)

        every { reservationMssqlFinderServiceMock.findByOriginalScreeningIdAndOriginalSeatId(any(), any()) } returns null
        every { productReceiptNumberGeneratorMock.generateProductReceiptNumber() } returns
            PRODUCT_RECEIPT_NUMBER_1 andThen
            PRODUCT_RECEIPT_NUMBER_2 andThen
            PRODUCT_RECEIPT_NUMBER_3

        setOf(TICKET_DISCOUNT_1, TICKET_DISCOUNT_2).forEach {
            ticketDiscountService.syncCreateOrUpdateTicketDiscount(
                mapToCreateOrUpdateTicketDiscountCommand(it)
            )
        }
        discountCardService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1))

        every { applicationEventPublisherMock.publishEvent(any<BasketPaidMessagingEvent>()) } just Runs
    }

    @Test
    fun `test reportBasketToHeadquarters - basket with various items - should publish correct event`() {
        val basket = basketService.initBasket(
            InitBasketCommand(
                items = listOf(
                    CREATE_BASKET_ITEM_REQUEST_TICKET_1,
                    CREATE_BASKET_ITEM_REQUEST_TICKET_2,
                    CREATE_BASKET_ITEM_REQUEST_PRODUCT_1,
                    CREATE_BASKET_ITEM_REQUEST_PRODUCT_2
                )
            )
        )
        val discountCardUsage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID
            )
        )
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCardUsage.id,
                basketItemId = basketItemFinderService.findAllByBasketId(basket.id).minByOrNull { it.createdAt }!!.id
            )
        )
        basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(
                basketId = basket.id,
                request = CREATE_BASKET_ITEM_REQUEST_PRODUCT_DISCOUNT_1
            )
        )

        basketService.recalculateBasketTotalPrice(
            RecalculateBasketTotalPriceCommand(basketId = basket.id)
        )

        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = basket.id,
                posConfigurationId = POS_CONFIGURATION_ID,
                paymentType = PaymentType.CASH
            )
        )

        basketReceiptNumbersService.generateBasketReceiptNumbers(
            GenerateBasketReceiptNumbersCommand(basketId = basket.id)
        )

        basketService.completeBasketPayment(
            CompleteBasketPaymentCommand(basketId = basket.id)
        )

        underTest.reportBasketToHeadquarters(
            ReportBasketToHeadquartersCommand(basketId = basket.id)
        )

        val createdBasketItems = basketItemFinderService.findAllByBasketId(basket.id)
        val createdTickets = ticketJpaFinderService.findAll()

        assertEquals(5, createdBasketItems.size)
        assertEquals(2, createdTickets.size)

        val paidBasket = basketFinderService.getById(basket.id)
        verify {
            applicationEventPublisherMock.publishEvent(
                BasketPaidMessagingEvent(
                    id = paidBasket.id,
                    totalPrice = 27.toBigDecimal().toCents(),
                    state = BasketState.PAID,
                    paidAt = paidBasket.paidAt!!,
                    paymentType = PaymentType.CASH,
                    variableSymbol = null,
                    branchCode = "510640",
                    items = listOf(
                        BasketPaidMessagingEvent.BasketItem(
                            id = createdBasketItems[0].id,
                            type = BasketItemType.TICKET,
                            price = 6.toBigDecimal().toCents(),
                            quantity = 1,
                            isCancelled = false,
                            discountCardCode = "67900000",
                            ticket = BasketPaidMessagingEvent.BasketTicketItem(
                                id = createdTickets[0].id,
                                screeningId = SCREENING_1.id,
                                auditoriumOriginalCode = 101,
                                seatOriginalId = 1,
                                basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
                                ticketReceiptNumber = "1000000001",
                                ticketDiscountPrimaryCode = null,
                                ticketDiscountSecondaryCode = "01",
                                isGroupTicket = false,
                                isUsed = false,
                                includes3dGlasses = false
                            ),
                            product = null
                        ),
                        BasketPaidMessagingEvent.BasketItem(
                            id = createdBasketItems[1].id,
                            type = BasketItemType.TICKET,
                            price = 1.toBigDecimal(),
                            quantity = 1,
                            isCancelled = false,
                            discountCardCode = null,
                            ticket = BasketPaidMessagingEvent.BasketTicketItem(
                                id = createdTickets[1].id,
                                screeningId = SCREENING_1.id,
                                auditoriumOriginalCode = 101,
                                seatOriginalId = 2,
                                basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
                                ticketReceiptNumber = "1000000002",
                                ticketDiscountPrimaryCode = "02",
                                ticketDiscountSecondaryCode = null,
                                isGroupTicket = false,
                                isUsed = false,
                                includes3dGlasses = false
                            ),
                            product = null
                        ),
                        BasketPaidMessagingEvent.BasketItem(
                            id = createdBasketItems[2].id,
                            type = BasketItemType.PRODUCT,
                            price = 20.toBigDecimal(),
                            quantity = 2,
                            isCancelled = false,
                            ticket = null,
                            discountCardCode = null,
                            product = BasketPaidMessagingEvent.BasketProductItem(
                                productCode = "01",
                                productReceiptNumber = "1234567890"
                            )
                        ),
                        BasketPaidMessagingEvent.BasketItem(
                            id = createdBasketItems[3].id,
                            type = BasketItemType.PRODUCT,
                            price = 20.toBigDecimal(),
                            quantity = 2,
                            isCancelled = false,
                            ticket = null,
                            discountCardCode = null,
                            product = BasketPaidMessagingEvent.BasketProductItem(
                                productCode = "02",
                                productReceiptNumber = "1234567890"
                            )
                        ),
                        BasketPaidMessagingEvent.BasketItem(
                            id = createdBasketItems[4].id,
                            type = BasketItemType.PRODUCT_DISCOUNT,
                            price = (-20).toBigDecimal(),
                            quantity = 1,
                            isCancelled = false,
                            ticket = null,
                            discountCardCode = null,
                            product = BasketPaidMessagingEvent.BasketProductItem(
                                productCode = "03",
                                productReceiptNumber = "1234567890"
                            )
                        )
                    )
                )
            )
        }
    }
}

private const val PRODUCT_RECEIPT_NUMBER_1 = "1234567890"
private const val PRODUCT_RECEIPT_NUMBER_2 = "1234567891"
private const val PRODUCT_RECEIPT_NUMBER_3 = "1234567892"

private val POS_CONFIGURATION_ID = UUID.randomUUID()
private val DISTRIBUTOR_1 = createDistributor()
private val AUDITORIUM_1 = createAuditorium()
private val AUDITORIUM_LAYOUT = createAuditoriumLayout(auditoriumId = AUDITORIUM_1.id)
private val MOVIE_1 = createMovie(distributorId = DISTRIBUTOR_1.id)
private val PRICE_CATEGORY_1 = createPriceCategory()
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_1 = createScreeningWithCurrentDateTime(
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id,
    surchargeVip = 1.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    surchargeImax = 0.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 1.toBigDecimal(),
    serviceFeeImax = 0.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal()
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT.id
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT.id,
    type = SeatType.PREMIUM_PLUS
)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "A1",
    title = "Popcorn"
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "B3",
    title = "Zlavy",
    type = ProductCategoryType.DISCOUNT
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    code = "01",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    price = 10.toBigDecimal()
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    code = "02",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XL",
    price = 10.toBigDecimal()
)
private val PRODUCT_3 = createProduct(
    originalId = 3,
    code = "03",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "VIP karta -20%",
    type = ProductType.PRODUCT,
    price = 20.toBigDecimal()
)
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01",
    title = "Sleva 50%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 50,
    usageType = TicketDiscountUsageType.SECONDARY,
    freeCount = null
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "02",
    title = "Sleva 10%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 10,
    usageType = TicketDiscountUsageType.PRIMARY
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 123435,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    title = "VIP karta",
    code = "67900000"
)
private val CREATE_BASKET_ITEM_REQUEST_TICKET_1 = CreateBasketItemRequest(
    type = BasketItemType.TICKET,
    quantity = 1,
    ticket = CreateTicketRequest(
        ticketPrice = CreateTicketPriceRequest(
            priceCategoryItemNumber = PRICE_CATEGORY_ITEM_1.number
        ),
        reservation = CreateReservationRequest(
            seatId = SEAT_1.id
        ),
        screeningId = SCREENING_1.id,
        secondaryDiscount = CreateTicketDiscountRequest(
            ticketDiscountId = TICKET_DISCOUNT_1.id,
            discountCardId = DISCOUNT_CARD_1.id
        )
    )
)
private val CREATE_BASKET_ITEM_REQUEST_TICKET_2 = CREATE_BASKET_ITEM_REQUEST_TICKET_1.copy(
    ticket = CreateTicketRequest(
        ticketPrice = CreateTicketPriceRequest(
            priceCategoryItemNumber = PRICE_CATEGORY_ITEM_1.number
        ),
        reservation = CreateReservationRequest(
            seatId = SEAT_2.id
        ),
        screeningId = SCREENING_1.id,
        primaryDiscount = CreateTicketDiscountRequest(
            ticketDiscountId = TICKET_DISCOUNT_2.id
        )
    )
)
private val CREATE_BASKET_ITEM_REQUEST_PRODUCT_1 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 2,
    product = CreateProductRequest(
        productId = PRODUCT_1.id
    )
)
private val CREATE_BASKET_ITEM_REQUEST_PRODUCT_2 = CREATE_BASKET_ITEM_REQUEST_PRODUCT_1.copy(
    product = CreateProductRequest(
        productId = PRODUCT_2.id
    )
)
private val CREATE_BASKET_ITEM_REQUEST_PRODUCT_DISCOUNT_1 = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT_DISCOUNT,
    quantity = 1,
    product = CreateProductRequest(
        productId = PRODUCT_3.id,
        productIsolatedWithId = PRODUCT_2.id
    )
)
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = 180.0.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = 0.25.toBigDecimal()
)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = 0.20.toBigDecimal()
)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = 1.00.toBigDecimal()
)
private val POS_CONFIGURATION_COMMAND = CreateOrUpdatePosConfigurationCommand(
    id = POS_CONFIGURATION_ID,
    macAddress = "AA-BB-CC-DD-EE",
    title = "dummyPOS",
    receiptsDirectory = "/dummy/directory",
    ticketSalesEnabled = false,
    productModes = setOf(ProductMode.STANDARD),
    tablesType = TablesType.NO_TABLES,
    seatsEnabled = false
)
