package com.cleevio.cinemax.api.module.basket.service

import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basket.exception.BasketIsEmptyException
import com.cleevio.cinemax.api.module.basket.exception.BasketNotFoundException
import com.cleevio.cinemax.api.module.basket.exception.InvalidBasketStateException
import com.cleevio.cinemax.api.module.basket.service.command.CompleteBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.DeleteBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitiateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.UpdateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemJpaFinderService
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemService
import com.cleevio.cinemax.api.module.branch.service.BranchJpaFinderService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageFinderService
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationJpaFinderService
import com.cleevio.cinemax.api.module.reservation.service.ReservationService
import com.cleevio.cinemax.api.module.table.service.TableFinderService
import com.cleevio.cinemax.api.module.ticket.service.TicketJooqFinderService
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createScreeningWithCustomDateTimeLimit
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.context.ApplicationEventPublisher
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID
import java.util.stream.Stream

class BasketServiceTest {

    private val basketRepository = mockk<BasketRepository>()
    private val basketFinderRepository = mockk<BasketFinderRepository>()
    private val basketFinderService = mockk<BasketFinderService>()
    private val basketItemService = mockk<BasketItemService>()
    private val basketItemJpaFinderService = mockk<BasketItemJpaFinderService>()
    private val tableFinderService = mockk<TableFinderService>()
    private val reservationService = mockk<ReservationService>()
    private val ticketJooqFinderService = mockk<TicketJooqFinderService>()
    private val discountCardUsageFinderService = mockk<DiscountCardUsageFinderService>()
    private val branchJpaFinderService = mockk<BranchJpaFinderService>()
    private val posConfigurationJpaFinderService = mockk<PosConfigurationJpaFinderService>()
    private val applicationEventPublisher = mockk<ApplicationEventPublisher>()
    private val underTest = BasketService(
        basketRepository,
        basketFinderRepository,
        basketFinderService,
        basketItemService,
        basketItemJpaFinderService,
        tableFinderService,
        reservationService,
        ticketJooqFinderService,
        discountCardUsageFinderService,
        applicationEventPublisher,
        posConfigurationJpaFinderService,
        branchJpaFinderService
    )

    @Test
    fun `test deleteBasket - not existing basket - should throw exception`() {
        every { basketFinderService.getNonDeletedById(any()) } throws BasketNotFoundException()

        val command = DeleteBasketCommand(UUID.randomUUID())
        assertThrows<BasketNotFoundException> {
            underTest.deleteBasket(command)
        }
    }

    @ParameterizedTest
    @MethodSource("nonDeletableBasketsProvider")
    fun `test deleteBasket - invalid basket state - should throw exception`() {
        every { basketFinderService.getNonDeletedById(any()) } throws BasketNotFoundException()

        val command = DeleteBasketCommand(UUID.randomUUID())
        assertThrows<BasketNotFoundException> {
            underTest.deleteBasket(command)
        }
    }

    @Test
    fun `test initiateBasketPayment - not existing basket - should throw exception`() {
        every { basketFinderService.getNonDeletedById(any()) } throws BasketNotFoundException()

        assertThrows<BasketNotFoundException> {
            underTest.initiateBasketPayment(
                InitiateBasketPaymentCommand(
                    basketId = UUID.randomUUID(),
                    posConfigurationId = UUID.randomUUID(),
                    paymentType = PaymentType.CASH
                )
            )
        }
    }

    @Test
    fun `test initiateBasketPayment - basket is in invalid state - should throw exception`() {
        every { basketFinderService.getNonDeletedById(any()) } returns DELETED_BASKET

        assertThrows<InvalidBasketStateException> {
            underTest.initiateBasketPayment(
                InitiateBasketPaymentCommand(
                    basketId = UUID.randomUUID(),
                    posConfigurationId = UUID.randomUUID(),
                    paymentType = PaymentType.CASH
                )
            )
        }
    }

    @Test
    fun `test initiateBasketPayment - empty basket - should throw exception`() {
        every { basketFinderService.getNonDeletedById(any()) } returns OPEN_BASKET
        every { basketItemJpaFinderService.findAllNonDeletedByBasketId(any()) } returns listOf()

        assertThrows<BasketIsEmptyException> {
            underTest.initiateBasketPayment(
                InitiateBasketPaymentCommand(
                    basketId = OPEN_BASKET.id,
                    posConfigurationId = UUID.randomUUID(),
                    paymentType = PaymentType.CASH
                )
            )
        }
    }

    @ParameterizedTest
    @MethodSource("nonDeletableBasketsProvider")
    fun `test updateBasketPayment - invalid basket state - should throw exception`(
        basket: Basket,
    ) {
        every { basketFinderService.getNonDeletedById(any()) } returns basket
        every { basketItemJpaFinderService.findAllNonDeletedByBasketId(any()) } returns listOf()

        assertThrows<InvalidBasketStateException> {
            underTest.updateBasketPayment(
                UpdateBasketPaymentCommand(
                    basketId = basket.id,
                    paymentType = PaymentType.CASH
                )
            )
        }
    }

    @ParameterizedTest
    @MethodSource("otherThanPaymentInProgressBasketsProvider")
    fun `test completeBasketPayment - invalid basket state - should throw exception`(
        basket: Basket,
    ) {
        every { basketFinderService.getNonDeletedById(any()) } returns basket
        every { basketItemJpaFinderService.findAllNonDeletedByBasketId(any()) } returns listOf()

        assertThrows<InvalidBasketStateException> {
            underTest.completeBasketPayment(
                CompleteBasketPaymentCommand(
                    basketId = basket.id
                )
            )
        }
    }

    companion object {
        @JvmStatic
        fun otherThanPaymentInProgressBasketsProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(OPEN_BASKET),
                Arguments.of(PAID_BASKET),
                Arguments.of(DELETED_BASKET)
            )
        }

        @JvmStatic
        fun nonDeletableBasketsProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(PAID_BASKET),
                Arguments.of(DELETED_BASKET)
            )
        }
    }
}

private val DISTRIBUTOR_1_ID = UUID.randomUUID()
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1_ID
)
private val SCREENING_1 = createScreeningWithCustomDateTimeLimit(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    date = LocalDate.now().minusDays(1),
    time = LocalTime.now(),
    movieId = MOVIE_1.id,
    saleTimeLimit = 30
)
private val OPEN_BASKET = createBasket(
    tableId = SCREENING_1.id,
    state = BasketState.OPEN
)
private val PAID_BASKET = createBasket(
    tableId = SCREENING_1.id,
    state = BasketState.PAID
)
private val DELETED_BASKET = createBasket(
    tableId = SCREENING_1.id,
    state = BasketState.DELETED
)
