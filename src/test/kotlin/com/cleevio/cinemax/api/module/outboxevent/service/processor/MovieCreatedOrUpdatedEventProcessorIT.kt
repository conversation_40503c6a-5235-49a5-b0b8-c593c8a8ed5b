package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.genre.entity.Genre
import com.cleevio.cinemax.api.module.language.entity.Language
import com.cleevio.cinemax.api.module.movie.service.MovieMssqlFinderRepository
import com.cleevio.cinemax.api.module.movie.service.command.UpdateMovieOriginalIdCommand
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventState
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.production.entity.Production
import com.cleevio.cinemax.api.module.rating.entity.Rating
import com.cleevio.cinemax.api.module.screening.service.ScreeningMssqlFinderRepository
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.tmslanguage.entity.TmsLanguage
import com.cleevio.cinemax.api.util.assertMovieToMssqlMovieMapping
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.jooq.exception.DataException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_movie.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_movie.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class MovieCreatedOrUpdatedEventProcessorIT @Autowired constructor(
    private val underTest: MovieCreatedOrUpdatedEventProcessor,
    private val movieMssqlFinderRepository: MovieMssqlFinderRepository,
    private val screeningMssqlFinderRepository: ScreeningMssqlFinderRepository,
) : MssqlIntegrationTest() {

    @BeforeEach
    fun setUp() {
        every { productionJpaFinderServiceMock.findById(any()) } returns PRODUCTION_1
        every { genreJpaFinderServiceMock.findById(any()) } returnsMany listOf(GENRE_1, GENRE_2)
        every { ratingJpaFinderServiceMock.findById(any()) } returns RATING_1
        every { technologyJpaFinderServiceMock.findById(any()) } returns TECHNOLOGY_1
        every { languageJpaFinderServiceMock.findById(any()) } returns LANGUAGE_1
        every { tmsLanguageJpaFinderServiceMock.findById(any()) } returns TMS_LANGUAGE_1
        every { distributorJpaFinderServiceMock.getNonDeletedById(any()) } returns DISTRIBUTOR_1

        every { movieServiceMock.updateMovieOriginalId(any()) } just Runs
    }

    @Test
    fun `test process - should correctly process MovieCreatedOrUpdatedEvent and create new record`() {
        val movieToCreate = createMovie(
            originalId = null,
            distributorId = DISTRIBUTOR_1.id,
            productionId = PRODUCTION_1.id,
            primaryGenreId = GENRE_1.id,
            secondaryGenreId = GENRE_2.id,
            ratingId = RATING_1.id,
            technologyId = TECHNOLOGY_1.id,
            languageId = LANGUAGE_1.id,
            tmsLanguageId = TMS_LANGUAGE_1.id
        )
        every { movieJpaFinderServiceMock.findNonDeletedById(any()) } returns movieToCreate

        assertEquals(4, movieMssqlFinderRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = movieToCreate.id,
                type = OutboxEventType.MOVIE_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(5, movieMssqlFinderRepository.findAll().size)

        val createdMssqlMovie = movieMssqlFinderRepository.findAll().sortedBy { it.zcas }[4]
        assertNotNull(createdMssqlMovie)
        assertMovieToMssqlMovieMapping(
            expected = movieToCreate,
            expectedOriginalId = 5,
            actual = createdMssqlMovie,
            expectedDistributorOriginalCode = DISTRIBUTOR_1.code,
            expectedProductionCode = PRODUCTION_1.code,
            expectedPrimaryGenreCode = GENRE_1.code,
            expectedSecondaryGenreCode = GENRE_2.code,
            expectedRatingCode = RATING_1.code,
            expectedTmsLanguageCode = TMS_LANGUAGE_1.code
        )
        assertNotNull(createdMssqlMovie.zcas)

        verify {
            movieServiceMock.updateMovieOriginalId(
                UpdateMovieOriginalIdCommand(
                    movieId = movieToCreate.id,
                    originalId = 5
                )
            )
        }
    }

    @Test
    fun `test process - should correctly process MovieCreatedOrUpdatedEvent and update record with empty values`() {
        val mssqlMovie1 = movieMssqlFinderRepository.findAll()[0]
        val movieToUpdate = createMovie(
            originalId = mssqlMovie1.rfilmid,
            originalTitle = null,
            distributorId = DISTRIBUTOR_1.id,
            duration = null,
            technologyId = TECHNOLOGY_1.id,
            languageId = LANGUAGE_1.id
        )
        every { movieJpaFinderServiceMock.findNonDeletedById(any()) } returns movieToUpdate

        assertEquals(4, movieMssqlFinderRepository.findAll().size)

        val processResult = underTest.process(
            OutboxEvent(
                entityId = movieToUpdate.id,
                type = OutboxEventType.MOVIE_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )

        assertEquals(1, processResult)
        assertEquals(4, movieMssqlFinderRepository.findAll().size)

        val updatedMssqlMovie = movieMssqlFinderRepository.findByOriginalId(mssqlMovie1.rfilmid)
        assertNotNull(updatedMssqlMovie)
        assertMovieToMssqlMovieMapping(
            expected = movieToUpdate,
            expectedOriginalId = 1,
            actual = updatedMssqlMovie,
            expectedDistributorOriginalCode = DISTRIBUTOR_1.code
        )

        val relatedScreenings = screeningMssqlFinderRepository.findAll().filter { it.rfilmid == updatedMssqlMovie.rfilmid }
        assertTrue(relatedScreenings.size == 1)
        assertEquals(TECHNOLOGY_1.code, relatedScreenings[0].cfotrm.trim())
        assertEquals(LANGUAGE_1.code, relatedScreenings[0].jazyk.trim())
    }

    @Test
    fun `test process - should return processResult=0 if movie record in Postgres db does not exist`() {
        every { movieJpaFinderServiceMock.findNonDeletedById(any()) } returns null

        val processResult = underTest.process(
            OutboxEvent(
                entityId = UUID.fromString("ff702f61-1bfa-4ae7-83fc-d8a4f88463da"),
                type = OutboxEventType.MOVIE_CREATED_OR_UPDATED,
                state = OutboxEventState.PENDING,
                data = "{}"
            )
        )
        assertEquals(0, processResult)
    }

    @Test
    fun `test process - should throw if movie values are not valid within MSSQL db constraints`() {
        val movieToCreate = createMovie(
            originalId = null,
            distributorId = DISTRIBUTOR_1.id,
            code = "TOOLONGCODE"
        )
        every { movieJpaFinderServiceMock.findNonDeletedById(any()) } returns movieToCreate

        assertEquals(4, movieMssqlFinderRepository.findAll().size)

        assertThrows<DataException> {
            underTest.process(
                OutboxEvent(
                    entityId = movieToCreate.id,
                    type = OutboxEventType.MOVIE_CREATED_OR_UPDATED,
                    state = OutboxEventState.PENDING,
                    data = "{}"
                )
            )
        }
    }
}

private val DISTRIBUTOR_1 = createDistributor()
private val PRODUCTION_1 = Production(
    originalId = 150,
    code = "ZZ",
    title = "USA"
)
private val GENRE_1 = Genre(
    originalId = 121,
    code = "A",
    title = "Dráma"
)
private val GENRE_2 = Genre(
    originalId = 122,
    code = "B",
    title = "Sci-Fi"
)
private val RATING_1 = Rating(
    originalId = 123,
    code = "CC",
    title = "13"
)
private val TECHNOLOGY_1 = Technology(
    originalId = 124,
    code = "DD",
    title = "IMAX"
)
private val LANGUAGE_1 = Language(
    originalId = 125,
    code = "EE",
    title = "česká verze"
)
private val TMS_LANGUAGE_1 = TmsLanguage(
    originalId = 126,
    code = "F",
    title = "český dabing"
)
