package com.cleevio.cinemax.api.module.productcategory.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.file.service.FileRepository
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.event.ProductCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.product.service.command.DeleteProductCommand
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.service.query.GetVipProductCategoriesByTableIdQuery
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.entity.ProductComposition
import com.cleevio.cinemax.api.module.table.constant.TableType
import com.cleevio.cinemax.api.module.table.constant.VipTableStatus
import com.cleevio.cinemax.api.module.table.service.TableRepository
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createFile
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createTable
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateProductCommand
import com.cleevio.cinemax.api.util.toUUID
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.UUID

class GetVipProductCategoriesByTableIdQueryServiceIT @Autowired constructor(
    private val underTest: GetVipProductCategoriesByTableIdQueryService,
    private val tableRepository: TableRepository,
    private val basketRepository: BasketRepository,
    private val productService: ProductService,
    private val productCategoryService: ProductCategoryService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val productComponentService: ProductComponentService,
    private val fileRepository: FileRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        initData()
    }

    @Test
    fun `test getVipProductCategoriesByTableId - should return VIP product categories with VIP products`() {
        // When
        val result = underTest.invoke(GetVipProductCategoriesByTableIdQuery(1.toUUID()))

        // Then
        result.basketState shouldBe BasketState.OPEN
        result.table.ipAddress shouldBe "*************"
        result.table.status shouldBe VipTableStatus.OK

        val categories = result.categories
        categories shouldHaveSize 2
        categories[0].id shouldBe PRODUCT_CATEGORY_1.id
        categories[0].title shouldBe PRODUCT_CATEGORY_1.title
        categories[0].products shouldHaveSize 2
        categories[0].products.run {
            this[0].title shouldBe PRODUCT_3.title
            this[0].price shouldBe PRODUCT_3.price
            this[0].imageFileUrl shouldBe "https://example.com/manager-app/files/product_image/${FILE_3.id}.jpg"
            this[0].tabletOrder shouldBe PRODUCT_3.tabletOrder
            this[1].title shouldBe PRODUCT_5.title
            this[1].price shouldBe PRODUCT_5.price
            this[1].imageFileUrl shouldBe null
            this[1].tabletOrder shouldBe PRODUCT_5.tabletOrder
        }

        categories[1].id shouldBe PRODUCT_CATEGORY_2.id
        categories[1].title shouldBe PRODUCT_CATEGORY_2.title
        categories[1].products shouldHaveSize 1
        categories[1].products.run {
            this[0].title shouldBe PRODUCT_2.title
            this[0].price shouldBe PRODUCT_2.price
            this[0].imageFileUrl shouldBe "https://example.com/manager-app/files/product_image/${FILE_2.id}.jpg"
            this[0].tabletOrder shouldBe PRODUCT_2.tabletOrder
        }
    }

    @Test
    fun `test getVipProductCategoriesByTableId - with non-existent table should return default values`() {
        // When
        val result = underTest.invoke(GetVipProductCategoriesByTableIdQuery(UUID.randomUUID()))

        // Then
        result.basketState shouldBe null
        result.table.ipAddress shouldBe null
        result.table.status shouldBe VipTableStatus.NOT_CONNECTED
    }

    private fun initData() {
        every { applicationEventPublisherMock.publishEvent(any<ProductCreatedOrUpdatedEvent>()) } just Runs

        tableRepository.save(VIP_TABLE)
        basketRepository.save(BASKET)

        fileRepository.saveAll(setOf(FILE_1, FILE_2, FILE_3))

        setOf(PRODUCT_CATEGORY_1, PRODUCT_CATEGORY_2).forEach {
            productCategoryService.syncCreateOrUpdateProductCategory(
                mapToCreateOrUpdateProductCategoryCommand(it)
            )
        }

        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        setOf(
            PRODUCT_COMPONENT_1,
            PRODUCT_COMPONENT_2,
            PRODUCT_COMPONENT_3,
            PRODUCT_COMPONENT_4,
            PRODUCT_COMPONENT_5,
            PRODUCT_COMPONENT_6
        ).forEach {
            productComponentService.syncCreateOrUpdateProductComponent(
                mapToCreateOrUpdateProductComponentCommand(it)
            )
        }

        val product1Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_1,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_1)
            )
        )

        val product2Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_2,
                productCategoryId = PRODUCT_CATEGORY_2.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_2)
            )
        )

        val product3Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_3,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_3, PRODUCT_COMPOSITION_4, PRODUCT_COMPOSITION_5)
            )
        )

        val product5Id = productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_5,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_8(product2Id), PRODUCT_COMPOSITION_9(product3Id))
            )
        )

        // just to be sure deleted product won't appear anywhere
        productService.adminCreateProduct(
            mapToCreateProductCommand(
                product = PRODUCT_7_DELETED,
                productCategoryId = PRODUCT_CATEGORY_1.id,
                productCompositions = listOf(PRODUCT_COMPOSITION_10)
            )
        ).also {
            productService.deleteProduct(DeleteProductCommand(it))
        }
    }

    private val VIP_TABLE = createTable(
        id = 1.toUUID(),
        originalId = 1,
        title = "VIP Table 1",
        type = TableType.SEAT,
        productMode = ProductMode.VIP,
        ipAddress = "*************"
    )

    private val BASKET = createBasket(
        tableId = 1.toUUID(),
        totalPrice = BigDecimal.valueOf(5.99),
        state = BasketState.OPEN
    )

    private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()

    private val PRODUCT_CATEGORY_1 = createProductCategory(
        originalId = 1,
        code = "1",
        title = "Kategorie A",
        type = ProductCategoryType.PRODUCT,
        taxRate = STANDARD_TAX_RATE
    )

    private val PRODUCT_CATEGORY_2 = createProductCategory(
        originalId = 2,
        code = "2",
        title = "Kategorie B",
        type = ProductCategoryType.DISCOUNT,
        taxRate = STANDARD_TAX_RATE
    )

    private val FILE_1 = createFile(id = 1.toUUID(), originalName = "${1.toUUID()}.jpg", originalId = 1)
    private val FILE_2 = createFile(id = 2.toUUID(), originalName = "${2.toUUID()}.jpg", originalId = 2)
    private val FILE_3 = createFile(id = 3.toUUID(), originalName = "${3.toUUID()}.jpg", originalId = 3)

    private val PRODUCT_1 = createProduct(
        id = 1.toUUID(),
        originalId = 1,
        code = "11",
        productCategoryId = PRODUCT_CATEGORY_1.id,
        title = "Coca Cola 0.33l",
        type = ProductType.PRODUCT,
        tabletOrder = 1,
        price = 10.toBigDecimal(),
        imageFileId = FILE_1.id,
        active = true,
        soldInVip = false
    )

    private val PRODUCT_2 = createProduct(
        id = 2.toUUID(),
        originalId = 2,
        code = "2",
        productCategoryId = PRODUCT_CATEGORY_2.id,
        title = "Sprite 0.33l",
        type = ProductType.PRODUCT,
        active = true,
        tabletOrder = 2,
        price = 30.toBigDecimal(),
        discountPercentage = 20,
        imageFileId = FILE_2.id,
        soldInVip = true
    )

    private val PRODUCT_3 = createProduct(
        id = 3.toUUID(),
        originalId = 3,
        code = "3",
        productCategoryId = PRODUCT_CATEGORY_1.id,
        title = "Popcorn XXL",
        type = ProductType.PRODUCT,
        tabletOrder = 3,
        active = true,
        price = 50.toBigDecimal(),
        imageFileId = FILE_3.id,
        soldInVip = true
    )

    private val PRODUCT_5 = createProduct(
        id = 5.toUUID(),
        originalId = 5,
        code = "5",
        productCategoryId = PRODUCT_CATEGORY_1.id,
        title = "Combo Popcorn XXL + Sprite 0,33l",
        type = ProductType.PRODUCT_IN_PRODUCT,
        active = true,
        tabletOrder = 5,
        price = 100.toBigDecimal(),
        soldInVip = true
    )

    private val PRODUCT_7_DELETED = createProduct(
        id = 7.toUUID(),
        originalId = 7,
        code = "7",
        productCategoryId = PRODUCT_CATEGORY_1.id,
        title = "Coca Cola 0.5l",
        type = ProductType.PRODUCT,
        price = 55.toBigDecimal(),
        discountAmount = 40.toBigDecimal()
    ).also { it.markDeleted() }

    private val PRODUCT_COMPONENT_1 = createProductComponent(
        originalId = 1,
        code = "1",
        title = "Coca Cola 0,33l",
        unit = ProductComponentUnit.KS,
        stockQuantity = BigDecimal.valueOf(90),
        productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
    )
    private val PRODUCT_COMPONENT_2 = createProductComponent(
        originalId = 2,
        code = "2",
        title = "Sprite 0,33l",
        unit = ProductComponentUnit.KS,
        stockQuantity = BigDecimal.valueOf(5.0),
        productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
    )
    private val PRODUCT_COMPONENT_3 = createProductComponent(
        originalId = 3,
        code = "3",
        title = "Kukurica",
        unit = ProductComponentUnit.KG,
        stockQuantity = BigDecimal.valueOf(1000),
        productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
    )
    private val PRODUCT_COMPONENT_4 = createProductComponent(
        originalId = 4,
        code = "4",
        title = "Soľ",
        unit = ProductComponentUnit.KG,
        stockQuantity = BigDecimal.valueOf(100),
        productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
    )
    private val PRODUCT_COMPONENT_5 = createProductComponent(
        originalId = 5,
        code = "5",
        title = "Tuk",
        unit = ProductComponentUnit.KG,
        stockQuantity = BigDecimal.valueOf(50),
        productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
    )
    private val PRODUCT_COMPONENT_6 = createProductComponent(
        originalId = 6,
        code = "6",
        title = "Coca Cola 0,5l",
        unit = ProductComponentUnit.KS,
        stockQuantity = BigDecimal.valueOf(30),
        productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
    )

    // Coca Cola 0.33l  - Coca Cola 0.33l (1x)
    private val PRODUCT_COMPOSITION_1 = createProductComposition(
        originalId = 1,
        productId = PRODUCT_1.id,
        productComponentId = PRODUCT_COMPONENT_1.id,
        amount = BigDecimal.valueOf(1)
    )

    // Sprite 0.33l - Sprite 0.33l (1x)
    private val PRODUCT_COMPOSITION_2 = createProductComposition(
        originalId = 2,
        productId = PRODUCT_2.id,
        productComponentId = PRODUCT_COMPONENT_2.id,
        amount = BigDecimal.valueOf(1)
    )

    // Popcorn XXL - Kukurica (1x)
    private val PRODUCT_COMPOSITION_3 = createProductComposition(
        originalId = 3,
        productId = PRODUCT_3.id,
        productComponentId = PRODUCT_COMPONENT_3.id,
        amount = BigDecimal.valueOf(1),
        productInProductId = null
    )

    // Popcorn XXL - Soľ (3x)
    private val PRODUCT_COMPOSITION_4 = createProductComposition(
        originalId = 4,
        productId = PRODUCT_3.id,
        productComponentId = PRODUCT_COMPONENT_4.id,
        amount = BigDecimal.valueOf(3)
    )

    // Popcorn XXL - Tuk (2x)
    private val PRODUCT_COMPOSITION_5 = createProductComposition(
        originalId = 5,
        productId = PRODUCT_3.id,
        productComponentId = PRODUCT_COMPONENT_5.id,
        amount = BigDecimal.valueOf(2)
    )

    // Combo Popcorn XXL + Sprite 0,33l -> Sprite 0,33l (1x)
    private val PRODUCT_COMPOSITION_8: (UUID) -> ProductComposition = { productInProductId: UUID ->
        createProductComposition(
            originalId = 8,
            productId = PRODUCT_5.id,
            productInProductId = productInProductId,
            productComponentId = null,
            amount = BigDecimal.valueOf(1),
            productInProductPrice = 2.toBigDecimal()
        )
    }

    // Combo Popcorn XXL + Coca Cola 0,33l -> Popcorn XXL (1x)
    private val PRODUCT_COMPOSITION_9: (UUID) -> ProductComposition = { productInProductId: UUID ->
        createProductComposition(
            originalId = 9,
            productId = PRODUCT_5.id,
            productInProductId = productInProductId,
            productComponentId = null,
            amount = BigDecimal.valueOf(1),
            productInProductPrice = 2.toBigDecimal()
        )
    }

    // Coca Cola 0.5l  - Coca Cola 0.5l (1x)
    private val PRODUCT_COMPOSITION_10 = createProductComposition(
        originalId = 10,
        productId = PRODUCT_7_DELETED.id,
        productComponentId = PRODUCT_COMPONENT_6.id,
        amount = BigDecimal.valueOf(1)
    )
}
