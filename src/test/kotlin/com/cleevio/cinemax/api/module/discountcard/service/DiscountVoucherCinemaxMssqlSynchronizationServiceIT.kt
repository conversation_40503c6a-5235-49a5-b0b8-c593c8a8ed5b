package com.cleevio.cinemax.api.module.discountcard.service

import com.cleevio.cinemax.api.MssqlIntegrationTest
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.service.command.CreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.util.assertCommandEquals
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createTicketDiscount
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.context.jdbc.SqlConfig
import org.springframework.test.context.jdbc.SqlGroup
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue

@SqlGroup(
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/init_mssql_discount_voucher_cinemax.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-cinemax/test/drop_mssql_discount_voucher_cinemax.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlCinemaxDataSource",
            transactionManager = "mssqlCinemaxTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/init_mssql_discount_voucher_buffet.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        )
    ),
    Sql(
        scripts = [
            "/db/mssql-buffet/test/drop_mssql_discount_voucher_buffet.sql"
        ],
        config = SqlConfig(
            dataSource = "mssqlBuffetDataSource",
            transactionManager = "mssqlBuffetTransactionManager"
        ),
        executionPhase = Sql.ExecutionPhase.AFTER_TEST_METHOD
    )
)
class DiscountVoucherCinemaxMssqlSynchronizationServiceIT @Autowired constructor(
    private val underTest: DiscountVoucherCinemaxMssqlSynchronizationService,
) : MssqlIntegrationTest() {

    @Test
    fun `test synchronize all - no PSQL discount voucher, 5 MSSQL discount vouchers - should create 3 discount vouchers`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns null
        every { discountCardJooqFinderServiceMock.findByCode(any()) } returns null
        every { ticketDiscountJooqFinderServiceMock.findNonDeletedByCode(any()) } returnsMany listOf(
            TICKET_DISCOUNT_1,
            TICKET_DISCOUNT_2,
            TICKET_DISCOUNT_3
        )
        every { productJooqFinderServiceMock.findNonDeletedByOriginalCode(PRODUCT_1.code) } returns PRODUCT_1
        every {
            productJooqFinderServiceMock.findNonDeletedByOriginalCode(PRODUCT_DISCOUNT_1.code)
        } returns PRODUCT_DISCOUNT_1
        every { discountCardServiceMock.createOrUpdateDiscountCard(any()) } just Runs
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val discountCardCodeCaptor = mutableListOf<String>()
        val ticketDiscountCodeCaptor = mutableListOf<String>()
        val productCodeCaptor = mutableListOf<String>()
        val commandCaptor = mutableListOf<CreateOrUpdateDiscountCardCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.DISCOUNT_VOUCHER) }
        verify { discountCardJooqFinderServiceMock.findByCode(capture(discountCardCodeCaptor)) }
        verify { ticketDiscountJooqFinderServiceMock.findNonDeletedByCode(capture(ticketDiscountCodeCaptor)) }
        verify { productJooqFinderServiceMock.findNonDeletedByOriginalCode(capture(productCodeCaptor)) }
        verify { discountCardServiceMock.createOrUpdateDiscountCard(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.DISCOUNT_VOUCHER,
                    lastSynchronization = DISCOUNT_VOUCHER_3_UPDATED_AT
                )
            )
        }

        assertTrue(discountCardCodeCaptor.size == 4)
        assertEquals(
            discountCardCodeCaptor,
            listOf(EXPECTED_COMMAND_1.code, EXPECTED_COMMAND_2.code, EXPECTED_COMMAND_3.code, "5103670506")
        )

        assertTrue(ticketDiscountCodeCaptor.size == 3)
        assertEquals(ticketDiscountCodeCaptor, listOf(TICKET_DISCOUNT_1.code, TICKET_DISCOUNT_2.code, TICKET_DISCOUNT_3.code))

        assertTrue(productCodeCaptor.size == 2)
        assertEquals(productCodeCaptor, listOf(PRODUCT_DISCOUNT_1.code, PRODUCT_1.code))

        assertTrue(commandCaptor.size == 3)
        assertCommandEquals(EXPECTED_COMMAND_1, commandCaptor[0])
        assertCommandEquals(EXPECTED_COMMAND_2, commandCaptor[1])
        assertCommandEquals(EXPECTED_COMMAND_3, commandCaptor[2])
    }

    @Test
    fun `test synchronize all - 2 PSQL discount vouchers, 5 MSSQL discount vouchers - should create 1 discount voucher`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns DISCOUNT_VOUCHER_2_UPDATED_AT
        every { discountCardServiceMock.createOrUpdateDiscountCard(any()) } just Runs
        every { discountCardJooqFinderServiceMock.findByCode(any()) } returns null
        every { ticketDiscountJooqFinderServiceMock.findNonDeletedByCode(any()) } returnsMany listOf(TICKET_DISCOUNT_3)
        every { productJooqFinderServiceMock.findNonDeletedByOriginalCode(PRODUCT_1.code) } returns PRODUCT_1
        every {
            productJooqFinderServiceMock.findNonDeletedByOriginalCode(PRODUCT_DISCOUNT_1.code)
        } returns PRODUCT_DISCOUNT_1
        every { synchronizationFromMssqlServiceMock.createOrUpdate(any()) } returns SYNCHRONIZATION_FROM_MSSQL

        underTest.synchronizeAll()

        val discountCardCodeCaptor = mutableListOf<String>()
        val ticketDiscountCodeCaptor = mutableListOf<String>()
        val productCodeCaptor = mutableListOf<String>()
        val commandCaptor = mutableListOf<CreateOrUpdateDiscountCardCommand>()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.DISCOUNT_VOUCHER) }
        verify { discountCardJooqFinderServiceMock.findByCode(capture(discountCardCodeCaptor)) }
        verify { ticketDiscountJooqFinderServiceMock.findNonDeletedByCode(capture(ticketDiscountCodeCaptor)) }
        verify { productJooqFinderServiceMock.findNonDeletedByOriginalCode(capture(productCodeCaptor)) }
        verify { discountCardServiceMock.createOrUpdateDiscountCard(capture(commandCaptor)) }
        verify {
            synchronizationFromMssqlServiceMock.createOrUpdate(
                CreateOrUpdateSynchronizationFromMssqlCommand(
                    type = SynchronizationFromMssqlType.DISCOUNT_VOUCHER,
                    lastSynchronization = DISCOUNT_VOUCHER_3_UPDATED_AT
                )
            )
        }

        assertTrue(discountCardCodeCaptor.size == 2)
        assertEquals(discountCardCodeCaptor, listOf(EXPECTED_COMMAND_3.code, "5103670506"))

        assertTrue(ticketDiscountCodeCaptor.size == 1)
        assertEquals(ticketDiscountCodeCaptor, listOf(TICKET_DISCOUNT_3.code))

        assertTrue(productCodeCaptor.size == 2)
        assertEquals(productCodeCaptor, listOf(PRODUCT_DISCOUNT_1.code, PRODUCT_1.code))

        assertTrue(commandCaptor.size == 1)
        assertCommandEquals(EXPECTED_COMMAND_3, commandCaptor[0])
    }

    @Test
    fun `test synchronize all - discount card with identical code exists - should create no discount voucher`() {
        every { synchronizationFromMssqlFinderServiceMock.findByType(any()) } returns DISCOUNT_VOUCHER_2_UPDATED_AT
        every { discountCardJooqFinderServiceMock.findByCode(any()) } returns DISCOUNT_CARD

        underTest.synchronizeAll()

        verify { synchronizationFromMssqlFinderServiceMock.findByType(SynchronizationFromMssqlType.DISCOUNT_VOUCHER) }
        verify { discountCardJooqFinderServiceMock.findByCode(DISCOUNT_CARD.code) }
        verify { productJooqFinderServiceMock wasNot Called }
        verify { discountCardServiceMock wasNot Called }
        verify { synchronizationFromMssqlServiceMock wasNot Called }
    }
}

private val DISCOUNT_VOUCHER_2_UPDATED_AT = LocalDateTime.of(2023, 5, 2, 11, 0, 0)
private val DISCOUNT_VOUCHER_3_UPDATED_AT = LocalDateTime.of(2023, 5, 10, 15, 28, 0)

private val TICKET_DISCOUNT_1 = createTicketDiscount(
    code = "O2",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 50
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    code = "K2",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 25
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    code = "00",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 10
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    productCategoryId = UUID.randomUUID(),
    code = "01052",
    title = "Popcorn Klasik 2.4l"
)
private val PRODUCT_DISCOUNT_1 = createProduct(
    originalId = 2,
    code = "00026",
    productCategoryId = UUID.randomUUID(),
    title = "Produkt zdarma",
    type = ProductType.PRODUCT,
    price = 0L.toBigDecimal(),
    discountPercentage = 100
)
private val EXPECTED_COMMAND_1 = CreateOrUpdateDiscountCardCommand(
    originalId = -1,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    productDiscountId = null,
    productId = null,
    type = DiscountCardType.VOUCHER,
    title = "O2",
    code = "6092217854",
    validFrom = LocalDate.of(2023, 3, 10),
    validUntil = LocalDate.of(2034, 12, 31),
    applicableToBasket = 2,
    applicableToScreening = 2,
    applicableToScreeningsPerDay = null,
    productsCount = null
)
private val EXPECTED_COMMAND_2 = CreateOrUpdateDiscountCardCommand(
    originalId = -2,
    ticketDiscountId = TICKET_DISCOUNT_2.id,
    productDiscountId = null,
    productId = null,
    type = DiscountCardType.VOUCHER,
    title = "IQOS",
    code = "6128016208",
    validFrom = LocalDate.of(2023, 5, 2),
    validUntil = LocalDate.of(2034, 12, 31),
    applicableToBasket = 2,
    applicableToScreening = 2,
    applicableToScreeningsPerDay = null,
    productsCount = null
)
private val EXPECTED_COMMAND_3 = CreateOrUpdateDiscountCardCommand(
    originalId = -3,
    ticketDiscountId = TICKET_DISCOUNT_3.id,
    productDiscountId = PRODUCT_DISCOUNT_1.id,
    productId = PRODUCT_1.id,
    type = DiscountCardType.VOUCHER,
    title = "Akce1",
    code = "6209016364",
    validFrom = LocalDate.of(2023, 5, 10),
    validUntil = LocalDate.of(2033, 12, 31),
    applicableToBasket = 1000,
    applicableToScreening = 1000,
    applicableToScreeningsPerDay = null,
    productsCount = null
)
private val SYNCHRONIZATION_FROM_MSSQL = SynchronizationFromMssql(
    type = SynchronizationFromMssqlType.DISCOUNT_VOUCHER,
    lastSynchronization = LocalDateTime.MIN,
    lastHeartbeat = null
)
private val DISCOUNT_CARD = createDiscountCard(code = EXPECTED_COMMAND_3.code)
