package com.cleevio.cinemax.api.module.basket.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationDataTestHelper
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.constant.WEB_SALE_USERNAME
import com.cleevio.cinemax.api.common.util.isEqualTo
import com.cleevio.cinemax.api.common.util.truncatedToSeconds
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basket.event.MessagingBasketItemWithDiscountCardCreatedEvent
import com.cleevio.cinemax.api.module.basket.event.TableBasketDeletedEvent
import com.cleevio.cinemax.api.module.basket.service.command.CompleteBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.CreateProductSalesBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.CreateTicketSalesBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.DeleteBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitBasketForTableCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitOrUpdateMssqlBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitiateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.MessagingCreateBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.UpdateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.WebCompleteBasketPaymentCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateBasketItemRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateProductRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateReservationRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketDiscountRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketPriceRequest
import com.cleevio.cinemax.api.module.basketitem.controller.dto.CreateTicketRequest
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemJpaFinderService
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemService
import com.cleevio.cinemax.api.module.basketitem.service.command.PatchBasketItemCommand
import com.cleevio.cinemax.api.module.branch.service.BranchService
import com.cleevio.cinemax.api.module.discountcard.event.DiscountCardsNotAppliedEvent
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageFinderService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageService
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.UpdateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.groupreservation.constant.GroupReservationType
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.outboxevent.event.WebBasketPaymentCompletedEvent
import com.cleevio.cinemax.api.module.posconfiguration.constant.PosConfigurationType
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.posconfiguration.constant.TablesType
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.module.product.service.ProductJpaFinderService
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.event.ReservationCreatedEvent
import com.cleevio.cinemax.api.module.reservation.service.ReservationJooqFinderService
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.module.table.event.BasketPaidEvent
import com.cleevio.cinemax.api.module.table.service.TableService
import com.cleevio.cinemax.api.module.ticket.service.TicketJooqFinderService
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountService
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.AuditoriumSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceJooqFinderService
import com.cleevio.cinemax.api.util.TEST_PRINCIPAL_USERNAME
import com.cleevio.cinemax.api.util.assertEqualsInitOrUpdateProductSalesBasketCommandToBasket
import com.cleevio.cinemax.api.util.assertEqualsInitOrUpdateProductSalesBasketCommandToBasketItem
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBranch
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createScreeningWithCurrentDateTime
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTable
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.mapToCreateBasketItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateBranchCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePosConfigurationCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateTableCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.util.mapToInitBasketCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.Optional
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class BasketServiceIT @Autowired constructor(
    private val underTest: BasketService,
    private val basketRepository: BasketRepository,
    private val basketFinderRepository: BasketFinderRepository,
    private val basketFinderService: BasketFinderService,
    private val basketItemService: BasketItemService,
    private val basketItemFinderService: BasketItemJpaFinderService,
    private val basketItemRepository: BasketItemRepository,
    private val auditoriumService: AuditoriumService,
    private val movieService: MovieService,
    private val screeningService: ScreeningService,
    private val seatService: SeatService,
    private val reservationJooqFinderService: ReservationJooqFinderService,
    private val reservationRepository: ReservationRepository,
    private val screeningFeeService: ScreeningFeeService,
    private val priceCategoryService: PriceCategoryService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val productService: ProductService,
    private val productCategoryService: ProductCategoryService,
    private val productFinderService: ProductJpaFinderService,
    private val ticketPriceJooqFinderService: TicketPriceJooqFinderService,
    private val productComponentService: ProductComponentService,
    private val productCompositionService: ProductCompositionService,
    private val posConfigurationService: PosConfigurationService,
    private val ticketDiscountService: TicketDiscountService,
    private val ticketJooqFinderService: TicketJooqFinderService,
    private val discountCardService: DiscountCardService,
    private val discountCardUsageService: DiscountCardUsageService,
    private val discountCardUsageFinderService: DiscountCardUsageFinderService,
    private val tableService: TableService,
    private val distributorService: DistributorService,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
    private val branchService: BranchService,
    private val integrationDataTestHelper: IntegrationDataTestHelper,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(AUDITORIUM_1))
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT_1)
        distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(DISTRIBUTOR_1))
        movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(MOVIE_1))
        priceCategoryService.syncCreateOrUpdatePriceCategory(mapToSyncCreateOrUpdatePriceCategoryCommand(PRICE_CATEGORY_1))
        setOf(SCREENING_1, SCREENING_2, SCREENING_3, SCREENING_4).forEach {
            screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(it))
        }
        setOf(SEAT_1, SEAT_2, SEAT_3).forEach {
            seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it))
        }
        setOf(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_ITEM_2, PRICE_CATEGORY_ITEM_3, PRICE_CATEGORY_ITEM_4).forEach {
            priceCategoryItemService.createOrUpdatePriceCategoryItem(
                mapToCreateOrUpdatePriceCategoryItemCommand(it, PRICE_CATEGORY_1.id)
            )
        }
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1))
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_4))
        productCategoryService.syncCreateOrUpdateProductCategory(mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_1))
        setOf(PRODUCT_1, PRODUCT_2, PRODUCT_3).forEach {
            productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(it, PRODUCT_CATEGORY_1.id))
        }
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        productComponentService.syncCreateOrUpdateProductComponent(
            mapToCreateOrUpdateProductComponentCommand(PRODUCT_COMPONENT_1)
        )
        setOf(PRODUCT_COMPOSITION_1, PRODUCT_COMPOSITION_2, PRODUCT_COMPOSITION_3).forEach {
            productCompositionService.createOrUpdateProductComposition(
                mapToCreateOrUpdateProductCompositionCommand(it)
            )
        }
        setOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2, POS_CONFIGURATION_3).forEach {
            posConfigurationService.createOrUpdatePosConfiguration(
                mapToCreateOrUpdatePosConfigurationCommand(it)
            )
        }
        setOf(BRANCH_1, BRANCH_2).forEach {
            branchService.syncCreateOrUpdateBranch(mapToCreateOrUpdateBranchCommand(it))
        }

        every { reservationMssqlFinderServiceMock.findByOriginalScreeningIdAndOriginalSeatId(any(), any()) } returns null
        every { applicationEventPublisherMock.publishEvent(any<DiscountCardsNotAppliedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ReservationCreatedEvent>()) } just Runs
    }

    @Test
    fun `test initBasket - should create basket`() {
        val items = prepareCreateBasketItemRequests()
        val createdBasket = createBasket(items)
        val ticketPrice = ticketPriceJooqFinderService.findAll().first { it.seatId == SEAT_1.id }

        assertBasketEquals(BASKET_1, createdBasket)
        val expectedPrice =
            ticketPrice.totalPrice.plus(PRODUCT_1.price.times(CREATE_BASKET_ITEM_REQUEST_PRODUCT.quantity.toBigDecimal()))
        assertTrue(expectedPrice isEqualTo createdBasket.totalPrice)
    }

    @Test
    fun `test initBasket - screening in the future with sale time earlier than now - should create basket`() {
        val items = prepareCreateBasketItemRequests(SCREENING_4.id)
        val createdBasket = createBasket(items)
        val ticketPrice = ticketPriceJooqFinderService.findAllByScreeningIdIn(setOf(SCREENING_4.id))
            .first { it.seatId == SEAT_1.id }

        assertBasketEquals(BASKET_3, createdBasket)
        val expectedPrice =
            ticketPrice.totalPrice.plus(PRODUCT_1.price.times(CREATE_BASKET_ITEM_REQUEST_PRODUCT.quantity.toBigDecimal()))
        assertTrue(expectedPrice isEqualTo createdBasket.totalPrice)
    }

    @Test
    fun `test initBasket - command with empty items list - should create empty basket`() {
        val items = emptyList<CreateBasketItemRequest>()
        val command = mapToInitBasketCommand(items = items)

        val createdBasket = underTest.initBasket(command)
        assertBasketEquals(BASKET_2_EMPTY, createdBasket)
        assertEquals(0, basketItemRepository.findAll().size)
    }

    @Test
    fun `test deleteBasket - should delete basket and corresponding items`() {
        val items = prepareCreateBasketItemRequests()
        val createdBasket = createBasket(items)

        assertEquals(1, reservationJooqFinderService.findAllNonDeleted().size)
        assertEquals(1, ticketJooqFinderService.findAllNonDeletedByBasketId(createdBasket.id).size)

        underTest.deleteBasket(DeleteBasketCommand(createdBasket.id))

        assertNull(basketFinderRepository.findNonDeletedById(createdBasket.id))
        val deletedBasket = basketRepository.findById(createdBasket.id).get()
        assertEquals(BasketState.DELETED, deletedBasket.state)
        assertNotNull(deletedBasket.deletedAt)
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedBasket.updatedBy)

        val basketItems = basketItemRepository.findAll()
        assertEquals(items.size, basketItems.size)
        assertEquals(0, basketItemFinderService.findAllNonDeletedByBasketId(deletedBasket.id).size)

        assertEquals(1, reservationJooqFinderService.findAll().size)
        assertEquals(0, reservationJooqFinderService.findAllNonDeleted().size)

        assertEquals(1, ticketJooqFinderService.findAll().size)
        assertEquals(0, ticketJooqFinderService.findAllNonDeletedByBasketId(createdBasket.id).size)
    }

    @Test
    fun `test deleteBasket on table - should delete basket and corresponding items`() {
        tableService.createOrUpdateTable(mapToCreateOrUpdateTableCommand(TABLE_1))

        val created = underTest.initBasketForTable(
            InitBasketForTableCommand(
                tableId = TABLE_1.id,
                items = listOf(
                    CREATE_BASKET_ITEM_REQUEST_PRODUCT.copy(
                        product = CreateProductRequest(
                            productId = PRODUCT_2.id
                        )
                    ),
                    CREATE_BASKET_ITEM_REQUEST_PRODUCT.copy(
                        product = CreateProductRequest(
                            productId = PRODUCT_3.id
                        )
                    )
                ),
                initiatedInNewPos = false
            )
        )
        val createdBasket = basketFinderRepository.findNonDeletedById(created.id)

        assertNotNull(createdBasket)
        assertEquals(2, basketItemFinderService.findAllNonDeletedByBasketId(createdBasket.id).size)

        assertEquals(0, reservationJooqFinderService.findAllNonDeleted().size)
        assertEquals(0, ticketJooqFinderService.findAllNonDeletedByBasketId(createdBasket.id).size)

        underTest.deleteBasket(DeleteBasketCommand(createdBasket.id))

        assertNull(basketFinderRepository.findNonDeletedById(createdBasket.id))
        val deletedBasket = basketRepository.findById(createdBasket.id).get()
        assertEquals(BasketState.DELETED, deletedBasket.state)
        assertNotNull(deletedBasket.deletedAt)
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedBasket.updatedBy)

        val basketItems = basketItemRepository.findAll()
        assertEquals(2, basketItems.size)
        assertEquals(0, basketItemFinderService.findAllNonDeletedByBasketId(deletedBasket.id).size)

        verify {
            applicationEventPublisherMock.publishEvent(
                TableBasketDeletedEvent(
                    basketId = createdBasket.id
                )
            )
        }
    }

    @Test
    fun `test deleteBasket - should delete basket, corresponding items and related discount card usage`() {
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1))
        setOf(DISCOUNT_CARD_1, DISCOUNT_CARD_2).forEach {
            discountCardService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(it))
        }

        val createdBasket = createBasket(listOf(CREATE_BASKET_ITEM_REQUEST_PRODUCT))

        // scan discount card
        val discountCard1Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = createdBasket.id,
                posConfigurationId = POS_CONFIGURATION_1.id
            )
        )
        assertNull(discountCard1Usage.deletedAt)

        // create ticket with applied discount card discount
        val createBasketItemRequestTicket = CreateBasketItemRequest(
            type = BasketItemType.TICKET,
            quantity = 1,
            ticket = CreateTicketRequest(
                ticketPrice = CreateTicketPriceRequest(
                    priceCategoryItemNumber = PRICE_CATEGORY_ITEM_1.number
                ),
                reservation = CreateReservationRequest(
                    seatId = SEAT_1.id
                ),
                screeningId = SCREENING_1.id,
                primaryDiscount = CreateTicketDiscountRequest(
                    ticketDiscountId = TICKET_DISCOUNT_1.id
                )
            )
        )
        basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(
                request = createBasketItemRequestTicket,
                basketId = createdBasket.id
            )
        )
        assertEquals(1, reservationJooqFinderService.findAllNonDeleted().size)
        assertEquals(1, ticketJooqFinderService.findAllNonDeletedByBasketId(createdBasket.id).size)

        // scan discount second card but do not apply any discount
        val discountCard2Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_2.id,
                basketId = createdBasket.id,
                posConfigurationId = POS_CONFIGURATION_1.id
            )
        )
        assertNull(discountCard2Usage.deletedAt)

        underTest.deleteBasket(DeleteBasketCommand(createdBasket.id))

        assertNull(basketFinderRepository.findNonDeletedById(createdBasket.id))
        val deletedBasket = basketRepository.findById(createdBasket.id).get()
        assertEquals(BasketState.DELETED, deletedBasket.state)
        assertNotNull(deletedBasket.deletedAt)
        assertEquals(TEST_PRINCIPAL_USERNAME, deletedBasket.updatedBy)

        val basketItems = basketItemRepository.findAll()
        assertEquals(2, basketItems.size)
        assertEquals(0, basketItemFinderService.findAllNonDeletedByBasketId(deletedBasket.id).size)

        assertEquals(1, reservationJooqFinderService.findAll().size)
        assertEquals(0, reservationJooqFinderService.findAllNonDeleted().size)

        assertEquals(1, ticketJooqFinderService.findAll().size)
        assertEquals(0, ticketJooqFinderService.findAllNonDeletedByBasketId(createdBasket.id).size)

        assertEquals(2, discountCardUsageFinderService.findAll().size)
        verify {
            applicationEventPublisherMock.publishEvent(
                DiscountCardsNotAppliedEvent(
                    discountCardUsageIds = setOf(discountCard1Usage.id, discountCard2Usage.id)
                )
            )
        }
    }

    @Test
    fun `test initiateBasketPayment - should pay basket, update corresponding reservation and delete trailing card usage`() {
        ticketDiscountService.syncCreateOrUpdateTicketDiscount(mapToCreateOrUpdateTicketDiscountCommand(TICKET_DISCOUNT_1))
        setOf(DISCOUNT_CARD_1, DISCOUNT_CARD_2).forEach {
            discountCardService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(it))
        }

        val items = prepareCreateBasketItemRequests()
        val createdBasket = createBasket(items)
        val originalUpdatedAt = createdBasket.updatedAt

        assertEquals(1, reservationJooqFinderService.findAllNonDeleted().size)
        assertEquals(1, ticketJooqFinderService.findAllNonDeletedByBasketId(createdBasket.id).size)
        assertEquals(TEST_PRINCIPAL_USERNAME, createdBasket.updatedBy)

        val ticketBasketItem = basketItemRepository.findAll().sortedBy { it.createdAt }[0]

        // scan discount card
        val discountCard1Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_1.id,
                basketId = createdBasket.id,
                posConfigurationId = POS_CONFIGURATION_1.id
            )
        )
        assertNull(discountCard1Usage.deletedAt)

        // apply ticket discount from discount card
        basketItemService.patchBasketItem(
            PatchBasketItemCommand(
                basketId = createdBasket.id,
                basketItemId = ticketBasketItem.id,
                primaryTicketDiscountId = Optional.of(DISCOUNT_CARD_1.ticketDiscountId!!),
                primaryTicketDiscountCardId = DISCOUNT_CARD_1.id
            )
        )
        // update discount card usage explicitly
        discountCardUsageService.updateDiscountCardUsage(
            UpdateDiscountCardUsageCommand(
                discountCardUsageId = discountCard1Usage.id,
                basketItemId = ticketBasketItem.id
            )
        )

        // scan second discount card but do not apply any discount
        val discountCard2Usage = discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = DISCOUNT_CARD_2.id,
                basketId = createdBasket.id,
                posConfigurationId = POS_CONFIGURATION_1.id
            )
        )
        assertNull(discountCard2Usage.deletedAt)

        underTest.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = createdBasket.id,
                posConfigurationId = POS_CONFIGURATION_1.id,
                paymentType = PaymentType.CASHLESS
            )
        )

        val paymentInProgressBasket = basketRepository.findById(createdBasket.id).get()
        assertEquals(BasketState.PAYMENT_IN_PROGRESS, paymentInProgressBasket.state)
        assertEquals(PaymentType.CASHLESS, paymentInProgressBasket.paymentType)
        assertTrue(paymentInProgressBasket.updatedAt.isAfter(originalUpdatedAt))
        assertNull(paymentInProgressBasket.paidAt)
        assertEquals(TEST_PRINCIPAL_USERNAME, paymentInProgressBasket.updatedBy)
        assertNull(paymentInProgressBasket.deletedAt)

        assertEquals(1, reservationJooqFinderService.findAll().size)
        val reservation = reservationJooqFinderService.findAll()[0]
        assertEquals(ReservationState.UNAVAILABLE, reservation.state)
        assertEquals(TEST_PRINCIPAL_USERNAME, reservation.updatedBy)

        assertNull(discountCard1Usage.deletedAt)
        verify {
            applicationEventPublisherMock.publishEvent(
                DiscountCardsNotAppliedEvent(
                    discountCardUsageIds = setOf(discountCard2Usage.id)
                )
            )
        }
    }

    @Test
    fun `test updateBasketPayment - should update basket payment`() {
        val items = prepareCreateBasketItemRequests()
        val createdBasket = createBasket(items)

        val paymentInProgressBasket = underTest.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = createdBasket.id,
                posConfigurationId = POS_CONFIGURATION_1.id,
                paymentType = PaymentType.CASHLESS
            )
        )

        assertEquals(BasketState.PAYMENT_IN_PROGRESS, paymentInProgressBasket.state)
        assertEquals(PaymentType.CASHLESS, paymentInProgressBasket.paymentType)

        val updatedPaymentInProgressBasket = underTest.updateBasketPayment(
            UpdateBasketPaymentCommand(
                basketId = paymentInProgressBasket.id,
                paymentType = PaymentType.CASH
            )
        )

        assertEquals(BasketState.PAYMENT_IN_PROGRESS, updatedPaymentInProgressBasket.state)
        assertEquals(PaymentType.CASH, updatedPaymentInProgressBasket.paymentType)
        assertTrue(updatedPaymentInProgressBasket.updatedAt.isAfter(paymentInProgressBasket.updatedAt))
    }

    @Test
    fun `test completeBasketPayment - should complete basket payment`() {
        every { applicationEventPublisherMock.publishEvent(any<BasketPaidEvent>()) } just Runs

        val items = prepareCreateBasketItemRequests()
        val createdBasket = createBasket(items)

        underTest.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = createdBasket.id,
                posConfigurationId = POS_CONFIGURATION_1.id,
                paymentType = PaymentType.CASHLESS
            )
        )

        val paymentInProgressBasket = basketRepository.findById(createdBasket.id).get()
        assertEquals(BasketState.PAYMENT_IN_PROGRESS, paymentInProgressBasket.state)
        assertEquals(PaymentType.CASHLESS, paymentInProgressBasket.paymentType)

        underTest.completeBasketPayment(
            CompleteBasketPaymentCommand(
                basketId = paymentInProgressBasket.id
            )
        )

        val updatedPaymentInProgressBasket = basketRepository.findById(createdBasket.id).get()
        assertEquals(BasketState.PAID, updatedPaymentInProgressBasket.state)
        assertEquals(PaymentType.CASHLESS, updatedPaymentInProgressBasket.paymentType)
        assertEquals(POS_CONFIGURATION_1.id, updatedPaymentInProgressBasket.paymentPosConfigurationId)
        assertNotNull(updatedPaymentInProgressBasket.paidAt)

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(
                BasketPaidEvent(
                    basketId = createdBasket.id,
                    tableId = createdBasket.tableId
                )
            )
        }
    }

    @Test
    fun `test initOrUpdateMssqlBasket - single offline ticket - should init basket and add one item`() {
        val reservation = reservationRepository.save(EXISTING_RESERVATION_1)
        underTest.initOrUpdateMssqlBasket(
            InitOrUpdateMssqlBasketCommand(
                variableSymbol = null,
                paymentPosConfigurationId = POS_CONFIGURATION_1.id,
                posTitle = POS_CONFIGURATION_1.title,
                paymentType = PaymentType.CASH,
                paidAt = LocalDateTime.now(),
                state = BasketState.PAID,
                originalId = 100,
                screeningId = SCREENING_1.id,
                reservationId = reservation.id,
                seatId = SEAT_1.id,
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                receiptNumber = "123456789",
                ticketDiscountPrimaryId = null,
                ticketDiscountSecondaryId = null,
                discountCardId = null,
                isUsed = true,
                includes3dGlasses = false,
                isCancellationItem = false,
                basePrice = 9.5.toBigDecimal(),
                basePriceBeforeDiscount = 9.5.toBigDecimal(),
                seatSurcharge = null,
                seatSurchargeType = null,
                auditoriumSurcharge = null,
                auditoriumSurchargeType = null,
                seatServiceFee = null,
                seatServiceFeeType = null,
                auditoriumServiceFee = null,
                auditoriumServiceFeeType = null,
                serviceFeeGeneral = null,
                freeTicket = false,
                totalPrice = 9.5.toBigDecimal()
            )
        )

        val basket = basketRepository.findAll()[0]
        assertNull(basket.tableId)
        assertTrue(basket.totalPrice isEqualTo 9.5.toBigDecimal())
        assertEquals(BasketState.PAID, basket.state)
        assertEquals(PaymentType.CASH, basket.paymentType)
        assertNull(basket.preferredPaymentType)
        assertEquals(POS_CONFIGURATION_1.id, basket.paymentPosConfigurationId)
        assertNotNull(basket.paidAt)
        assertNull(basket.variableSymbol)
        assertEquals(POS_CONFIGURATION_1.title, basket.createdBy)
        assertEquals(POS_CONFIGURATION_1.title, basket.updatedBy)

        val basketItem = basketItemFinderService.findAllByBasketId(basket.id)[0]
        assertNotNull(basketItem)
        assertEquals(100, basketItem.originalId)
        assertEquals(basket.id, basketItem.basketId)
        assertNotNull(basketItem.ticketId)
        assertNull(basketItem.productId)
        assertEquals(BasketItemType.TICKET, basketItem.type)
        assertTrue(basketItem.price isEqualTo 9.5.toBigDecimal())
        assertEquals(1, basketItem.quantity)
        assertNull(basketItem.productReceiptNumber)
        assertNull(basketItem.productIsolatedWithId)
        assertNull(basketItem.cancelledBasketItemId)
    }

    @Test
    fun `test initOrUpdateMssqlBasket - single online ticket - should init basket and add one item`() {
        val reservation = reservationRepository.save(EXISTING_RESERVATION_1)
        underTest.initOrUpdateMssqlBasket(
            InitOrUpdateMssqlBasketCommand(
                variableSymbol = "999888777666",
                paymentPosConfigurationId = POS_CONFIGURATION_2.id,
                posTitle = WEB_SALE_USERNAME,
                paymentType = PaymentType.CASHLESS,
                paidAt = LocalDateTime.now(),
                state = BasketState.PAID_ONLINE,
                originalId = 100,
                screeningId = SCREENING_1.id,
                reservationId = reservation.id,
                seatId = SEAT_1.id,
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_20,
                receiptNumber = "123456789",
                ticketDiscountPrimaryId = null,
                ticketDiscountSecondaryId = null,
                discountCardId = null,
                isUsed = true,
                includes3dGlasses = false,
                isCancellationItem = false,
                basePrice = 8.5.toBigDecimal(),
                basePriceBeforeDiscount = 8.5.toBigDecimal(),
                seatSurcharge = 1.toBigDecimal(),
                seatSurchargeType = SeatSurchargeType.VIP,
                auditoriumSurcharge = null,
                auditoriumSurchargeType = null,
                seatServiceFee = null,
                seatServiceFeeType = null,
                auditoriumServiceFee = null,
                auditoriumServiceFeeType = null,
                serviceFeeGeneral = null,
                freeTicket = false,
                totalPrice = 9.5.toBigDecimal()
            )
        )

        val basket = basketRepository.findAll()[0]
        assertNull(basket.tableId)
        assertTrue(basket.totalPrice isEqualTo 9.5.toBigDecimal())
        assertEquals(BasketState.PAID_ONLINE, basket.state)
        assertEquals(PaymentType.CASHLESS, basket.paymentType)
        assertNull(basket.preferredPaymentType)
        assertEquals(POS_CONFIGURATION_2.id, basket.paymentPosConfigurationId)
        assertNotNull(basket.paidAt)
        assertEquals("999888777666", basket.variableSymbol)
        assertEquals("Internet", basket.createdBy)
        assertEquals("Internet", basket.updatedBy)

        val basketItem = basketItemFinderService.findAllByBasketId(basket.id)[0]
        assertNotNull(basketItem)
        assertEquals(100, basketItem.originalId)
        assertEquals(basket.id, basketItem.basketId)
        assertNotNull(basketItem.ticketId)
        assertNull(basketItem.productId)
        assertEquals(BasketItemType.TICKET, basketItem.type)
        assertTrue(basketItem.price isEqualTo 9.5.toBigDecimal())
        assertEquals(1, basketItem.quantity)
        assertNull(basketItem.productReceiptNumber)
        assertNull(basketItem.productIsolatedWithId)
        assertNull(basketItem.cancelledBasketItemId)

        val ticketPrice = ticketPriceJooqFinderService.findAll().first { it.seatId == SEAT_1.id }
        assertTrue(ticketPrice.totalPrice isEqualTo 9.5.toBigDecimal())
        assertTrue(ticketPrice.basePrice isEqualTo 8.5.toBigDecimal())
        assertTrue(ticketPrice.basePriceBeforeDiscount isEqualTo 8.5.toBigDecimal())
        assertTrue(ticketPrice.seatSurcharge isEqualTo 1.toBigDecimal())
        assertEquals(ticketPrice.seatSurchargeType, SeatSurchargeType.VIP)
        assertNull(ticketPrice.auditoriumSurcharge)
        assertNull(ticketPrice.auditoriumSurchargeType)
        assertNull(ticketPrice.seatServiceFee)
        assertNull(ticketPrice.seatServiceFeeType)
        assertNull(ticketPrice.auditoriumServiceFee)
        assertNull(ticketPrice.auditoriumServiceFeeType)
        assertNull(ticketPrice.serviceFeeGeneral)
        assertNull(ticketPrice.freeTicket)
    }

    @Test
    fun `test initOrUpdateMssqlBasket - single offline ticket which already exists - should skip execution`() {
        val reservation = reservationRepository.save(EXISTING_RESERVATION_1)
        underTest.initOrUpdateMssqlBasket(
            InitOrUpdateMssqlBasketCommand(
                variableSymbol = null,
                paymentPosConfigurationId = POS_CONFIGURATION_1.id,
                paymentType = PaymentType.CASH,
                paidAt = LocalDateTime.now(),
                state = BasketState.PAID,
                originalId = 100,
                screeningId = SCREENING_1.id,
                reservationId = reservation.id,
                seatId = SEAT_1.id,
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                receiptNumber = "123456789",
                ticketDiscountPrimaryId = null,
                ticketDiscountSecondaryId = null,
                discountCardId = null,
                isUsed = true,
                includes3dGlasses = false,
                isCancellationItem = false,
                basePrice = 6.5.toBigDecimal(),
                basePriceBeforeDiscount = 6.5.toBigDecimal(),
                seatSurcharge = null,
                seatSurchargeType = null,
                auditoriumSurcharge = 2.toBigDecimal(),
                auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
                seatServiceFee = null,
                seatServiceFeeType = null,
                auditoriumServiceFee = 1.toBigDecimal(),
                auditoriumServiceFeeType = AuditoriumServiceFeeType.IMAX,
                serviceFeeGeneral = null,
                freeTicket = false,
                totalPrice = 9.5.toBigDecimal()
            )
        )

        val ticketPrice1 = ticketPriceJooqFinderService.findAll().first { it.seatId == SEAT_1.id }
        assertTrue(ticketPrice1.totalPrice isEqualTo 9.5.toBigDecimal())
        assertTrue(ticketPrice1.basePrice isEqualTo 6.5.toBigDecimal())
        assertTrue(ticketPrice1.basePriceBeforeDiscount isEqualTo 6.5.toBigDecimal())
        assertNull(ticketPrice1.seatSurcharge)
        assertNull(ticketPrice1.seatSurchargeType)
        assertTrue(ticketPrice1.auditoriumSurcharge isEqualTo 2.toBigDecimal())
        assertEquals(ticketPrice1.auditoriumSurchargeType, AuditoriumSurchargeType.IMAX)
        assertNull(ticketPrice1.seatServiceFee)
        assertNull(ticketPrice1.seatServiceFeeType)
        assertTrue(ticketPrice1.auditoriumServiceFee isEqualTo 1.toBigDecimal())
        assertEquals(ticketPrice1.auditoriumServiceFeeType, AuditoriumServiceFeeType.IMAX)
        assertNull(ticketPrice1.serviceFeeGeneral)
        assertNull(ticketPrice1.freeTicket)

        assertDoesNotThrow {
            underTest.initOrUpdateMssqlBasket(
                InitOrUpdateMssqlBasketCommand(
                    variableSymbol = null,
                    paymentPosConfigurationId = POS_CONFIGURATION_1.id,
                    paymentType = PaymentType.CASH,
                    paidAt = LocalDateTime.now(),
                    state = BasketState.PAID,
                    originalId = 100,
                    screeningId = SCREENING_1.id,
                    reservationId = reservation.id,
                    seatId = SEAT_1.id,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                    receiptNumber = "123456789",
                    ticketDiscountPrimaryId = null,
                    ticketDiscountSecondaryId = null,
                    discountCardId = null,
                    isUsed = true,
                    includes3dGlasses = false,
                    isCancellationItem = false,
                    basePrice = 5.5.toBigDecimal(),
                    basePriceBeforeDiscount = 5.5.toBigDecimal(),
                    seatSurcharge = null,
                    seatSurchargeType = null,
                    auditoriumSurcharge = 1.toBigDecimal(),
                    auditoriumSurchargeType = AuditoriumSurchargeType.IMAX,
                    seatServiceFee = null,
                    seatServiceFeeType = null,
                    auditoriumServiceFee = 2.toBigDecimal(),
                    auditoriumServiceFeeType = AuditoriumServiceFeeType.IMAX,
                    serviceFeeGeneral = null,
                    freeTicket = false,
                    totalPrice = 8.5.toBigDecimal()
                )
            )
        }

        assertEquals(1, basketRepository.count())
        assertEquals(1, basketItemRepository.count())

        val ticketPrice2 = ticketPriceJooqFinderService.findAll().first { it.seatId == SEAT_1.id }
        assertTrue(ticketPrice2.totalPrice isEqualTo 9.5.toBigDecimal())
        assertTrue(ticketPrice2.basePrice isEqualTo 6.5.toBigDecimal())
        assertTrue(ticketPrice2.basePriceBeforeDiscount isEqualTo 6.5.toBigDecimal())
        assertNull(ticketPrice2.seatSurcharge)
        assertNull(ticketPrice2.seatSurchargeType)
        assertTrue(ticketPrice2.auditoriumSurcharge isEqualTo 2.toBigDecimal())
        assertEquals(ticketPrice2.auditoriumSurchargeType, AuditoriumSurchargeType.IMAX)
        assertNull(ticketPrice2.seatServiceFee)
        assertNull(ticketPrice2.seatServiceFeeType)
        assertTrue(ticketPrice2.auditoriumServiceFee isEqualTo 1.toBigDecimal())
        assertEquals(ticketPrice2.auditoriumServiceFeeType, AuditoriumServiceFeeType.IMAX)
        assertNull(ticketPrice2.serviceFeeGeneral)
        assertNull(ticketPrice2.freeTicket)
    }

    @Test
    fun `test initOrUpdateMssqlBasket - multiple items - should init basket and then add items into it`() {
        reservationRepository.saveAll(
            setOf(
                EXISTING_RESERVATION_1,
                EXISTING_RESERVATION_2,
                EXISTING_RESERVATION_3
            )
        )
        val initCommand = InitOrUpdateMssqlBasketCommand(
            variableSymbol = "999888777666",
            paidAt = LocalDateTime.now(),
            paymentPosConfigurationId = POS_CONFIGURATION_2.id,
            paymentType = PaymentType.CASHLESS,
            state = BasketState.PAID_ONLINE,
            originalId = 100,
            screeningId = SCREENING_1.id,
            reservationId = EXISTING_RESERVATION_1.id,
            seatId = SEAT_1.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_20,
            receiptNumber = "123456789",
            ticketDiscountPrimaryId = null,
            ticketDiscountSecondaryId = null,
            discountCardId = null,
            isUsed = true,
            includes3dGlasses = false,
            isCancellationItem = false,
            basePrice = 9.0.toBigDecimal(),
            basePriceBeforeDiscount = 9.0.toBigDecimal(),
            seatSurcharge = null,
            seatSurchargeType = null,
            auditoriumSurcharge = null,
            auditoriumSurchargeType = null,
            seatServiceFee = null,
            seatServiceFeeType = null,
            auditoriumServiceFee = null,
            auditoriumServiceFeeType = null,
            serviceFeeGeneral = null,
            freeTicket = false,
            totalPrice = 9.0.toBigDecimal()
        )

        underTest.initOrUpdateMssqlBasket(initCommand)
        underTest.initOrUpdateMssqlBasket(
            initCommand.copy(
                reservationId = EXISTING_RESERVATION_2.id,
                seatId = SEAT_2.id,
                receiptNumber = "123456788",
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_19,
                originalId = 101,
                basePrice = 8.0.toBigDecimal(),
                basePriceBeforeDiscount = 8.0.toBigDecimal(),
                totalPrice = 8.0.toBigDecimal()
            )
        )
        underTest.initOrUpdateMssqlBasket(
            initCommand.copy(
                reservationId = EXISTING_RESERVATION_3.id,
                seatId = SEAT_3.id,
                receiptNumber = "123456787",
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_20,
                originalId = 102
            )
        )

        val baskets = basketRepository.findAll()
        assertEquals(1, baskets.size)

        val basket = basketRepository.findAll()[0]
        assertNull(basket.tableId)
        assertTrue(basket.totalPrice isEqualTo 26.toBigDecimal())
        assertEquals(BasketState.PAID_ONLINE, basket.state)
        assertEquals(PaymentType.CASHLESS, basket.paymentType)
        assertNull(basket.preferredPaymentType)
        assertEquals(POS_CONFIGURATION_2.id, basket.paymentPosConfigurationId)
        assertNotNull(basket.paidAt)
        assertEquals("999888777666", basket.variableSymbol)

        val basketItems = basketItemFinderService.findAllByBasketId(basket.id)
        assertEquals(3, basketItems.size)
        basketItems.sortedBy { it.createdAt }.forEachIndexed { index, actual ->
            assertNotNull(actual)
            if (index in setOf(0, 2)) {
                assertEquals(100 + index, actual.originalId)
            }
            assertEquals(basket.id, actual.basketId)
            assertNotNull(actual.ticketId)
            assertNull(actual.productId)
            assertEquals(BasketItemType.TICKET, actual.type)
            if (index in setOf(0, 2)) {
                assertTrue(actual.price isEqualTo 9.toBigDecimal())
            } else {
                assertTrue(actual.price isEqualTo 8.toBigDecimal())
            }
            assertEquals(1, actual.quantity)
            assertNull(actual.productReceiptNumber)
            assertNull(actual.productIsolatedWithId)
            assertNull(actual.cancelledBasketItemId)
        }
    }

    @Test
    fun `test initOrUpdateMssqlBasket - multiple baskets & items - should init baskets and then add items into them`() {
        reservationRepository.saveAll(
            setOf(
                EXISTING_RESERVATION_1,
                EXISTING_RESERVATION_2,
                EXISTING_RESERVATION_3
            )
        )
        val initCommand = InitOrUpdateMssqlBasketCommand(
            variableSymbol = "999888777666",
            paidAt = LocalDateTime.now(),
            paymentPosConfigurationId = POS_CONFIGURATION_2.id,
            paymentType = PaymentType.CASHLESS,
            state = BasketState.PAID_ONLINE,
            originalId = 100,
            screeningId = SCREENING_1.id,
            reservationId = EXISTING_RESERVATION_1.id,
            seatId = SEAT_1.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_20,
            receiptNumber = "123456789",
            ticketDiscountPrimaryId = null,
            ticketDiscountSecondaryId = null,
            discountCardId = null,
            isUsed = true,
            includes3dGlasses = false,
            isCancellationItem = false,
            basePrice = 9.5.toBigDecimal(),
            basePriceBeforeDiscount = 9.5.toBigDecimal(),
            seatSurcharge = null,
            seatSurchargeType = null,
            auditoriumSurcharge = null,
            auditoriumSurchargeType = null,
            seatServiceFee = null,
            seatServiceFeeType = null,
            auditoriumServiceFee = null,
            auditoriumServiceFeeType = null,
            serviceFeeGeneral = null,
            freeTicket = false,
            totalPrice = 9.5.toBigDecimal()
        )

        underTest.initOrUpdateMssqlBasket(initCommand)
        underTest.initOrUpdateMssqlBasket(
            initCommand.copy(
                variableSymbol = "666777888999",
                reservationId = EXISTING_RESERVATION_2.id,
                seatId = SEAT_2.id,
                receiptNumber = "123456788",
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_19,
                basePrice = 8.5.toBigDecimal(),
                basePriceBeforeDiscount = 8.5.toBigDecimal(),
                totalPrice = 8.5.toBigDecimal()
            )
        )
        underTest.initOrUpdateMssqlBasket(
            initCommand.copy(
                variableSymbol = "999888777666",
                reservationId = EXISTING_RESERVATION_3.id,
                seatId = SEAT_3.id,
                receiptNumber = "123456787",
                priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_20,
                basePrice = 7.0.toBigDecimal(),
                basePriceBeforeDiscount = 7.0.toBigDecimal(),
                totalPrice = 7.0.toBigDecimal()
            )
        )

        val baskets = basketRepository.findAll()
        assertEquals(2, baskets.size)

        val basket1 = basketRepository.findAll().sortedBy { it.createdAt }[0]
        assertNull(basket1.tableId)
        assertTrue(basket1.totalPrice isEqualTo 16.5.toBigDecimal())
        assertEquals(BasketState.PAID_ONLINE, basket1.state)
        assertEquals(PaymentType.CASHLESS, basket1.paymentType)
        assertNull(basket1.preferredPaymentType)
        assertEquals(POS_CONFIGURATION_2.id, basket1.paymentPosConfigurationId)
        assertNotNull(basket1.paidAt)
        assertEquals("999888777666", basket1.variableSymbol)

        val basketItems1 = basketItemFinderService.findAllByBasketId(basket1.id)
        assertEquals(2, basketItems1.size)
        basketItems1.sortedBy { it.createdAt }.forEachIndexed { index, actual ->
            assertNotNull(actual)
            if (index in setOf(0, 2)) {
                assertEquals(100 + index, actual.originalId)
            }
            assertEquals(basket1.id, actual.basketId)
            assertNotNull(actual.ticketId)
            assertNull(actual.productId)
            assertEquals(BasketItemType.TICKET, actual.type)
            if (index in setOf(0)) {
                assertTrue(actual.price isEqualTo 9.5.toBigDecimal())
            }
            if (index in setOf(2)) {
                assertTrue(actual.price isEqualTo 7.0.toBigDecimal())
            }
            assertEquals(1, actual.quantity)
            assertNull(actual.productReceiptNumber)
            assertNull(actual.productIsolatedWithId)
            assertNull(actual.cancelledBasketItemId)
        }

        val basket2 = basketRepository.findAll().sortedBy { it.createdAt }[1]
        assertNull(basket2.tableId)
        assertTrue(basket2.totalPrice isEqualTo 8.5.toBigDecimal())
        assertEquals(BasketState.PAID_ONLINE, basket2.state)
        assertEquals(PaymentType.CASHLESS, basket2.paymentType)
        assertNull(basket2.preferredPaymentType)
        assertEquals(POS_CONFIGURATION_2.id, basket2.paymentPosConfigurationId)
        assertNotNull(basket2.paidAt)
        assertEquals("666777888999", basket2.variableSymbol)

        val basketItems2 = basketItemFinderService.findAllByBasketId(basket2.id)
        assertEquals(1, basketItems2.size)
        basketItems2.sortedBy { it.createdAt }.forEachIndexed { index, actual ->
            assertNotNull(actual)
            if (index in setOf(0, 2)) {
                assertEquals(100 + index, actual.originalId)
            }
            assertEquals(basket2.id, actual.basketId)
            assertNotNull(actual.ticketId)
            assertNull(actual.productId)
            assertEquals(BasketItemType.TICKET, actual.type)
            assertTrue(actual.price isEqualTo 8.5.toBigDecimal())
            assertEquals(1, actual.quantity)
            assertNull(actual.productReceiptNumber)
            assertNull(actual.productIsolatedWithId)
            assertNull(actual.cancelledBasketItemId)
        }
    }

    @Test
    fun `test createProductSalesBaskets - single command - should init basket and add one item`() {
        val command = CreateProductSalesBasketCommand(
            paidAt = LocalDateTime.now(),
            paymentType = PaymentType.CASH,
            receiptNumber = "1098765",
            productId = PRODUCT_1.id,
            type = BasketItemType.PRODUCT,
            price = 20.toBigDecimal(),
            quantity = 2,
            isCancelled = false,
            branchId = BRANCH_1.id
        )

        underTest.createProductSalesBaskets(listOf(command))

        val basket = basketRepository.findAll()[0]
        assertEqualsInitOrUpdateProductSalesBasketCommandToBasket(
            command = command,
            expectedPrice = command.price,
            basket = basket
        )

        val basketItem = basketItemFinderService.findAllByBasketId(basket.id)[0]
        assertNotNull(basketItem)
        assertEqualsInitOrUpdateProductSalesBasketCommandToBasketItem(
            command = command,
            basketId = basket.id,
            basketItem = basketItem
        )
    }

    @Test
    fun `test createProductSalesBaskets - multiple items - should init basket and then add items into it`() {
        val paidAt = LocalDateTime.now()

        val command1 = CreateProductSalesBasketCommand(
            paidAt = paidAt,
            paymentType = PaymentType.CASH,
            receiptNumber = "1098765",
            productId = PRODUCT_1.id,
            type = BasketItemType.PRODUCT,
            price = 20.toBigDecimal(),
            quantity = 2,
            isCancelled = false,
            branchId = BRANCH_1.id
        )
        val command2 = CreateProductSalesBasketCommand(
            paidAt = paidAt,
            paymentType = PaymentType.CASH,
            receiptNumber = "1098765",
            productId = PRODUCT_2.id,
            type = BasketItemType.PRODUCT,
            price = 10.toBigDecimal(),
            quantity = 1,
            isCancelled = false,
            branchId = BRANCH_1.id
        )
        val command3 = CreateProductSalesBasketCommand(
            paidAt = paidAt,
            paymentType = PaymentType.CASH,
            receiptNumber = "1098765",
            productId = PRODUCT_3.id,
            type = BasketItemType.PRODUCT,
            price = 5.toBigDecimal(),
            quantity = 1,
            isCancelled = false,
            branchId = BRANCH_1.id
        )

        underTest.createProductSalesBaskets(listOf(command1, command2, command3))

        val baskets = basketRepository.findAll()
        assertEquals(1, baskets.size)

        val basket = baskets[0]
        assertEqualsInitOrUpdateProductSalesBasketCommandToBasket(
            command = command1,
            expectedPrice = 35.toBigDecimal(),
            basket = basket
        )

        val basketItems = basketItemFinderService.findAllByBasketId(basket.id)
        assertEquals(3, basketItems.size)

        basketItems.sortedBy { it.createdAt }.zip(listOf(command1, command2, command3)).forEach {
            assertEqualsInitOrUpdateProductSalesBasketCommandToBasketItem(
                command = it.second,
                basketId = basket.id,
                basketItem = it.first
            )
        }
    }

    @Test
    fun `test createProductSalesBaskets - multiple baskets & items - should init baskets and then add items into them`() {
        val paidAt = LocalDateTime.now()

        val command1 = CreateProductSalesBasketCommand(
            paidAt = paidAt,
            paymentType = PaymentType.CASH,
            receiptNumber = "1098765",
            productId = PRODUCT_1.id,
            type = BasketItemType.PRODUCT,
            price = 20.toBigDecimal(),
            quantity = 2,
            isCancelled = false,
            branchId = BRANCH_1.id
        )
        val command2 = CreateProductSalesBasketCommand(
            paidAt = paidAt,
            paymentType = PaymentType.CASHLESS,
            receiptNumber = "2233765",
            productId = PRODUCT_2.id,
            type = BasketItemType.PRODUCT,
            price = 10.toBigDecimal(),
            quantity = 1,
            isCancelled = true,
            branchId = BRANCH_2.id
        )
        val command3 = CreateProductSalesBasketCommand(
            paidAt = paidAt,
            paymentType = PaymentType.CASH,
            receiptNumber = "1098765",
            productId = PRODUCT_3.id,
            type = BasketItemType.PRODUCT,
            price = 5.toBigDecimal(),
            quantity = 1,
            isCancelled = false,
            branchId = BRANCH_1.id
        )

        underTest.createProductSalesBaskets(listOf(command1, command2, command3))

        val baskets = basketRepository.findAll()
        assertEquals(2, baskets.size)

        assertEqualsInitOrUpdateProductSalesBasketCommandToBasket(
            command = command1,
            expectedPrice = 25.toBigDecimal(),
            basket = baskets[0]
        )
        assertEqualsInitOrUpdateProductSalesBasketCommandToBasket(
            command = command2,
            expectedPrice = 10.toBigDecimal(),
            basket = baskets[1]
        )

        val basketItems = basketItemRepository.findAll()
        assertEquals(3, basketItems.size)

        basketItems.sortedBy { it.createdAt }.zip(listOf(command1, command3, command2)).forEach {
            assertEqualsInitOrUpdateProductSalesBasketCommandToBasketItem(
                command = it.second,
                basketId = if (it.second.receiptNumber == "1098765") baskets[0].id else baskets[1].id,
                basketItem = it.first
            )
        }
    }

    @Test
    fun `test messagingCreateBasket - basket doesn't exist - should create basket and all its items`() {
        setOf(TICKET_DISCOUNT_1, TICKET_DISCOUNT_2).forEach {
            ticketDiscountService.syncCreateOrUpdateTicketDiscount(mapToCreateOrUpdateTicketDiscountCommand(it))
        }
        discountCardService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(DISCOUNT_CARD_1))

        val command = MessagingCreateBasketCommand(
            id = UUID.randomUUID(),
            totalPrice = 23.5.toBigDecimal(),
            state = BasketState.PAID,
            paidAt = LocalDateTime.now(),
            paymentType = PaymentType.CASHLESS,
            variableSymbol = null,
            branchCode = BRANCH_1.code,
            items = listOf(
                MessagingCreateBasketCommand.BasketItem(
                    id = 1.toUUID(),
                    type = BasketItemType.TICKET,
                    price = 7.125.toBigDecimal(),
                    quantity = 1,
                    isCancelled = false,
                    discountCardCode = null,
                    ticket = MessagingCreateBasketCommand.BasketTicketItem(
                        id = 11.toUUID(),
                        screeningId = SCREENING_1.id,
                        auditoriumOriginalCode = AUDITORIUM_1.originalCode,
                        seatOriginalId = SEAT_1.originalId!!,
                        basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
                        ticketReceiptNumber = "100000001",
                        ticketDiscountPrimaryCode = null,
                        ticketDiscountSecondaryCode = TICKET_DISCOUNT_2.code,
                        isGroupTicket = false,
                        isUsed = false,
                        includes3dGlasses = false
                    ),
                    product = null
                ),
                MessagingCreateBasketCommand.BasketItem(
                    id = 2.toUUID(),
                    type = BasketItemType.TICKET,
                    price = 6.375.toBigDecimal(),
                    quantity = 1,
                    isCancelled = false,
                    discountCardCode = DISCOUNT_CARD_1.code,
                    ticket = MessagingCreateBasketCommand.BasketTicketItem(
                        id = 22.toUUID(),
                        screeningId = SCREENING_1.id,
                        auditoriumOriginalCode = AUDITORIUM_1.originalCode,
                        seatOriginalId = SEAT_2.originalId!!,
                        basePriceItemNumber = PriceCategoryItemNumber.PRICE_2,
                        ticketReceiptNumber = "100000002",
                        ticketDiscountPrimaryCode = TICKET_DISCOUNT_1.code,
                        ticketDiscountSecondaryCode = null,
                        isGroupTicket = true,
                        isUsed = true,
                        includes3dGlasses = true
                    ),
                    product = null
                ),
                MessagingCreateBasketCommand.BasketItem(
                    id = 3.toUUID(),
                    type = BasketItemType.PRODUCT,
                    price = 10.toBigDecimal(),
                    quantity = 2,
                    isCancelled = false,
                    discountCardCode = null,
                    ticket = null,
                    product = MessagingCreateBasketCommand.BasketProductItem(
                        productCode = PRODUCT_1.code,
                        productReceiptNumber = "123456789"
                    )
                )
            )
        )

        underTest.messagingCreateBasket(command)

        val basketModel = basketFinderService.getWithItemsById(command.id)
        assertEquals(command.id, basketModel.basket.id)
        assertTrue(basketModel.basket.totalPrice isEqualTo 23.5.toBigDecimal())
        assertEquals(BasketState.PAID, basketModel.basket.state)
        assertEquals(command.paidAt.truncatedToSeconds(), basketModel.basket.paidAt!!.truncatedToSeconds())
        assertEquals(PaymentType.CASHLESS, basketModel.basket.paymentType)
        assertNull(basketModel.basket.variableSymbol)
        assertEquals(3, basketModel.basketItems.size)

        val ticketItem1 = basketModel.basketItems[0]
        assertEquals(1.toUUID(), ticketItem1.id)
        assertEquals(BasketItemType.TICKET, ticketItem1.type)
        assertTrue(ticketItem1.price isEqualTo 7.125.toBigDecimal())
        assertEquals(1, ticketItem1.quantity)
        assertFalse(ticketItem1.isCancelled)
        assertNull(ticketItem1.productId)
        assertEquals(BRANCH_1.id, ticketItem1.branchId)

        val ticketModel1 = ticketJooqFinderService.getTicketModelsByIdIn(setOfNotNull(ticketItem1.ticketId)).first()
        assertEquals(11.toUUID(), ticketModel1.ticket.id)
        assertEquals(SCREENING_1.id, ticketModel1.ticket.screeningId)
        assertEquals("100000001", ticketModel1.ticket.receiptNumber)
        assertNull(ticketModel1.ticket.ticketDiscountPrimaryId)
        assertEquals(TICKET_DISCOUNT_2.id, ticketModel1.ticket.ticketDiscountSecondaryId)
        assertFalse(ticketModel1.ticket.isGroupTicket)
        assertFalse(ticketModel1.ticket.isUsed)
        assertFalse(ticketModel1.ticket.includes3dGlasses)
        assertTrue(ticketModel1.ticketPrice.basePrice isEqualTo 7.125.toBigDecimal())
        assertTrue(ticketModel1.ticketPrice.basePriceBeforeDiscount isEqualTo 9.5.toBigDecimal())
        assertTrue(ticketModel1.ticketPrice.totalPrice isEqualTo 7.125.toBigDecimal())
        assertEquals(25, ticketModel1.ticketPrice.secondaryDiscountPercentage)
        assertNull(ticketModel1.ticketPrice.secondaryDiscountAmount)
        assertEquals(PriceCategoryItemNumber.PRICE_1, ticketModel1.ticketPrice.basePriceItemNumber)
        assertEquals(SEAT_1.id, ticketModel1.ticketPrice.seatId)

        val reservation1 = reservationJooqFinderService.getById(ticketModel1.ticket.reservationId)
        assertEquals(SCREENING_1.id, reservation1.screeningId)
        assertEquals(SEAT_1.id, reservation1.seatId)
        assertEquals(ReservationState.UNAVAILABLE, reservation1.state)

        val ticketItem2 = basketModel.basketItems[1]
        assertEquals(2.toUUID(), ticketItem2.id)
        assertEquals(BasketItemType.TICKET, ticketItem2.type)
        assertTrue(ticketItem2.price isEqualTo 6.375.toBigDecimal())
        assertEquals(1, ticketItem2.quantity)
        assertFalse(ticketItem2.isCancelled)
        assertNull(ticketItem2.productId)
        assertEquals(BRANCH_1.id, ticketItem1.branchId)

        val ticketModel2 = ticketJooqFinderService.getTicketModelsByIdIn(setOfNotNull(ticketItem2.ticketId)).first()
        assertEquals(22.toUUID(), ticketModel2.ticket.id)
        assertEquals(SCREENING_1.id, ticketModel2.ticket.screeningId)
        assertEquals("100000002", ticketModel2.ticket.receiptNumber)
        assertEquals(TICKET_DISCOUNT_1.id, ticketModel2.ticket.ticketDiscountPrimaryId)
        assertNull(ticketModel2.ticket.ticketDiscountSecondaryId)
        assertTrue(ticketModel2.ticket.isGroupTicket)
        assertTrue(ticketModel2.ticket.isUsed)
        assertTrue(ticketModel2.ticket.includes3dGlasses)
        assertTrue(ticketModel2.ticketPrice.basePrice isEqualTo 6.375.toBigDecimal())
        assertTrue(ticketModel2.ticketPrice.basePriceBeforeDiscount isEqualTo 7.5.toBigDecimal())
        assertTrue(ticketModel2.ticketPrice.totalPrice isEqualTo 6.375.toBigDecimal())
        assertEquals(15, ticketModel2.ticketPrice.primaryDiscountPercentage)
        assertNull(ticketModel2.ticketPrice.primaryDiscountAmount)
        assertEquals(PriceCategoryItemNumber.PRICE_2, ticketModel2.ticketPrice.basePriceItemNumber)
        assertEquals(SEAT_2.id, ticketModel2.ticketPrice.seatId)

        val reservation2 = reservationJooqFinderService.getById(ticketModel2.ticket.reservationId)
        assertEquals(SCREENING_1.id, reservation2.screeningId)
        assertEquals(SEAT_2.id, reservation2.seatId)
        assertEquals(ReservationState.UNAVAILABLE, reservation2.state)

        val productItem = basketModel.basketItems[2]
        assertEquals(3.toUUID(), productItem.id)
        assertEquals(BasketItemType.PRODUCT, productItem.type)
        assertTrue(productItem.price isEqualTo 10.toBigDecimal())
        assertEquals(2, productItem.quantity)
        assertFalse(productItem.isCancelled)
        assertNull(productItem.ticketId)

        val product = productFinderService.getNonDeletedById(productItem.productId!!)
        assertEquals(command.items[2].product!!.productCode, product.code)
        assertEquals(command.items[2].product!!.productReceiptNumber, productItem.productReceiptNumber)

        verify {
            applicationEventPublisherMock.publishEvent(
                MessagingBasketItemWithDiscountCardCreatedEvent(
                    discountCardId = DISCOUNT_CARD_1.id,
                    basketId = basketModel.basket.id,
                    basketItemId = ticketItem2.id
                )
            )
        }
    }

    @Test
    fun `test messagingCreateBasket - basket is in invalid state - should throw exception`() {
        val command = MessagingCreateBasketCommand(
            id = UUID.randomUUID(),
            totalPrice = 9.5.toBigDecimal(),
            state = BasketState.PAYMENT_IN_PROGRESS,
            paidAt = LocalDateTime.now(),
            paymentType = PaymentType.CASHLESS,
            variableSymbol = null,
            branchCode = BRANCH_2.code,
            items = listOf(
                MessagingCreateBasketCommand.BasketItem(
                    id = 1.toUUID(),
                    type = BasketItemType.TICKET,
                    price = 9.5.toBigDecimal(),
                    quantity = 1,
                    isCancelled = false,
                    discountCardCode = null,
                    ticket = MessagingCreateBasketCommand.BasketTicketItem(
                        id = 11.toUUID(),
                        screeningId = SCREENING_1.id,
                        auditoriumOriginalCode = AUDITORIUM_1.originalCode,
                        seatOriginalId = SEAT_1.originalId!!,
                        basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
                        ticketReceiptNumber = "100000001",
                        ticketDiscountPrimaryCode = null,
                        ticketDiscountSecondaryCode = null,
                        isGroupTicket = false,
                        isUsed = false,
                        includes3dGlasses = false
                    ),
                    product = null
                )
            )
        )

        assertThrows<IllegalStateException> {
            underTest.messagingCreateBasket(command)
        }
    }

    @Test
    fun `test messagingCreateBasket - basket with given id already exists - should skip execution`() {
        val basketId = UUID.randomUUID()
        basketRepository.save(
            Basket(
                id = basketId,
                totalPrice = 20.toBigDecimal(),
                state = BasketState.PAID,
                paymentType = PaymentType.CASHLESS,
                paidAt = LocalDateTime.now().minusDays(1)
            )
        )

        assertEquals(1, basketRepository.count())
        assertEquals(0, basketItemRepository.count())

        val command = MessagingCreateBasketCommand(
            id = basketId,
            totalPrice = 9.5.toBigDecimal(),
            state = BasketState.PAID,
            paidAt = LocalDateTime.now(),
            paymentType = PaymentType.CASHLESS,
            variableSymbol = null,
            branchCode = BRANCH_2.code,
            items = listOf(
                MessagingCreateBasketCommand.BasketItem(
                    id = 1.toUUID(),
                    type = BasketItemType.TICKET,
                    price = 9.5.toBigDecimal(),
                    quantity = 1,
                    isCancelled = false,
                    discountCardCode = null,
                    ticket = MessagingCreateBasketCommand.BasketTicketItem(
                        id = 11.toUUID(),
                        screeningId = SCREENING_1.id,
                        auditoriumOriginalCode = AUDITORIUM_1.originalCode,
                        seatOriginalId = SEAT_1.originalId!!,
                        basePriceItemNumber = PriceCategoryItemNumber.PRICE_1,
                        ticketReceiptNumber = "100000001",
                        ticketDiscountPrimaryCode = null,
                        ticketDiscountSecondaryCode = null,
                        isGroupTicket = false,
                        isUsed = false,
                        includes3dGlasses = false
                    ),
                    product = null
                )
            )
        )

        assertDoesNotThrow {
            underTest.messagingCreateBasket(command)
        }

        assertTrue(basketRepository.findAll().first().totalPrice isEqualTo 20.toBigDecimal())

        assertEquals(1, basketRepository.count())
        assertEquals(0, basketItemRepository.count())
    }

    @Test
    fun `test createTicketSalesBaskets - single item - should create basket with one item`() {
        underTest.createTicketSalesBaskets(
            listOf(
                CreateTicketSalesBasketCommand(
                    variableSymbol = "4800427267",
                    paidAt = LocalDateTime.of(2022, 7, 9, 8, 37, 0),
                    paymentPosConfigurationId = POS_CONFIGURATION_3.id,
                    paymentType = PaymentType.CASHLESS,
                    state = BasketState.PAID_ONLINE,
                    originalId = 1,
                    screeningId = SCREENING_1.id,
                    seatId = SEAT_1.id,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_19,
                    receiptNumber = "0002104035",
                    ticketDiscountPrimaryId = null,
                    ticketDiscountSecondaryId = null,
                    discountCardId = null,
                    isUsed = true,
                    isCancellationItem = false,
                    includes3dGlasses = false,
                    branchId = BRANCH_1.id,
                    basePrice = 7.5.toBigDecimal(),
                    basePriceBeforeDiscount = 7.5.toBigDecimal(),
                    seatSurcharge = null,
                    seatSurchargeType = null,
                    auditoriumSurcharge = null,
                    auditoriumSurchargeType = null,
                    seatServiceFee = 1.toBigDecimal(),
                    seatServiceFeeType = SeatServiceFeeType.PREMIUM_PLUS,
                    auditoriumServiceFee = null,
                    auditoriumServiceFeeType = null,
                    serviceFeeGeneral = 1.toBigDecimal(),
                    freeTicket = false,
                    totalPrice = 9.5.toBigDecimal()
                )
            )
        )

        val basket = basketRepository.findAll()[0]
        assertNull(basket.tableId)
        assertTrue(basket.totalPrice isEqualTo 9.5.toBigDecimal())
        assertEquals(BasketState.PAID_ONLINE, basket.state)
        assertEquals(PaymentType.CASHLESS, basket.paymentType)
        assertNull(basket.preferredPaymentType)
        assertEquals(POS_CONFIGURATION_3.id, basket.paymentPosConfigurationId)
        assertEquals(LocalDateTime.of(2022, 7, 9, 8, 37, 0), basket.paidAt)
        assertEquals("4800427267", basket.variableSymbol)

        val basketItem = basketItemFinderService.findAllByBasketId(basket.id)[0]
        assertNotNull(basketItem)
        assertEquals(1, basketItem.originalId)
        assertEquals(basket.id, basketItem.basketId)
        assertNotNull(basketItem.ticketId)
        assertNull(basketItem.productId)
        assertEquals(BasketItemType.TICKET, basketItem.type)
        assertTrue(basketItem.price isEqualTo 9.5.toBigDecimal())
        assertEquals(1, basketItem.quantity)
        assertNull(basketItem.productReceiptNumber)
        assertNull(basketItem.productIsolatedWithId)
        assertNull(basketItem.cancelledBasketItemId)
        assertEquals(BRANCH_1.id, basketItem.branchId)

        val ticket = ticketJooqFinderService.getById(basketItem.ticketId!!)
        assertEquals(SCREENING_1.id, ticket.screeningId)
        assertNull(ticket.ticketDiscountPrimaryId)
        assertNull(ticket.ticketDiscountSecondaryId)
        assertEquals("0002104035", ticket.receiptNumber)
        assertFalse(ticket.isGroupTicket)
        assertTrue(ticket.isUsed)
        assertFalse(ticket.includes3dGlasses)

        val ticketPrice = ticketPriceJooqFinderService.findAll().first { it.seatId == SEAT_1.id }
        assertTrue(ticketPrice.totalPrice isEqualTo 9.5.toBigDecimal())
        assertTrue(ticketPrice.basePrice isEqualTo 7.5.toBigDecimal())
        assertTrue(ticketPrice.basePriceBeforeDiscount isEqualTo 7.5.toBigDecimal())
        assertNull(ticketPrice.seatSurcharge)
        assertNull(ticketPrice.seatSurchargeType)
        assertNull(ticketPrice.auditoriumSurcharge)
        assertNull(ticketPrice.auditoriumSurchargeType)
        assertTrue(ticketPrice.seatServiceFee isEqualTo 1.toBigDecimal())
        assertEquals(ticketPrice.seatServiceFeeType, SeatServiceFeeType.PREMIUM_PLUS)
        assertNull(ticketPrice.auditoriumServiceFee)
        assertNull(ticketPrice.auditoriumServiceFeeType)
        assertTrue(ticketPrice.serviceFeeGeneral isEqualTo 1.toBigDecimal())
        assertNull(ticketPrice.freeTicket)

        val createdReservation = reservationJooqFinderService.getById(ticket.reservationId)
        assertEquals(SCREENING_1.id, createdReservation.screeningId)
        assertEquals(SEAT_1.id, createdReservation.seatId)
        assertEquals(ReservationState.UNAVAILABLE, createdReservation.state)
    }

    @Test
    fun `test createTicketSalesBaskets - two almost identical items but on different POS's - should create 2 baskets`() {
        underTest.createTicketSalesBaskets(
            listOf(
                CreateTicketSalesBasketCommand(
                    variableSymbol = null,
                    paidAt = LocalDateTime.of(2023, 11, 1, 9, 8, 0),
                    paymentPosConfigurationId = POS_CONFIGURATION_1.id,
                    paymentType = PaymentType.CASH,
                    state = BasketState.PAID,
                    originalId = 100,
                    screeningId = SCREENING_1.id,
                    seatId = SEAT_1.id,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
                    receiptNumber = "0001948987",
                    ticketDiscountPrimaryId = null,
                    ticketDiscountSecondaryId = null,
                    discountCardId = null,
                    isUsed = false,
                    isCancellationItem = true,
                    includes3dGlasses = true,
                    branchId = BRANCH_1.id,
                    basePrice = 9.5.toBigDecimal(),
                    basePriceBeforeDiscount = 9.5.toBigDecimal(),
                    seatSurcharge = null,
                    seatSurchargeType = null,
                    auditoriumSurcharge = null,
                    auditoriumSurchargeType = null,
                    seatServiceFee = null,
                    seatServiceFeeType = null,
                    auditoriumServiceFee = null,
                    auditoriumServiceFeeType = null,
                    serviceFeeGeneral = null,
                    freeTicket = false,
                    totalPrice = 9.5.toBigDecimal()
                ),
                CreateTicketSalesBasketCommand(
                    variableSymbol = null,
                    paidAt = LocalDateTime.of(2023, 11, 1, 9, 8, 0),
                    paymentPosConfigurationId = POS_CONFIGURATION_2.id,
                    paymentType = PaymentType.CASH,
                    state = BasketState.PAID,
                    originalId = 101,
                    screeningId = SCREENING_1.id,
                    seatId = SEAT_2.id,
                    priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_2,
                    receiptNumber = "0001948988",
                    ticketDiscountPrimaryId = null,
                    ticketDiscountSecondaryId = null,
                    discountCardId = null,
                    isUsed = true,
                    isCancellationItem = false,
                    includes3dGlasses = false,
                    branchId = BRANCH_1.id,
                    basePrice = 7.5.toBigDecimal(),
                    basePriceBeforeDiscount = 7.5.toBigDecimal(),
                    seatSurcharge = null,
                    seatSurchargeType = null,
                    auditoriumSurcharge = null,
                    auditoriumSurchargeType = null,
                    seatServiceFee = null,
                    seatServiceFeeType = null,
                    auditoriumServiceFee = null,
                    auditoriumServiceFeeType = null,
                    serviceFeeGeneral = null,
                    freeTicket = false,
                    totalPrice = 7.5.toBigDecimal()
                )
            )
        )

        assertEquals(2, basketRepository.count())

        val basket1 = basketRepository.findAll()[0]
        assertNull(basket1.tableId)
        assertTrue(basket1.totalPrice isEqualTo 9.5.toBigDecimal())
        assertEquals(BasketState.PAID, basket1.state)
        assertEquals(PaymentType.CASH, basket1.paymentType)
        assertEquals(LocalDateTime.of(2023, 11, 1, 9, 8, 0), basket1.paidAt)
        assertNull(basket1.variableSymbol)

        val basketItem1 = basketItemFinderService.findAllByBasketId(basket1.id)[0]
        assertNotNull(basketItem1)
        assertEquals(100, basketItem1.originalId)
        assertEquals(basket1.id, basketItem1.basketId)
        assertEquals(BasketItemType.TICKET, basketItem1.type)
        assertTrue(basketItem1.price isEqualTo 9.5.toBigDecimal())
        assertEquals(1, basketItem1.quantity)
        assertEquals(BRANCH_1.id, basketItem1.branchId)
        assertFalse(basketItem1.isCancelled)

        val ticket1 = ticketJooqFinderService.getById(basketItem1.ticketId!!)
        assertEquals(SCREENING_1.id, ticket1.screeningId)
        assertEquals("0001948987", ticket1.receiptNumber)
        assertFalse(ticket1.isGroupTicket)
        assertFalse(ticket1.isUsed)
        assertTrue(ticket1.includes3dGlasses)

        val basket2 = basketRepository.findAll()[1]
        assertTrue(basket2.totalPrice isEqualTo 7.5.toBigDecimal())
        assertEquals(BasketState.PAID, basket2.state)
        assertEquals(PaymentType.CASH, basket2.paymentType)
        assertEquals(LocalDateTime.of(2023, 11, 1, 9, 8, 0), basket2.paidAt)
        assertNull(basket2.variableSymbol)

        val basketItem2 = basketItemFinderService.findAllByBasketId(basket2.id)[0]
        assertNotNull(basketItem2)
        assertEquals(101, basketItem2.originalId)
        assertEquals(basket2.id, basketItem2.basketId)
        assertEquals(BasketItemType.TICKET, basketItem2.type)
        assertTrue(basketItem2.price isEqualTo 7.5.toBigDecimal())
        assertEquals(1, basketItem2.quantity)
        assertEquals(BRANCH_1.id, basketItem2.branchId)
        assertFalse(basketItem2.isCancelled)

        val ticket2 = ticketJooqFinderService.getById(basketItem2.ticketId!!)
        assertEquals(SCREENING_1.id, ticket2.screeningId)
        assertEquals("0001948988", ticket2.receiptNumber)
        assertFalse(ticket2.isGroupTicket)
        assertTrue(ticket2.isUsed)
        assertFalse(ticket2.includes3dGlasses)
    }

    @Test
    fun `test webCompleteWebBasketPayment - should complete online basket payment`() {
        every { applicationEventPublisherMock.publishEvent(any<BasketPaidEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<WebBasketPaymentCompletedEvent>()) } just Runs

        prepareOnlineBasketWithTickets()

        underTest.webCompleteWebBasketPayment(
            WebCompleteBasketPaymentCommand(
                basketId = 10000.toUUID(),
                variableSymbol = "123456789"
            )
        )

        val basket = basketRepository.findById(10000.toUUID()).get()
        assertEquals(BasketState.PAID_ONLINE, basket.state)
        assertEquals(POS_CONFIGURATION_3.id, basket.paymentPosConfigurationId)
        assertEquals("123456789", basket.variableSymbol)

        verifySequence {
            applicationEventPublisherMock.publishEvent(BasketPaidEvent(10000.toUUID(), null))
            applicationEventPublisherMock.publishEvent(WebBasketPaymentCompletedEvent(10000.toUUID()))
        }
    }

    private fun assertBasketEquals(expected: Basket, actual: Basket) {
        assertEquals(expected.tableId, actual.tableId)
        assertEquals(expected.state, actual.state)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
        assertEquals(expected.createdBy, actual.createdBy)
        assertEquals(expected.updatedBy, actual.updatedBy)
    }

    private fun prepareCreateBasketItemRequests(screeningId: UUID = SCREENING_1.id): List<CreateBasketItemRequest> {
        return listOf(
            CREATE_BASKET_ITEM_REQUEST_TICKET.copy(
                ticket = CreateTicketRequest(
                    ticketPrice = CreateTicketPriceRequest(
                        priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1
                    ),
                    reservation = CreateReservationRequest(
                        seatId = SEAT_1.id
                    ),
                    screeningId = screeningId
                )
            ),
            CREATE_BASKET_ITEM_REQUEST_PRODUCT
        )
    }

    private fun prepareOnlineBasketWithTickets() {
        val auditoriumLayout = integrationDataTestHelper.getAuditoriumLayout(
            originalId = 2,
            auditoriumId = integrationDataTestHelper.getAuditorium(
                id = 2.toUUID()
            )
        )

        val seat_1 = integrationDataTestHelper.getSeat(
            id = 10.toUUID(),
            originalId = 10,
            row = "1",
            number = "1",
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id
        )
        val screening = integrationDataTestHelper.getScreening(
            id = 100.toUUID(),
            originalId = 100,
            auditoriumId = auditoriumLayout.auditoriumId,
            auditoriumLayoutId = auditoriumLayout.id,
            movieId = integrationDataTestHelper.getMovie(
                originalId = 201,
                distributorId = integrationDataTestHelper.getDistributor(originalId = 202).id
            ).id,
            priceCategoryId = integrationDataTestHelper.getPriceCategory(
                id = 1000.toUUID(),
                originalId = 1000
            ).id
        )
        integrationDataTestHelper.getScreeningFee(
            screeningId = screening.id,
            originalScreeningId = screening.originalId
        )
        integrationDataTestHelper.getPriceCategoryItem(
            priceCategoryId = screening.priceCategoryId
        )

        val groupReservation = integrationDataTestHelper.getGroupReservation(
            id = 1000.toUUID(),
            type = GroupReservationType.ONLINE
        )
        val reservation1 = integrationDataTestHelper.getReservation(
            id = 100.toUUID(),
            originalId = 100,
            groupReservationId = groupReservation.id,
            screeningId = screening.id,
            seatId = seat_1.id,
            state = ReservationState.ONLINE_RESERVED
        )

        val ticket1 = integrationDataTestHelper.getTicket(
            id = 2222.toUUID(),
            originalId = 2222,
            screeningId = screening.id,
            reservationId = reservation1.id,
            ticketPriceId = integrationDataTestHelper.getTicketPrice(
                seatId = seat_1.id,
                screeningId = screening.id,
                totalPrice = BigDecimal.TEN
            ).id
        )

        val basket = integrationDataTestHelper.getBasket(
            id = 10000.toUUID(),
            state = BasketState.OPEN_ONLINE
        )
        integrationDataTestHelper.getBasketItem(
            basketId = basket.id,
            ticketId = ticket1.id,
            type = BasketItemType.TICKET,
            price = BigDecimal.TEN
        )
    }

    private fun createBasket(items: List<CreateBasketItemRequest>): Basket {
        val command = mapToInitBasketCommand(items = items)
        val created = underTest.initBasket(command)
        val createdBasket = basketFinderRepository.findNonDeletedById(created.id)

        assertNotNull(createdBasket)
        assertEquals(items.size, basketItemFinderService.findAllNonDeletedByBasketId(createdBasket.id).size)

        return createdBasket
    }
}

private val DISTRIBUTOR_1 = createDistributor()
private val AUDITORIUM_1 = createAuditorium(
    originalId = 1,
    code = "A",
    title = "SÁLA A - CINEMAX BRATISLAVA"
)
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(auditoriumId = AUDITORIUM_1.id)
private val MOVIE_1 = createMovie(
    originalId = 1,
    title = "Star Wars: Episode I – The Phantom Menace",
    releaseYear = 2001,
    distributorId = DISTRIBUTOR_1.id
)
private val PRICE_CATEGORY_1 = createPriceCategory(
    originalId = 1,
    title = "ARTMAX PO 17",
    active = true
)
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    title = "Dospely",
    price = 9.5.toBigDecimal()
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_2,
    title = "Student",
    price = 7.5.toBigDecimal()
)
private val PRICE_CATEGORY_ITEM_3 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_19,
    title = "Online student",
    price = 8.0.toBigDecimal()
)
private val PRICE_CATEGORY_ITEM_4 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_20,
    title = "Online dospely",
    price = 9.0.toBigDecimal()
)
private val SCREENING_1 = createScreeningWithCurrentDateTime(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_2 = createScreeningWithCurrentDateTime(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_3 = createScreeningWithCurrentDateTime(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_4 = createScreening(
    originalId = 4,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(2),
    time = INTEGRATION_TEST_DATE_TIME.toLocalTime().minusHours(2)
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id,
    surchargeVip = 0.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    surchargeImax = 0.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 0.toBigDecimal(),
    serviceFeeImax = 0.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal()
)
private val SCREENING_FEE_4 = createScreeningFee(
    originalScreeningId = 4,
    screeningId = SCREENING_4.id,
    surchargeVip = 0.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    surchargeImax = 0.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 0.toBigDecimal(),
    serviceFeeImax = 0.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal()
)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id
)
private val BASKET_1 = createBasket(
    tableId = null,
    totalPrice = 12.0.toBigDecimal(),
    state = BasketState.OPEN
)
private val BASKET_2_EMPTY = createBasket(
    tableId = null,
    totalPrice = 0.toBigDecimal(),
    state = BasketState.OPEN
)
private val BASKET_3 = createBasket(
    tableId = null,
    totalPrice = 12.0.toBigDecimal(),
    state = BasketState.OPEN
)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "A1",
    title = "Popcorn"
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    code = "01",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL",
    price = BigDecimal.TEN
)
private val PRODUCT_2 = createProduct(
    originalId = 2,
    code = "02",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Popcorn XXL - VIP app",
    price = BigDecimal.TEN
)
private val PRODUCT_3 = createProduct(
    originalId = 3,
    code = "03",
    productCategoryId = PRODUCT_CATEGORY_1.id,
    title = "Coca Cola 0.33 l",
    price = BigDecimal.TEN
)
private val CREATE_BASKET_ITEM_REQUEST_TICKET = CreateBasketItemRequest(
    type = BasketItemType.TICKET,
    quantity = 1,
    ticket = CreateTicketRequest(
        ticketPrice = CreateTicketPriceRequest(
            priceCategoryItemNumber = PRICE_CATEGORY_ITEM_1.number
        ),
        reservation = CreateReservationRequest(
            seatId = SEAT_1.id
        ),
        screeningId = SCREENING_1.id
    )
)
private val CREATE_BASKET_ITEM_REQUEST_PRODUCT = CreateBasketItemRequest(
    type = BasketItemType.PRODUCT,
    quantity = 2,
    product = CreateProductRequest(
        productId = PRODUCT_1.id
    )
)
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    originalId = 1,
    code = "01",
    title = "Kukurica",
    unit = ProductComponentUnit.KG,
    stockQuantity = 180.0.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    originalId = 1,
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = 0.25.toBigDecimal()
)
private val PRODUCT_COMPOSITION_2 = createProductComposition(
    originalId = 2,
    productId = PRODUCT_2.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = 0.25.toBigDecimal()
)
private val PRODUCT_COMPOSITION_3 = createProductComposition(
    originalId = 3,
    productId = PRODUCT_3.id,
    productComponentId = PRODUCT_COMPONENT_1.id,
    amount = 0.25.toBigDecimal()
)
private val POS_CONFIGURATION_1 = createPosConfiguration(
    type = PosConfigurationType.PHYSICAL,
    macAddress = "AA-BB-CC-DD-EE",
    title = "pokl1",
    receiptsDirectory = "/dummy/directory",
    ticketSalesEnabled = false,
    productModes = setOf(ProductMode.STANDARD),
    tablesType = TablesType.NO_TABLES,
    seatsEnabled = false
)
private val POS_CONFIGURATION_2 = createPosConfiguration(
    type = PosConfigurationType.PHYSICAL,
    macAddress = "BB-CC-DD-EE-FF",
    title = "pokl3",
    receiptsDirectory = "/dummy/directory",
    ticketSalesEnabled = false,
    productModes = setOf(ProductMode.STANDARD),
    tablesType = TablesType.NO_TABLES,
    seatsEnabled = false
)
private val POS_CONFIGURATION_3 = createPosConfiguration(
    type = PosConfigurationType.ONLINE,
    macAddress = "XX-XX-XX-XX-XX",
    title = "Internet",
    receiptsDirectory = "/dummy/directory",
    ticketSalesEnabled = false,
    productModes = setOf(),
    tablesType = TablesType.NO_TABLES,
    seatsEnabled = false
)
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01X",
    title = "Sleva 15%",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.PRIMARY,
    percentage = 15,
    amount = null,
    freeCount = null
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "02X",
    title = "Sleva 25%",
    type = TicketDiscountType.PERCENTAGE,
    usageType = TicketDiscountUsageType.SECONDARY,
    percentage = 25,
    amount = null,
    freeCount = null
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 123435,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    title = "VIP karta",
    code = "67900000"
)
private val DISCOUNT_CARD_2 = createDiscountCard(
    originalId = 67890,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    title = "FILM karta",
    code = "679567"
)
private val TABLE_1 = createTable()
private val EXISTING_RESERVATION_1 = createReservation(
    originalId = 1,
    screeningId = SCREENING_1.id,
    seatId = SEAT_1.id,
    state = ReservationState.UNAVAILABLE
)
private val EXISTING_RESERVATION_2 = createReservation(
    originalId = 2,
    screeningId = SCREENING_1.id,
    seatId = SEAT_2.id,
    state = ReservationState.UNAVAILABLE
)
private val EXISTING_RESERVATION_3 = createReservation(
    originalId = 3,
    screeningId = SCREENING_1.id,
    seatId = SEAT_3.id,
    state = ReservationState.UNAVAILABLE
)
private val BRANCH_1 = createBranch(
    originalId = 1,
    productSalesCode = 1,
    auditoriumOriginalCodePrefix = "5100",
    code = "510640",
    name = "Bratislava Bory"
)
private val BRANCH_2 = createBranch(
    originalId = 2,
    productSalesCode = 2,
    auditoriumOriginalCodePrefix = "5200",
    code = "522222",
    name = "Košice"
)
