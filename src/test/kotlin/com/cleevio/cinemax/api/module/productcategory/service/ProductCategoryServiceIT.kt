package com.cleevio.cinemax.api.module.productcategory.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.REDUCED_TAX_RATE
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.module.file.exception.FileNotFoundException
import com.cleevio.cinemax.api.module.file.service.FileRepository
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.service.ProductRepository
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.entity.ProductCategory
import com.cleevio.cinemax.api.module.productcategory.event.AdminProductCategoryCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.productcategory.event.AdminProductCategoryDeletedEvent
import com.cleevio.cinemax.api.module.productcategory.event.ProductCategoryCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.productcategory.event.toMessagingDeleteEvent
import com.cleevio.cinemax.api.module.productcategory.event.toMessagingEvent
import com.cleevio.cinemax.api.module.productcategory.exception.ProductForProductCategoryExistsException
import com.cleevio.cinemax.api.module.productcategory.service.command.DeleteProductCategoryCommand
import com.cleevio.cinemax.api.module.productcategory.service.command.MessagingDeleteProductCategoryCommand
import com.cleevio.cinemax.api.module.productcategory.service.command.UpdateProductCategoryOriginalIdCommand
import com.cleevio.cinemax.api.util.assertCommandToProductCategoryMapping
import com.cleevio.cinemax.api.util.createFile
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToMessagingCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.toUUID
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import jakarta.validation.ConstraintViolationException
import org.junit.jupiter.api.Assertions.assertDoesNotThrow
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.util.Optional
import kotlin.jvm.optionals.getOrNull
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class ProductCategoryServiceIT @Autowired constructor(
    private val underTest: ProductCategoryService,
    private val productCategoryRepository: ProductCategoryRepository,
    private val productCategoryJooqFinderService: ProductCategoryJooqFinderService,
    private val productRepository: ProductRepository,
    private val fileRepository: FileRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @Test
    fun `test syncCreateOrUpdateProductCategory - should create product category`() {
        fileRepository.save(FILE_1)

        val command = mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_1)
        underTest.syncCreateOrUpdateProductCategory(command.copy(id = null))

        val createdProductCategory =
            productCategoryJooqFinderService.findNonDeletedByOriginalId(PRODUCT_CATEGORY_1.originalId!!)
        assertNotNull(createdProductCategory)
        assertProductCategoryEquals(PRODUCT_CATEGORY_1, createdProductCategory)
    }

    @Test
    fun `test syncCreateOrUpdateProductCategory - one product category exists, set attributes - should update`() {
        fileRepository.saveAll(setOf(FILE_1, FILE_2))
        val command = mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_1)
        underTest.syncCreateOrUpdateProductCategory(command.copy(id = null))

        val createdProductCategory =
            productCategoryJooqFinderService.findNonDeletedByOriginalId(PRODUCT_CATEGORY_1.originalId!!)
        assertNotNull(createdProductCategory)
        assertProductCategoryEquals(PRODUCT_CATEGORY_1, createdProductCategory)

        underTest.syncCreateOrUpdateProductCategory(
            command.copy(
                id = createdProductCategory.id,
                order = Optional.of(66),
                imageFileId = Optional.of(FILE_2.id)
            )
        )

        val updatedProductCategory =
            productCategoryJooqFinderService.findNonDeletedByOriginalId(PRODUCT_CATEGORY_1.originalId!!)
        assertNotNull(updatedProductCategory)
        assertEquals(PRODUCT_CATEGORY_1.code, updatedProductCategory.code)
        assertEquals(PRODUCT_CATEGORY_1.title, updatedProductCategory.title)
        assertEquals(PRODUCT_CATEGORY_1.type, updatedProductCategory.type)
        assertEquals(66, updatedProductCategory.order)
        assertEquals(PRODUCT_CATEGORY_1.taxRate, updatedProductCategory.taxRate)
        assertEquals(PRODUCT_CATEGORY_1.hexColorCode, updatedProductCategory.hexColorCode)
        assertEquals(FILE_2.id, updatedProductCategory.imageFileId)

        assertTrue { updatedProductCategory.updatedAt.isAfter(createdProductCategory.updatedAt) }
    }

    @Test
    fun `test syncCreateOrUpdateProductCategory - one product category exists, set null or missing attributes - should update`() {
        fileRepository.save(FILE_1)
        val command = mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_1)
        underTest.syncCreateOrUpdateProductCategory(command.copy(id = null))

        val createdProductCategory =
            productCategoryJooqFinderService.findNonDeletedByOriginalId(PRODUCT_CATEGORY_1.originalId!!)
        assertNotNull(createdProductCategory)
        assertProductCategoryEquals(PRODUCT_CATEGORY_1, createdProductCategory)

        underTest.syncCreateOrUpdateProductCategory(
            command.copy(
                id = createdProductCategory.id,
                order = null,
                imageFileId = Optional.empty()
            )
        )

        val updatedProductCategory =
            productCategoryJooqFinderService.findNonDeletedByOriginalId(PRODUCT_CATEGORY_1.originalId!!)
        assertNotNull(updatedProductCategory)
        assertEquals(PRODUCT_CATEGORY_1.code, updatedProductCategory.code)
        assertEquals(PRODUCT_CATEGORY_1.title, updatedProductCategory.title)
        assertEquals(PRODUCT_CATEGORY_1.type, updatedProductCategory.type)
        assertEquals(PRODUCT_CATEGORY_1.order, updatedProductCategory.order)
        assertEquals(PRODUCT_CATEGORY_1.taxRate, updatedProductCategory.taxRate)
        assertEquals(PRODUCT_CATEGORY_1.hexColorCode, updatedProductCategory.hexColorCode)
        assertNull(updatedProductCategory.imageFileId)

        assertTrue { updatedProductCategory.updatedAt.isAfter(createdProductCategory.updatedAt) }
    }

    @Test
    fun `test syncCreateOrUpdateProductCategory - two product categories - should create two product categories`() {
        fileRepository.save(FILE_1)
        productCategoryRepository.save(PRODUCT_CATEGORY_1)

        val command = mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_2)
        underTest.syncCreateOrUpdateProductCategory(command)

        val categories = productCategoryJooqFinderService.findAll()
        assertEquals(categories.size, 2)
        assertProductCategoryEquals(
            PRODUCT_CATEGORY_1,
            categories.first { it.originalId == PRODUCT_CATEGORY_1.originalId }
        )
        assertProductCategoryEquals(
            PRODUCT_CATEGORY_2,
            categories.first { it.originalId == PRODUCT_CATEGORY_2.originalId }
        )
    }

    @Test
    fun `test adminCreateOrUpdateProductCategory - should create product category`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductCategoryCreatedOrUpdatedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<AdminProductCategoryCreatedOrUpdatedEvent>()) } just Runs

        fileRepository.save(FILE_1)

        val command = mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_3).copy(id = null, code = null)
        underTest.adminCreateOrUpdateProductCategory(command)

        val productCategories = productCategoryJooqFinderService.findAll()
        assertEquals(1, productCategories.size)

        val createdProductCategory = productCategories[0]
        assertNotNull(createdProductCategory)
        assertCommandToProductCategoryMapping(command, createdProductCategory, expectedCode = null)

        verify { applicationEventPublisherMock.publishEvent(ProductCategoryCreatedOrUpdatedEvent(createdProductCategory.id)) }
        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(
                createdProductCategory.toMessagingEvent(
                    Optional.ofNullable(FILE_1.originalName)
                )
            )
        }
    }

    @Test
    fun `test adminCreateOrUpdateProductCategory - one product category exists, set attributes - should update`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductCategoryCreatedOrUpdatedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<AdminProductCategoryCreatedOrUpdatedEvent>()) } just Runs

        fileRepository.saveAll(setOf(FILE_1, FILE_2))
        val createCommand = mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_3).copy(id = null, code = null)
        underTest.adminCreateOrUpdateProductCategory(createCommand)

        val productCategories = productCategoryJooqFinderService.findAll()
        assertEquals(1, productCategories.size)

        val createdProductCategory = productCategories[0]
        assertNotNull(createdProductCategory)
        assertCommandToProductCategoryMapping(createCommand, createdProductCategory, expectedCode = null)

        val updateCommand = createCommand.copy(
            id = createdProductCategory.id,
            title = "test update",
            order = Optional.of(20),
            imageFileId = Optional.of(FILE_2.id),
            taxRate = STANDARD_TAX_RATE
        )
        underTest.adminCreateOrUpdateProductCategory(updateCommand)

        val updatedProductCategory = productCategoryJooqFinderService.findNonDeletedById(createdProductCategory.id)!!
        assertCommandToProductCategoryMapping(updateCommand, updatedProductCategory, expectedCode = createdProductCategory.code)
        assertTrue { updatedProductCategory.updatedAt.isAfter(createdProductCategory.updatedAt) }

        verify(exactly = 2) {
            applicationEventPublisherMock.publishEvent(ProductCategoryCreatedOrUpdatedEvent(createdProductCategory.id))
        }
        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(
                createdProductCategory.toMessagingEvent(
                    Optional.ofNullable(FILE_1.originalName)
                )
            )
        }
        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(
                updatedProductCategory.toMessagingEvent(
                    Optional.ofNullable(FILE_2.originalName)
                )
            )
        }
    }

    @Test
    fun `test adminCreateOrUpdateProductCategory - one product category exists, set null or missing attrs - should update`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductCategoryCreatedOrUpdatedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<AdminProductCategoryCreatedOrUpdatedEvent>()) } just Runs

        fileRepository.save(FILE_1)
        val command = mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_3)
        underTest.adminCreateOrUpdateProductCategory(command.copy(id = null))

        val productCategories = productCategoryJooqFinderService.findAll()
        assertEquals(1, productCategories.size)

        val createdProductCategory = productCategories[0]
        assertNotNull(createdProductCategory)
        assertProductCategoryEquals(PRODUCT_CATEGORY_3, createdProductCategory)

        underTest.adminCreateOrUpdateProductCategory(
            command.copy(
                id = createdProductCategory.id,
                title = "test update",
                order = Optional.empty(),
                imageFileId = null,
                taxRate = STANDARD_TAX_RATE
            )
        )

        val updatedProductCategory = productCategoryJooqFinderService.findNonDeletedById(createdProductCategory.id)
        assertNotNull(updatedProductCategory)
        assertEquals(PRODUCT_CATEGORY_3.code, updatedProductCategory.code)
        assertEquals("test update", updatedProductCategory.title)
        assertEquals(PRODUCT_CATEGORY_3.type, updatedProductCategory.type)
        assertNull(updatedProductCategory.order)
        assertEquals(STANDARD_TAX_RATE, updatedProductCategory.taxRate)
        assertEquals(PRODUCT_CATEGORY_3.hexColorCode, updatedProductCategory.hexColorCode)
        assertEquals(FILE_1.id, updatedProductCategory.imageFileId)

        assertTrue { updatedProductCategory.updatedAt.isAfter(createdProductCategory.updatedAt) }
        verify(exactly = 2) {
            applicationEventPublisherMock.publishEvent(ProductCategoryCreatedOrUpdatedEvent(createdProductCategory.id))
        }
        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(
                createdProductCategory.toMessagingEvent(
                    Optional.ofNullable(FILE_1.originalName)
                )
            )
        }
        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(
                updatedProductCategory.toMessagingEvent(
                    Optional.ofNullable(FILE_1.originalName)
                )
            )
        }
    }

    @Test
    fun `test createOrUpdateProductCategory - command with blank string - should throw`() {
        val command = mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_2)
        val commandWithBlankString = command.copy(
            title = ""
        )
        assertThrows<ConstraintViolationException> {
            underTest.syncCreateOrUpdateProductCategory(commandWithBlankString)
        }
        assertThrows<ConstraintViolationException> {
            underTest.adminCreateOrUpdateProductCategory(commandWithBlankString)
        }
    }

    @Test
    fun `test createOrUpdateProductCategory - file does not exist - should throw`() {
        val command = mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_2)
        val commandWithNonExistentFileId = command.copy(
            id = null,
            imageFileId = Optional.of(1.toUUID())
        )
        assertThrows<FileNotFoundException> {
            underTest.syncCreateOrUpdateProductCategory(commandWithNonExistentFileId)
        }
        assertThrows<FileNotFoundException> {
            underTest.adminCreateOrUpdateProductCategory(commandWithNonExistentFileId)
        }
    }

    @ParameterizedTest
    @CsvSource(
        "23, true",
        "19, true",
        "5, true",
        "0, true",
        "-5, false",
        "15, false",
        "1, false"
    )
    fun `test createOrUpdateProductCategory - command correctly validates value of taxRate`(
        taxRate: Int,
        valid: Boolean,
    ) {
        val command = mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_1).copy(
            taxRate = taxRate,
            originalId = null,
            id = null
        )
        if (!valid) {
            assertThrows<ConstraintViolationException> {
                underTest.adminCreateOrUpdateProductCategory(command)
            }
        } else {
            every { applicationEventPublisherMock.publishEvent(any<ProductCategoryCreatedOrUpdatedEvent>()) } just Runs
            fileRepository.save(FILE_1)
            assertDoesNotThrow { underTest.adminCreateOrUpdateProductCategory(command) }
        }
    }

    @Test
    fun `test syncCreateOrUpdate - exists deleted by code - should not update productCategory`() {
        every { applicationEventPublisherMock.publishEvent(any<AdminProductCategoryDeletedEvent>()) } just Runs

        fileRepository.save(FILE_1)
        val command = mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_2)
        underTest.syncCreateOrUpdateProductCategory(command)

        val createdProductCategory =
            productCategoryJooqFinderService.findNonDeletedByOriginalId(PRODUCT_CATEGORY_2.originalId!!)
        assertNotNull(createdProductCategory)
        assertProductCategoryEquals(PRODUCT_CATEGORY_2, createdProductCategory)

        underTest.deleteProductCategory(DeleteProductCategoryCommand(createdProductCategory.id))

        assertNull(productCategoryRepository.findByCodeAndDeletedAtIsNull(PRODUCT_CATEGORY_2.code))
        assertNotNull(productCategoryRepository.findByCode(PRODUCT_CATEGORY_2.code))

        val updateCommand = mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_1)
            .copy(code = createdProductCategory.code)
        underTest.syncCreateOrUpdateProductCategory(updateCommand)

        val notUpdatedProductCategory = productCategoryRepository.findByCode(PRODUCT_CATEGORY_2.code)
        assertNotNull(notUpdatedProductCategory)
        assertProductCategoryEquals(PRODUCT_CATEGORY_2, notUpdatedProductCategory)

        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(createdProductCategory.toMessagingDeleteEvent())
        }
    }

    @Test
    fun `test deleteProductCategory - related deleted product exists - should delete product category`() {
        every { applicationEventPublisherMock.publishEvent(any<AdminProductCategoryDeletedEvent>()) } just Runs

        val command = mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_2)
        underTest.syncCreateOrUpdateProductCategory(command)

        val categories = productCategoryJooqFinderService.findAll()
        assertEquals(categories.size, 1)
        val createdCategory = categories[0]

        productRepository.save(PRODUCT_1.apply { markDeleted() })

        underTest.deleteProductCategory(DeleteProductCategoryCommand(productCategoryId = createdCategory.id))
        assertNotNull(productCategoryRepository.findById(createdCategory.id)).getOrNull()?.also {
            assertTrue(it.isDeleted())
        }
        verify(exactly = 1) {
            applicationEventPublisherMock.publishEvent(createdCategory.toMessagingDeleteEvent())
        }
    }

    @Test
    fun `test deleteProductCategory - related non-deleted product exists - should throw`() {
        val command = mapToCreateOrUpdateProductCategoryCommand(PRODUCT_CATEGORY_2)
        underTest.syncCreateOrUpdateProductCategory(command)

        val categories = productCategoryJooqFinderService.findAll()
        assertEquals(categories.size, 1)
        val createdCategory = categories[0]

        productRepository.save(PRODUCT_1)

        assertThrows<ProductForProductCategoryExistsException> {
            underTest.deleteProductCategory(DeleteProductCategoryCommand(productCategoryId = createdCategory.id))
        }
    }

    @Test
    fun `test updateProductCategoryOriginalId - should correctly update in db`() {
        val productCategory = createProductCategory(originalId = null).also {
            productCategoryRepository.save(it)
        }

        underTest.updateProductCategoryOriginalId(
            UpdateProductCategoryOriginalIdCommand(
                productCategoryId = productCategory.id,
                originalId = 5
            )
        )

        assertEquals(5, productCategoryRepository.findByIdAndDeletedAtIsNull(productCategory.id)?.originalId)
    }

    @Test
    fun `test messagingCreateOrUpdateProductCategory - does not exist by original code - should create productCategory`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductCategoryCreatedOrUpdatedEvent>()) } just Runs

        val command = mapToMessagingCreateOrUpdateProductCategoryCommand(
            productCategory = createProductCategory(),
            imageFileOriginalName = null
        )

        underTest.messagingCreateOrUpdateProductCategory(command)

        val productCategories = productCategoryRepository.findAll()
        assertEquals(1, productCategories.size)

        productCategories[0].let {
            assertEquals(command.code, it.code)
            assertEquals(command.title, it.title)
            assertEquals(command.type, it.type)
            assertEquals(command.order?.getOrNull(), it.order)
            assertEquals(command.taxRate, it.taxRate)
            assertEquals(command.hexColorCode, it.hexColorCode)
            assertNull(it.imageFileId)

            verify(exactly = 1) {
                applicationEventPublisherMock.publishEvent(ProductCategoryCreatedOrUpdatedEvent(it.id))
            }
        }
    }

    @Test
    fun `test messagingCreateOrUpdateProductCategory - exists by original code - should update productCategory`() {
        every { applicationEventPublisherMock.publishEvent(any<ProductCategoryCreatedOrUpdatedEvent>()) } just Runs

        val existing = createProductCategory()
        productCategoryRepository.save(existing)

        val updateCommand = mapToMessagingCreateOrUpdateProductCategoryCommand(
            productCategory = existing,
            imageFileOriginalName = FILE_2.originalName
        ).copy(
            title = "Updated Category",
            type = ProductCategoryType.DISCOUNT,
            order = Optional.of(50),
            taxRate = REDUCED_TAX_RATE,
            hexColorCode = "#001122"
        )

        fileRepository.save(FILE_2)

        underTest.messagingCreateOrUpdateProductCategory(updateCommand)

        productCategoryRepository.findAll().first { it.code == existing.code }.let {
            assertEquals("Updated Category", it.title)
            assertEquals(50, it.order)
            assertEquals(REDUCED_TAX_RATE, it.taxRate)
            assertEquals("#001122", it.hexColorCode)
            assertEquals(ProductCategoryType.DISCOUNT, it.type)
            assertEquals(FILE_2.id, it.imageFileId)

            verify(exactly = 1) {
                applicationEventPublisherMock.publishEvent(ProductCategoryCreatedOrUpdatedEvent(it.id))
            }
        }
    }

    @Test
    fun `test messagingDeleteProductCategory - should soft delete`() {
        every { applicationEventPublisherMock.publishEvent(any()) } just Runs

        val category = createProductCategory().also { productCategoryRepository.save(it) }

        underTest.messagingDeleteProductCategory(
            MessagingDeleteProductCategoryCommand(category.code)
        )

        productCategoryRepository.findAll().first { it.id == category.id }.let {
            assertNotNull(it)
            assertTrue(it.isDeleted())
        }
    }

    private fun assertProductCategoryEquals(expected: ProductCategory, actual: ProductCategory) {
        assertEquals(expected.originalId, actual.originalId)
        assertEquals(expected.code, actual.code)
        assertEquals(expected.title, actual.title)
        assertEquals(expected.type, actual.type)
        assertEquals(expected.order, actual.order)
        assertEquals(expected.taxRate, actual.taxRate)
        assertEquals(expected.hexColorCode, actual.hexColorCode)
        assertEquals(expected.imageFileId, actual.imageFileId)
        assertNotNull(actual.createdAt)
        assertNotNull(actual.updatedAt)
    }
}

private val FILE_1 = createFile(
    originalName = "KITKAT.PNG",
    extension = "PNG"
)
private val FILE_2 = createFile(
    originalId = 2,
    originalName = "ZUVACKY.PNG",
    extension = "PNG"
)
private val PRODUCT_CATEGORY_1 = createProductCategory(
    originalId = 1,
    code = "01",
    title = "Nápoje",
    type = ProductCategoryType.PRODUCT,
    order = 10,
    taxRate = STANDARD_TAX_RATE,
    hexColorCode = "#001122",
    imageFileId = FILE_1.id
)
private val PRODUCT_CATEGORY_2 = createProductCategory(
    originalId = 2,
    code = "94",
    title = "Pochutiny",
    type = ProductCategoryType.PRODUCT,
    order = 11,
    taxRate = STANDARD_TAX_RATE,
    hexColorCode = "#aabbcc"
)
private val PRODUCT_CATEGORY_3 = createProductCategory(
    originalId = null,
    code = "03",
    title = "Zlava karta",
    type = ProductCategoryType.DISCOUNT,
    order = 111,
    taxRate = STANDARD_TAX_RATE,
    hexColorCode = "#aabbcc",
    imageFileId = FILE_1.id
)
private val PRODUCT_1 = createProduct(
    originalId = 1,
    code = "01",
    productCategoryId = PRODUCT_CATEGORY_2.id,
    title = "Coca Cola 0.33l",
    order = 23,
    type = ProductType.PRODUCT,
    price = BigDecimal.valueOf(3.5),
    stockQuantityThreshold = 10,
    imageFileId = null
)
