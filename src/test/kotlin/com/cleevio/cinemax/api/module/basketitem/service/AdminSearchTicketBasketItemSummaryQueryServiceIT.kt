package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.INTEGRATION_TEST_DATE_TIME
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutService
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basket.service.BasketReceiptNumbersService
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basket.service.BasketService
import com.cleevio.cinemax.api.module.basket.service.command.CompleteBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.GenerateBasketReceiptNumbersCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitiateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchTicketBasketItemsFilter
import com.cleevio.cinemax.api.module.basketitem.controller.dto.AdminSearchTicketBasketItemsSummaryResponse
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.service.command.CancelBasketItemsCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.DeleteBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminSearchTicketBasketItemsQuery
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageService
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.command.UpdateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.distributor.service.DistributorService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.PriceCategoryItemService
import com.cleevio.cinemax.api.module.product.service.ProductService
import com.cleevio.cinemax.api.module.productcategory.service.ProductCategoryService
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentService
import com.cleevio.cinemax.api.module.productcomponentcategory.service.ProductComponentCategoryRepository
import com.cleevio.cinemax.api.module.productcomposition.service.ProductCompositionService
import com.cleevio.cinemax.api.module.production.entity.Production
import com.cleevio.cinemax.api.module.production.service.ProductionRepository
import com.cleevio.cinemax.api.module.reservation.event.ReservationCreatedEvent
import com.cleevio.cinemax.api.module.reservation.event.ScreeningWithNonDefaultLayoutSyncedFromMssqlEvent
import com.cleevio.cinemax.api.module.screening.service.ScreeningService
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeService
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesService
import com.cleevio.cinemax.api.module.screeningtypes.service.command.DeleteAndCreateScreeningTypesCommand
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.seat.service.SeatService
import com.cleevio.cinemax.api.module.technology.entity.Technology
import com.cleevio.cinemax.api.module.technology.service.TechnologyRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketService
import com.cleevio.cinemax.api.module.ticket.service.command.UpdateTicketOriginalIdCommand
import com.cleevio.cinemax.api.module.ticket.service.command.UpdateTicketUsedCommand
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountService
import com.cleevio.cinemax.api.util.assertTicketBasketItemSummaryEquals
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createDiscountCard
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createPriceCategoryItem
import com.cleevio.cinemax.api.util.createProduct
import com.cleevio.cinemax.api.util.createProductCategory
import com.cleevio.cinemax.api.util.createProductComponent
import com.cleevio.cinemax.api.util.createProductComponentCategory
import com.cleevio.cinemax.api.util.createProductComposition
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createScreeningFee
import com.cleevio.cinemax.api.util.createScreeningType
import com.cleevio.cinemax.api.util.createScreeningTypes
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicketBasketItemRequest
import com.cleevio.cinemax.api.util.createTicketDiscount
import com.cleevio.cinemax.api.util.mapToCreateBasketItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateAuditoriumLayoutCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateDistributorCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateMovieCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePosConfigurationCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCategoryCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductComponentCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateProductCompositionCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateScreeningTypeCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateSeatCommand
import com.cleevio.cinemax.api.util.mapToCreateOrUpdateTicketDiscountCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.util.mapToSyncCreateOrUpdateProductCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Pageable
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

class AdminSearchTicketBasketItemSummaryQueryServiceIT @Autowired constructor(
    private val underTest: AdminSearchTicketBasketItemSummaryQueryService,
    private val posConfigurationService: PosConfigurationService,
    private val auditoriumService: AuditoriumService,
    private val auditoriumLayoutService: AuditoriumLayoutService,
    private val seatService: SeatService,
    private val distributorService: DistributorService,
    private val movieService: MovieService,
    private val productionRepository: ProductionRepository,
    private val technologyRepository: TechnologyRepository,
    private val screeningService: ScreeningService,
    private val screeningTypeService: ScreeningTypeService,
    private val screeningTypesService: ScreeningTypesService,
    private val priceCategoryService: PriceCategoryService,
    private val priceCategoryItemService: PriceCategoryItemService,
    private val screeningFeeService: ScreeningFeeService,
    private val ticketDiscountService: TicketDiscountService,
    private val discountCardService: DiscountCardService,
    private val discountCardUsageService: DiscountCardUsageService,
    private val basketRepository: BasketRepository,
    private val basketService: BasketService,
    private val basketReceiptNumbersService: BasketReceiptNumbersService,
    private val basketItemService: BasketItemService,
    private val basketItemRepository: BasketItemRepository,
    private val productService: ProductService,
    private val productCategoryService: ProductCategoryService,
    private val productComponentService: ProductComponentService,
    private val productCompositionService: ProductCompositionService,
    private val ticketService: TicketService,
    private val productComponentCategoryRepository: ProductComponentCategoryRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun setUp() {
        every {
            reservationMssqlFinderServiceMock.findByOriginalScreeningIdAndOriginalSeatId(
                any(),
                any()
            )
        } returns null
        every { applicationEventPublisherMock.publishEvent(any<ReservationCreatedEvent>()) } just Runs
        every { applicationEventPublisherMock.publishEvent(any<ScreeningWithNonDefaultLayoutSyncedFromMssqlEvent>()) } just Runs

        setOf(POS_CONFIGURATION_1, POS_CONFIGURATION_2).forEach {
            posConfigurationService.createOrUpdatePosConfiguration(mapToCreateOrUpdatePosConfigurationCommand(it))
        }
        setOf(AUDITORIUM_1, AUDITORIUM_2, AUDITORIUM_3).forEach {
            auditoriumService.syncCreateOrUpdateAuditorium(mapToCreateOrUpdateAuditoriumCommand(it))
        }
        setOf(AUDITORIUM_LAYOUT_1, AUDITORIUM_LAYOUT_2, AUDITORIUM_LAYOUT_3).forEach {
            auditoriumLayoutService.syncCreateOrUpdateAuditoriumLayout(mapToCreateOrUpdateAuditoriumLayoutCommand(it))
        }
        setOf(SEAT_1, SEAT_2, SEAT_3, SEAT_4, SEAT_5, SEAT_6, SEAT_7, SEAT_8, SEAT_9, SEAT_10).forEach {
            seatService.createOrUpdateSeat(mapToCreateOrUpdateSeatCommand(it))
        }

        with(technologyRepository) {
            deleteAll()
            saveAll(setOf(TECHNOLOGY_1, TECHNOLOGY_2, TECHNOLOGY_3))
        }
        with(productionRepository) {
            deleteAll()
            saveAll(setOf(PRODUCTION_1, PRODUCTION_2))
        }
        setOf(DISTRIBUTOR_1, DISTRIBUTOR_2).forEach {
            distributorService.syncCreateOrUpdateDistributor(mapToCreateOrUpdateDistributorCommand(it))
        }
        setOf(MOVIE_1, MOVIE_2, MOVIE_3, MOVIE_4).forEach {
            movieService.syncCreateOrUpdateMovie(mapToCreateOrUpdateMovieCommand(it))
        }

        setOf(PRICE_CATEGORY_1, PRICE_CATEGORY_2).forEach {
            priceCategoryService.syncCreateOrUpdatePriceCategory(mapToSyncCreateOrUpdatePriceCategoryCommand(it))
        }
        setOf(PRICE_CATEGORY_ITEM_1, PRICE_CATEGORY_ITEM_2, PRICE_CATEGORY_ITEM_3, PRICE_CATEGORY_ITEM_4).forEach {
            priceCategoryItemService.createOrUpdatePriceCategoryItem(
                mapToCreateOrUpdatePriceCategoryItemCommand(it, it.priceCategoryId)
            )
        }
        // SCREENING_6 is cancelled, shouldn't appear anywhere
        // SCREENING_4 is necessary for non-paid BASKET_5
        setOf(SCREENING_4, SCREENING_6).forEach {
            screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(it))
        }
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_4))
        screeningTypeService.syncCreateOrUpdateScreeningType(mapToCreateOrUpdateScreeningTypeCommand(SCREENING_TYPE_1))
        screeningTypesService.deleteAndCreateScreeningTypes(
            DeleteAndCreateScreeningTypesCommand(
                SCREENING_TYPES_1.screeningId,
                setOf(SCREENING_TYPES_1.screeningTypeId)
            )
        )

        setOf(TICKET_DISCOUNT_1, TICKET_DISCOUNT_2, TICKET_DISCOUNT_3, TICKET_DISCOUNT_4).forEach {
            ticketDiscountService.syncCreateOrUpdateTicketDiscount(mapToCreateOrUpdateTicketDiscountCommand(it))
        }
        setOf(DISCOUNT_CARD_1, DISCOUNT_CARD_2).forEach {
            discountCardService.createOrUpdateDiscountCard(mapToCreateOrUpdateDiscountCardCommand(it))
        }

        productCategoryService.syncCreateOrUpdateProductCategory(
            mapToCreateOrUpdateProductCategoryCommand(
                PRODUCT_CATEGORY_1
            )
        )
        productComponentCategoryRepository.save(PRODUCT_COMPONENT_CATEGORY_1)
        productComponentService.syncCreateOrUpdateProductComponent(
            mapToCreateOrUpdateProductComponentCommand(
                PRODUCT_COMPONENT_1
            )
        )
        productService.syncCreateOrUpdateProduct(mapToSyncCreateOrUpdateProductCommand(PRODUCT_1, PRODUCT_CATEGORY_1.id))
        productCompositionService.createOrUpdateProductComposition(
            mapToCreateOrUpdateProductCompositionCommand(PRODUCT_COMPOSITION_1)
        )

        // non-paid basket - shouldn't appear anywhere
        BASKET_5.createBasket()
        BASKET_5.addToBasket(12, SEAT_6.id, SCREENING_4.id, PriceCategoryItemNumber.PRICE_1)
    }

    @Test
    fun `test AdminSearchTicketBasketItemsQuery - no filter, screening with+without sold tickets, tickets without discounts - should calculate correctly`() {
        setOf(SCREENING_1, SCREENING_5).forEach {
            screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(it))
        }
        setOf(SCREENING_FEE_1, SCREENING_FEE_5).forEach {
            screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(it))
        }
        setOf(SCREENING_TYPE_2).forEach {
            screeningTypeService.syncCreateOrUpdateScreeningType(mapToCreateOrUpdateScreeningTypeCommand(it))
        }
        setOf(SCREENING_TYPES_2).forEach {
            screeningTypesService.deleteAndCreateScreeningTypes(
                DeleteAndCreateScreeningTypesCommand(it.screeningId, setOf(it.screeningTypeId))
            )
        }
        // two tickets, no discounts, two base prices
        //     ticket 1: basic price = 7, auditorium IMAX surcharge = 1
        //     ticket 2: basic price = 6, auditorium IMAX surcharge = 1
        BASKET_1.createBasket()
        BASKET_1.addToBasket(1, SEAT_1.id, SCREENING_1.id, PriceCategoryItemNumber.PRICE_1)
        BASKET_1.addToBasket(2, SEAT_2.id, SCREENING_1.id, PriceCategoryItemNumber.PRICE_2)
        BASKET_1.payBasket(PaymentType.CASH, POS_CONFIGURATION_1.id)

        val result = underTest(
            AdminSearchTicketBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminSearchTicketBasketItemsFilter()
            )
        )

        assertTicketBasketItemSummaryEquals(
            expected = AdminSearchTicketBasketItemsSummaryResponse(
                sales = AdminSearchTicketBasketItemsSummaryResponse.SalesResponse(
                    screeningsCount = 3,
                    ticketsCount = 2,
                    ticketsUsedCount = 0,
                    salesCash = 15.toBigDecimal(),
                    salesCashless = 0.toBigDecimal(),
                    grossSales = 15.toBigDecimal(),
                    netSales = 4.25.toBigDecimal(),
                    proDeduction = 0.15.toBigDecimal(),
                    filmFondDeduction = 0.3.toBigDecimal(),
                    distributorDeduction = 7.5.toBigDecimal(),
                    taxAmount = 2.8.toBigDecimal(),
                    cancelledTicketsCount = 0,
                    cancelledTicketsExpense = 0.toBigDecimal()
                ),
                technologies = AdminSearchTicketBasketItemsSummaryResponse.TechnologiesResponse(
                    serviceFees = AdminSearchTicketBasketItemsSummaryResponse.ServiceFeesResponse(
                        general = 0.toBigDecimal(),
                        vip = 0.toBigDecimal(),
                        premiumPlus = 0.toBigDecimal(),
                        imax = 0.toBigDecimal(),
                        dolbyAtmos = 0.toBigDecimal(),
                        total = 0.toBigDecimal()
                    ),
                    surcharges = AdminSearchTicketBasketItemsSummaryResponse.SurchargesResponse(
                        vip = 0.toBigDecimal(),
                        dBox = 0.toBigDecimal(),
                        premiumPlus = 0.toBigDecimal(),
                        imax = 2.toBigDecimal(),
                        dolbyAtmos = 0.toBigDecimal(),
                        total = 2.toBigDecimal()
                    ),
                    counts = AdminSearchTicketBasketItemsSummaryResponse.CountsResponse(
                        general = 0,
                        vip = 0,
                        dBox = 0,
                        premiumPlus = 0,
                        imax = 2,
                        dolbyAtmos = 0,
                        total = 2
                    )
                ),
                discounts = listOf(
                    AdminSearchTicketBasketItemsSummaryResponse.DiscountResponse(
                        title = NO_DISCOUNT_TITLE,
                        count = 1 // PRICE_2 for PRICE_CATEGORY_2 is discounted
                    )
                )
            ),
            result
        )
    }

    @Test
    fun `test AdminSearchTicketBasketItemsQuery - screening types filter - should calculate correctly`() {
        setOf(SCREENING_1, SCREENING_5).forEach {
            screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(it))
        }
        setOf(SCREENING_FEE_1, SCREENING_FEE_5).forEach {
            screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(it))
        }
        setOf(SCREENING_TYPE_2).forEach {
            screeningTypeService.syncCreateOrUpdateScreeningType(mapToCreateOrUpdateScreeningTypeCommand(it))
        }
        setOf(SCREENING_TYPES_2).forEach {
            screeningTypesService.deleteAndCreateScreeningTypes(
                DeleteAndCreateScreeningTypesCommand(it.screeningId, setOf(it.screeningTypeId))
            )
        }

        BASKET_1.createBasket()
        BASKET_1.addToBasket(1, SEAT_1.id, SCREENING_1.id, PriceCategoryItemNumber.PRICE_1)
        BASKET_1.addToBasket(2, SEAT_2.id, SCREENING_1.id, PriceCategoryItemNumber.PRICE_2)
        BASKET_1.payBasket(PaymentType.CASH, POS_CONFIGURATION_1.id)

        val result = underTest(
            AdminSearchTicketBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminSearchTicketBasketItemsFilter(
                    screeningTypeIds = setOf(SCREENING_TYPE_2.id)
                )
            )
        )

        assertTicketBasketItemSummaryEquals(
            expected = AdminSearchTicketBasketItemsSummaryResponse(
                sales = AdminSearchTicketBasketItemsSummaryResponse.SalesResponse(
                    screeningsCount = 1,
                    ticketsCount = 2,
                    ticketsUsedCount = 0,
                    salesCash = 15.toBigDecimal(),
                    salesCashless = 0.toBigDecimal(),
                    grossSales = 15.toBigDecimal(),
                    netSales = 4.25.toBigDecimal(),
                    proDeduction = 0.15.toBigDecimal(),
                    filmFondDeduction = 0.3.toBigDecimal(),
                    distributorDeduction = 7.5.toBigDecimal(),
                    taxAmount = 2.8.toBigDecimal(),
                    cancelledTicketsCount = 0,
                    cancelledTicketsExpense = 0.toBigDecimal()
                ),
                technologies = AdminSearchTicketBasketItemsSummaryResponse.TechnologiesResponse(
                    serviceFees = AdminSearchTicketBasketItemsSummaryResponse.ServiceFeesResponse(
                        general = 0.toBigDecimal(),
                        vip = 0.toBigDecimal(),
                        premiumPlus = 0.toBigDecimal(),
                        imax = 0.toBigDecimal(),
                        dolbyAtmos = 0.toBigDecimal(),
                        total = 0.toBigDecimal()
                    ),
                    surcharges = AdminSearchTicketBasketItemsSummaryResponse.SurchargesResponse(
                        vip = 0.toBigDecimal(),
                        dBox = 0.toBigDecimal(),
                        premiumPlus = 0.toBigDecimal(),
                        imax = 2.toBigDecimal(),
                        dolbyAtmos = 0.toBigDecimal(),
                        total = 2.toBigDecimal()
                    ),
                    counts = AdminSearchTicketBasketItemsSummaryResponse.CountsResponse(
                        general = 0,
                        vip = 0,
                        dBox = 0,
                        premiumPlus = 0,
                        imax = 2,
                        dolbyAtmos = 0,
                        total = 2
                    )
                ),
                discounts = listOf(
                    AdminSearchTicketBasketItemsSummaryResponse.DiscountResponse(
                        title = NO_DISCOUNT_TITLE,
                        count = 1 // PRICE_2 for PRICE_CATEGORY_2 is discounted
                    )
                )
            ),
            result
        )
    }

    @Test
    fun `test AdminSearchTicketBasketItemsQuery - no filter, screenings with+without sold tickets, tickets used with discounts applied - should calculate correctly`() {
        setOf(SCREENING_1, SCREENING_2, SCREENING_5).forEach {
            screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(it))
        }
        setOf(SCREENING_FEE_1, SCREENING_FEE_2, SCREENING_FEE_5).forEach {
            screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(it))
        }
        // two tickets, each with one applied ticket discount
        //     ticket 1: basic price = 5, no surcharges or fees, discount percentage 15
        //     ticket 2: basic price = 4, no surcharges or fees, discount percentage 30
        BASKET_2.createBasket()
        BASKET_2.addToBasket(
            originalId = 5,
            seatId = SEAT_5.id,
            screeningId = SCREENING_2.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
            primaryTicketDiscountId = TICKET_DISCOUNT_1.id,
            isUsed = true,
            ticketOriginalId = 1
        )
        BASKET_2.addToBasket(
            originalId = 6,
            seatId = SEAT_6.id,
            screeningId = SCREENING_2.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_2,
            secondaryTicketDiscountId = TICKET_DISCOUNT_2.id,
            isUsed = true,
            ticketOriginalId = 2
        )
        BASKET_2.payBasket(PaymentType.CASHLESS, POS_CONFIGURATION_1.id)

        val result = underTest(
            AdminSearchTicketBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminSearchTicketBasketItemsFilter()
            )
        )

        assertTicketBasketItemSummaryEquals(
            expected = AdminSearchTicketBasketItemsSummaryResponse(
                sales = AdminSearchTicketBasketItemsSummaryResponse.SalesResponse(
                    screeningsCount = 4,
                    ticketsCount = 2,
                    ticketsUsedCount = 2,
                    salesCash = 0.toBigDecimal(),
                    salesCashless = 7.05.toBigDecimal(),
                    grossSales = 7.05.toBigDecimal(),
                    netSales = 3.474.toBigDecimal(),
                    proDeduction = 0.0705.toBigDecimal(),
                    filmFondDeduction = 0.0705.toBigDecimal(),
                    distributorDeduction = 2.115.toBigDecimal(),
                    taxAmount = 1.32.toBigDecimal(),
                    cancelledTicketsCount = 0,
                    cancelledTicketsExpense = 0.toBigDecimal()
                ),
                technologies = AdminSearchTicketBasketItemsSummaryResponse.TechnologiesResponse(
                    serviceFees = AdminSearchTicketBasketItemsSummaryResponse.ServiceFeesResponse(
                        general = 0.toBigDecimal(),
                        vip = 0.toBigDecimal(),
                        premiumPlus = 0.toBigDecimal(),
                        imax = 0.toBigDecimal(),
                        dolbyAtmos = 0.toBigDecimal(),
                        total = 0.toBigDecimal()
                    ),
                    surcharges = AdminSearchTicketBasketItemsSummaryResponse.SurchargesResponse(
                        vip = 0.toBigDecimal(),
                        dBox = 0.toBigDecimal(),
                        premiumPlus = 0.toBigDecimal(),
                        imax = 0.toBigDecimal(),
                        dolbyAtmos = 0.toBigDecimal(),
                        total = 0.toBigDecimal()
                    ),
                    counts = AdminSearchTicketBasketItemsSummaryResponse.CountsResponse(
                        general = 0,
                        vip = 0,
                        dBox = 0,
                        premiumPlus = 0,
                        imax = 0,
                        dolbyAtmos = 0,
                        total = 0
                    )
                ),
                discounts = listOf(
                    AdminSearchTicketBasketItemsSummaryResponse.DiscountResponse(
                        title = NO_DISCOUNT_TITLE,
                        count = 0
                    ),
                    AdminSearchTicketBasketItemsSummaryResponse.DiscountResponse(
                        title = TICKET_DISCOUNT_1.title!!,
                        count = 1
                    ),
                    AdminSearchTicketBasketItemsSummaryResponse.DiscountResponse(
                        title = TICKET_DISCOUNT_2.title!!,
                        count = 1
                    )
                )
            ),
            result
        )
    }

    @Test
    fun `test AdminSearchTicketBasketItemsQuery - no filter, only screenings with sold tickets, ticket used - should calculate correctly`() {
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1))

        // two tickets, no discounts, two base prices
        BASKET_1.createBasket()
        BASKET_1.addToBasket(1, SEAT_1.id, SCREENING_1.id, PriceCategoryItemNumber.PRICE_1)
        BASKET_1.addToBasket(2, SEAT_2.id, SCREENING_1.id, PriceCategoryItemNumber.PRICE_2)
        BASKET_1.payBasket(PaymentType.CASH, POS_CONFIGURATION_1.id)

        val result = underTest(
            AdminSearchTicketBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminSearchTicketBasketItemsFilter()
            )
        )

        assertTicketBasketItemSummaryEquals(
            expected = AdminSearchTicketBasketItemsSummaryResponse(
                sales = AdminSearchTicketBasketItemsSummaryResponse.SalesResponse(
                    screeningsCount = 2, // SCREENING_6 is cancelled
                    ticketsCount = 2,
                    ticketsUsedCount = 0,
                    salesCash = 15.toBigDecimal(),
                    salesCashless = 0.toBigDecimal(),
                    grossSales = 15.toBigDecimal(),
                    netSales = 4.25.toBigDecimal(),
                    proDeduction = 0.15.toBigDecimal(),
                    filmFondDeduction = 0.3.toBigDecimal(),
                    distributorDeduction = 7.5.toBigDecimal(),
                    taxAmount = 2.8.toBigDecimal(),
                    cancelledTicketsCount = 0,
                    cancelledTicketsExpense = 0.toBigDecimal()
                ),
                technologies = AdminSearchTicketBasketItemsSummaryResponse.TechnologiesResponse(
                    serviceFees = AdminSearchTicketBasketItemsSummaryResponse.ServiceFeesResponse(
                        general = 0.toBigDecimal(),
                        vip = 0.toBigDecimal(),
                        premiumPlus = 0.toBigDecimal(),
                        imax = 0.toBigDecimal(),
                        dolbyAtmos = 0.toBigDecimal(),
                        total = 0.toBigDecimal()
                    ),
                    surcharges = AdminSearchTicketBasketItemsSummaryResponse.SurchargesResponse(
                        vip = 0.toBigDecimal(),
                        dBox = 0.toBigDecimal(),
                        premiumPlus = 0.toBigDecimal(),
                        imax = 2.toBigDecimal(),
                        dolbyAtmos = 0.toBigDecimal(),
                        total = 2.toBigDecimal()
                    ),
                    counts = AdminSearchTicketBasketItemsSummaryResponse.CountsResponse(
                        general = 0,
                        vip = 0,
                        dBox = 0,
                        premiumPlus = 0,
                        imax = 2,
                        dolbyAtmos = 0,
                        total = 2
                    )
                ),
                discounts = listOf(
                    AdminSearchTicketBasketItemsSummaryResponse.DiscountResponse(
                        title = NO_DISCOUNT_TITLE,
                        count = 1 // PRICE_2 for PRICE_CATEGORY_2 is discounted
                    )
                )
            ),
            result
        )
    }

    @Test
    fun `test AdminSearchTicketBasketItemsQuery - no filter, only cancelled ticket exists - should calculate correctly`() {
        screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(SCREENING_1))
        screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(SCREENING_FEE_1))

        // cancelled and deleted ticket
        BASKET_3.createBasket()
        val basket6Item1 = BASKET_3.addToBasket(9, SEAT_9.id, SCREENING_4.id, PriceCategoryItemNumber.PRICE_2)
        BASKET_3.cancelBasketItem(10, basket6Item1.id)
        val basket6Item2 = BASKET_3.addToBasket(11, SEAT_10.id, SCREENING_4.id, PriceCategoryItemNumber.PRICE_2)
        BASKET_3.deleteBasketItem(basket6Item2.id)
        BASKET_3.payBasket(PaymentType.CASH, POS_CONFIGURATION_2.id)

        val result = underTest(
            AdminSearchTicketBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminSearchTicketBasketItemsFilter()
            )
        )

        assertTicketBasketItemSummaryEquals(
            expected = AdminSearchTicketBasketItemsSummaryResponse(
                sales = AdminSearchTicketBasketItemsSummaryResponse.SalesResponse(
                    screeningsCount = 2,
                    ticketsCount = 0,
                    ticketsUsedCount = 0,
                    salesCash = 0.toBigDecimal(),
                    salesCashless = 0.toBigDecimal(),
                    grossSales = 0.toBigDecimal(),
                    netSales = (-2.65).toBigDecimal(),
                    proDeduction = 0.05.toBigDecimal(),
                    filmFondDeduction = 0.1.toBigDecimal(),
                    distributorDeduction = 2.5.toBigDecimal(),
                    taxAmount = 0.toBigDecimal(),
                    cancelledTicketsCount = 1,
                    cancelledTicketsExpense = 6.toBigDecimal()
                ),
                technologies = AdminSearchTicketBasketItemsSummaryResponse.TechnologiesResponse(
                    serviceFees = AdminSearchTicketBasketItemsSummaryResponse.ServiceFeesResponse(
                        general = 0.toBigDecimal(),
                        vip = 0.toBigDecimal(),
                        premiumPlus = 0.toBigDecimal(),
                        imax = 0.toBigDecimal(),
                        dolbyAtmos = 0.toBigDecimal(),
                        total = 0.toBigDecimal()
                    ),
                    surcharges = AdminSearchTicketBasketItemsSummaryResponse.SurchargesResponse(
                        vip = 0.toBigDecimal(),
                        dBox = 0.toBigDecimal(),
                        premiumPlus = 0.toBigDecimal(),
                        imax = 0.toBigDecimal(),
                        dolbyAtmos = 0.toBigDecimal(),
                        total = 0.toBigDecimal()
                    ),
                    counts = AdminSearchTicketBasketItemsSummaryResponse.CountsResponse(
                        general = 0,
                        vip = 0,
                        dBox = 0,
                        premiumPlus = 0,
                        imax = 0,
                        dolbyAtmos = 0,
                        total = 0
                    )
                ),
                discounts = listOf(
                    AdminSearchTicketBasketItemsSummaryResponse.DiscountResponse(
                        title = NO_DISCOUNT_TITLE,
                        count = 1
                    )
                )
            ),
            result
        )
    }

    @Test
    fun `test AdminSearchTicketBasketItemsQuery - filter screeningDateTimeFrom, just one screening without tickets matches - should calculate correctly`() {
        setOf(SCREENING_1, SCREENING_2, SCREENING_3, SCREENING_5).forEach {
            screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(it))
        }
        setOf(SCREENING_FEE_1, SCREENING_FEE_2, SCREENING_FEE_3, SCREENING_FEE_5).forEach {
            screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(it))
        }

        // two tickets, no discounts, two base prices
        BASKET_1.createBasket()
        BASKET_1.addToBasket(1, SEAT_1.id, SCREENING_1.id, PriceCategoryItemNumber.PRICE_1)
        BASKET_1.addToBasket(2, SEAT_2.id, SCREENING_1.id, PriceCategoryItemNumber.PRICE_2)
        BASKET_1.payBasket(PaymentType.CASH, POS_CONFIGURATION_1.id)

        // two tickets, one with applied discount card, one with voucher, both tickets used
        BASKET_4.createBasket()
        BASKET_4.addToBasket(
            originalId = 7,
            seatId = SEAT_7.id,
            screeningId = SCREENING_3.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
            primaryDiscountCardId = DISCOUNT_CARD_1.id,
            primaryTicketDiscountId = TICKET_DISCOUNT_3.id,
            posConfigurationId = POS_CONFIGURATION_2.id,
            isUsed = true,
            ticketOriginalId = 1
        )
        BASKET_4.addToBasket(
            originalId = 8,
            seatId = SEAT_8.id,
            screeningId = SCREENING_3.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
            secondaryDiscountCardId = DISCOUNT_CARD_2.id,
            secondaryTicketDiscountId = TICKET_DISCOUNT_4.id,
            posConfigurationId = POS_CONFIGURATION_2.id,
            isUsed = true,
            ticketOriginalId = 2
        )
        BASKET_4.payBasket(PaymentType.CASH, POS_CONFIGURATION_2.id)

        val result = underTest(
            AdminSearchTicketBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminSearchTicketBasketItemsFilter(
                    screeningDateTimeFrom = LocalDateTime.of(
                        INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(3),
                        LocalTime.of(19, 0)
                    )
                )
            )
        )

        assertTicketBasketItemSummaryEquals(
            expected = AdminSearchTicketBasketItemsSummaryResponse(
                sales = AdminSearchTicketBasketItemsSummaryResponse.SalesResponse(
                    screeningsCount = 1,
                    ticketsCount = 0,
                    ticketsUsedCount = 0,
                    salesCash = 0.toBigDecimal(),
                    salesCashless = 0.toBigDecimal(),
                    grossSales = 0.toBigDecimal(),
                    netSales = 0.toBigDecimal(),
                    proDeduction = 0.toBigDecimal(),
                    filmFondDeduction = 0.toBigDecimal(),
                    distributorDeduction = 0.toBigDecimal(),
                    taxAmount = 0.toBigDecimal(),
                    cancelledTicketsCount = 0,
                    cancelledTicketsExpense = 0.toBigDecimal()
                ),
                technologies = AdminSearchTicketBasketItemsSummaryResponse.TechnologiesResponse(
                    serviceFees = AdminSearchTicketBasketItemsSummaryResponse.ServiceFeesResponse(
                        general = 0.toBigDecimal(),
                        vip = 0.toBigDecimal(),
                        premiumPlus = 0.toBigDecimal(),
                        imax = 0.toBigDecimal(),
                        dolbyAtmos = 0.toBigDecimal(),
                        total = 0.toBigDecimal()
                    ),
                    surcharges = AdminSearchTicketBasketItemsSummaryResponse.SurchargesResponse(
                        vip = 0.toBigDecimal(),
                        dBox = 0.toBigDecimal(),
                        premiumPlus = 0.toBigDecimal(),
                        imax = 0.toBigDecimal(),
                        dolbyAtmos = 0.toBigDecimal(),
                        total = 0.toBigDecimal()
                    ),
                    counts = AdminSearchTicketBasketItemsSummaryResponse.CountsResponse(
                        general = 0,
                        vip = 0,
                        dBox = 0,
                        premiumPlus = 0,
                        imax = 0,
                        dolbyAtmos = 0,
                        total = 0
                    )
                ),
                discounts = listOf(
                    AdminSearchTicketBasketItemsSummaryResponse.DiscountResponse(
                        title = NO_DISCOUNT_TITLE,
                        count = 0
                    )
                )
            ),
            result
        )
    }

    @Test
    fun `test AdminSearchTicketBasketItemsQuery - filter isCancelled, multiple screenings without tickets and one cancelled ticket exist - should calculate correctly`() {
        setOf(SCREENING_1, SCREENING_3, SCREENING_5).forEach {
            screeningService.syncCreateOrUpdateScreening(mapToCreateOrUpdateScreeningCommand(it))
        }
        setOf(SCREENING_FEE_1, SCREENING_FEE_3, SCREENING_FEE_5).forEach {
            screeningFeeService.createOrUpdateScreeningFee(mapToCreateOrUpdateScreeningFeeCommand(it))
        }

        // two tickets, no discounts, two base prices
        BASKET_1.createBasket()
        BASKET_1.addToBasket(1, SEAT_1.id, SCREENING_1.id, PriceCategoryItemNumber.PRICE_1)
        BASKET_1.addToBasket(2, SEAT_2.id, SCREENING_1.id, PriceCategoryItemNumber.PRICE_2)
        BASKET_1.payBasket(PaymentType.CASH, POS_CONFIGURATION_1.id)

        // two tickets, one with applied discount card, one with voucher, both tickets used
        BASKET_4.createBasket()
        BASKET_4.addToBasket(
            originalId = 7,
            seatId = SEAT_7.id,
            screeningId = SCREENING_3.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
            primaryDiscountCardId = DISCOUNT_CARD_1.id,
            primaryTicketDiscountId = TICKET_DISCOUNT_3.id,
            posConfigurationId = POS_CONFIGURATION_2.id,
            isUsed = true,
            ticketOriginalId = 1
        )
        BASKET_4.addToBasket(
            originalId = 8,
            seatId = SEAT_8.id,
            screeningId = SCREENING_3.id,
            priceCategoryItemNumber = PriceCategoryItemNumber.PRICE_1,
            secondaryDiscountCardId = DISCOUNT_CARD_2.id,
            secondaryTicketDiscountId = TICKET_DISCOUNT_4.id,
            posConfigurationId = POS_CONFIGURATION_2.id,
            isUsed = true,
            ticketOriginalId = 2
        )
        BASKET_4.payBasket(PaymentType.CASH, POS_CONFIGURATION_2.id)

        // cancelled and deleted ticket
        BASKET_3.createBasket()
        val basket6Item1 = BASKET_3.addToBasket(9, SEAT_9.id, SCREENING_4.id, PriceCategoryItemNumber.PRICE_2)
        BASKET_3.cancelBasketItem(10, basket6Item1.id)
        val basket6Item2 = BASKET_3.addToBasket(11, SEAT_10.id, SCREENING_4.id, PriceCategoryItemNumber.PRICE_2)
        BASKET_3.deleteBasketItem(basket6Item2.id)
        BASKET_3.payBasket(PaymentType.CASH, POS_CONFIGURATION_2.id)

        val result = underTest(
            AdminSearchTicketBasketItemsQuery(
                pageable = Pageable.unpaged(),
                filter = AdminSearchTicketBasketItemsFilter(
                    isCancelled = true
                )
            )
        )

        assertTicketBasketItemSummaryEquals(
            expected = AdminSearchTicketBasketItemsSummaryResponse(
                sales = AdminSearchTicketBasketItemsSummaryResponse.SalesResponse(
                    screeningsCount = 1, // only SCREENING_5 has no tickets sold
                    ticketsCount = 0,
                    ticketsUsedCount = 0,
                    salesCash = 0.toBigDecimal(),
                    salesCashless = 0.toBigDecimal(),
                    grossSales = 0.toBigDecimal(),
                    netSales = (-2.65).toBigDecimal(),
                    proDeduction = 0.05.toBigDecimal(),
                    filmFondDeduction = 0.1.toBigDecimal(),
                    distributorDeduction = 2.5.toBigDecimal(),
                    taxAmount = 0.toBigDecimal(),
                    cancelledTicketsCount = 1,
                    cancelledTicketsExpense = 6.toBigDecimal()
                ),
                technologies = AdminSearchTicketBasketItemsSummaryResponse.TechnologiesResponse(
                    serviceFees = AdminSearchTicketBasketItemsSummaryResponse.ServiceFeesResponse(
                        general = 0.toBigDecimal(),
                        vip = 0.toBigDecimal(),
                        premiumPlus = 0.toBigDecimal(),
                        imax = 0.toBigDecimal(),
                        dolbyAtmos = 0.toBigDecimal(),
                        total = 0.toBigDecimal()
                    ),
                    surcharges = AdminSearchTicketBasketItemsSummaryResponse.SurchargesResponse(
                        vip = 0.toBigDecimal(),
                        dBox = 0.toBigDecimal(),
                        premiumPlus = 0.toBigDecimal(),
                        imax = 0.toBigDecimal(),
                        dolbyAtmos = 0.toBigDecimal(),
                        total = 0.toBigDecimal()
                    ),
                    counts = AdminSearchTicketBasketItemsSummaryResponse.CountsResponse(
                        general = 0,
                        vip = 0,
                        dBox = 0,
                        premiumPlus = 0,
                        imax = 0,
                        dolbyAtmos = 0,
                        total = 0
                    )
                ),
                discounts = listOf(
                    AdminSearchTicketBasketItemsSummaryResponse.DiscountResponse(
                        title = NO_DISCOUNT_TITLE,
                        count = 0
                    )
                )
            ),
            result
        )
    }

    private fun Basket.addToBasket(
        originalId: Int,
        seatId: UUID,
        screeningId: UUID,
        priceCategoryItemNumber: PriceCategoryItemNumber,
        primaryTicketDiscountId: UUID? = null,
        secondaryTicketDiscountId: UUID? = null,
        primaryDiscountCardId: UUID? = null,
        secondaryDiscountCardId: UUID? = null,
        posConfigurationId: UUID? = null,
        isUsed: Boolean = false,
        ticketOriginalId: Int? = null,
    ): BasketItem {
        val basketItem = basketItemService.createBasketItem(
            mapToCreateBasketItemCommand(
                request = createTicketBasketItemRequest(
                    seatId = seatId,
                    screeningId = screeningId,
                    priceCategoryItem = priceCategoryItemNumber,
                    primaryTicketDiscountId = primaryTicketDiscountId,
                    secondaryTicketDiscountId = secondaryTicketDiscountId,
                    primaryDiscountCardId = primaryDiscountCardId,
                    secondaryDiscountCardId = secondaryDiscountCardId
                ),
                basketId = this.id,
                originalId = originalId
            )
        )

        (primaryDiscountCardId ?: secondaryDiscountCardId)?.let {
            val discountCardUsage = discountCardUsageService.createDiscountCardUsage(
                CreateDiscountCardUsageCommand(
                    discountCardId = it,
                    screeningId = screeningId,
                    basketId = basketItem.basketId,
                    posConfigurationId = posConfigurationId!!
                )
            )
            discountCardUsageService.updateDiscountCardUsage(
                UpdateDiscountCardUsageCommand(
                    discountCardUsageId = discountCardUsage.id,
                    basketItemId = basketItem.id
                )
            )
        }

        if (isUsed) {
            ticketService.updateTicketOriginalId(
                UpdateTicketOriginalIdCommand(
                    ticketId = basketItem.ticketId!!,
                    originalId = ticketOriginalId!!
                )
            )
            ticketService.updateTicketUsed(
                UpdateTicketUsedCommand(originalId = ticketOriginalId, isUsed = true)
            )
        }

        return basketItem
    }

    private fun Basket.addProductToBasket(originalId: Int, productId: UUID) {
        basketItemService.createBasketItem(
            CreateBasketItemCommand(
                basketId = this.id,
                type = BasketItemType.PRODUCT,
                quantity = 1,
                productId = productId,
                originalId = originalId
            )
        )
    }

    private fun Basket.payBasket(
        paymentType: PaymentType,
        posConfigurationId: UUID,
    ) {
        basketService.initiateBasketPayment(
            InitiateBasketPaymentCommand(
                basketId = this.id,
                posConfigurationId = posConfigurationId,
                paymentType = paymentType
            )
        )
        basketReceiptNumbersService.generateBasketReceiptNumbers(
            GenerateBasketReceiptNumbersCommand(
                basketId = this.id
            )
        )
        this.paidAt = basketService.completeBasketPayment(
            CompleteBasketPaymentCommand(
                basketId = this.id
            )
        ).paidAt
    }

    private fun Basket.cancelBasketItem(originalId: Int, basketItemId: UUID) {
        val cancelledBasketItemIds = basketItemService.cancelBasketItems(
            command = CancelBasketItemsCommand(
                basketItemIds = setOf(basketItemId),
                printReceipt = false
            )
        )
        val cancelledBasketItem =
            basketItemRepository.findAllByBasketIdAndDeletedAtIsNullAndCancelledBasketItemIdIsNotNull(
                basketId = cancelledBasketItemIds.first()
            ).first()

        basketItemRepository.save(
            cancelledBasketItem.apply { this.originalId = originalId }
        )
    }

    private fun Basket.deleteBasketItem(basketItemId: UUID) {
        basketItemService.deleteBasketItem(
            command = DeleteBasketItemCommand(
                basketId = this.id,
                basketItemId = basketItemId
            )
        )
    }

    private fun Basket.createBasket() = basketRepository.save(this)
}

// baskets
private val BASKET_1 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_2 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_3 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_4 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())
private val BASKET_5 = createBasket(state = BasketState.OPEN, totalPrice = 0.toBigDecimal())

// physical stuff - auditoriums, seats, POS's
private val POS_CONFIGURATION_1 = createPosConfiguration(title = "pokl1")
private val POS_CONFIGURATION_2 = createPosConfiguration(title = "pokl2", macAddress = "EE:DD:CC:BB:AA")
private val AUDITORIUM_1 = createAuditorium(originalId = 1, title = "Sála IMAX", code = "IMAX")
private val AUDITORIUM_2 = createAuditorium(originalId = 2, title = "Sála A", code = "A")
private val AUDITORIUM_3 = createAuditorium(originalId = 3, title = "Sála B", code = "B")
private val AUDITORIUM_LAYOUT_1 = createAuditoriumLayout(originalId = 1, auditoriumId = AUDITORIUM_1.id)
private val AUDITORIUM_LAYOUT_2 = createAuditoriumLayout(originalId = 2, auditoriumId = AUDITORIUM_1.id)
private val AUDITORIUM_LAYOUT_3 = createAuditoriumLayout(originalId = 3, auditoriumId = AUDITORIUM_1.id)
private val SEAT_1 = createSeat(
    originalId = 1,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR
)
private val SEAT_2 = createSeat(
    originalId = 2,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.REGULAR
)
private val SEAT_3 = createSeat(
    originalId = 3,
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    type = SeatType.PREMIUM_PLUS
)
private val SEAT_4 = createSeat(
    originalId = 4,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.REGULAR
)
private val SEAT_5 = createSeat(
    originalId = 5,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.REGULAR
)
private val SEAT_6 = createSeat(
    originalId = 6,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.PREMIUM_PLUS
)
private val SEAT_7 = createSeat(
    originalId = 7,
    auditoriumId = AUDITORIUM_3.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    type = SeatType.PREMIUM_PLUS
)
private val SEAT_8 = createSeat(
    originalId = 8,
    auditoriumId = AUDITORIUM_3.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    type = SeatType.REGULAR
)
private val SEAT_9 = createSeat(
    originalId = 9,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.VIP
)
private val SEAT_10 = createSeat(
    originalId = 10,
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    type = SeatType.REGULAR
)

// movie stuff - distributor, production, technology, movie...
private val DISTRIBUTOR_1 = createDistributor(originalId = 1, title = "Best movies ever, s.r.o.")
private val DISTRIBUTOR_2 = createDistributor(originalId = 2, title = "Worst movies ever, a.s.")
private val PRODUCTION_1 = Production(originalId = 1, code = "01", title = "USA")
private val PRODUCTION_2 = Production(originalId = 2, code = "02", title = "Česká Republika")
private val TECHNOLOGY_1 = Technology(originalId = 1, code = "01", title = "IMAX")
private val TECHNOLOGY_2 = Technology(originalId = 2, code = "02", title = "DCI2D")
private val TECHNOLOGY_3 = Technology(originalId = 3, code = "03", title = "Analog")
private val MOVIE_1 = createMovie(
    originalId = 1,
    rawTitle = "Star Wars: Episode I – The Phantom Menace 2D (CT)",
    title = "Star Wars: Episode I – The Phantom Menace",
    distributorId = DISTRIBUTOR_1.id,
    productionId = PRODUCTION_1.id,
    technologyId = TECHNOLOGY_2.id
)
private val MOVIE_2 = createMovie(
    originalId = 2,
    rawTitle = "Guardians of the Galaxy IMAX 3D (ST)",
    title = "Guardians of the Galaxy",
    distributorId = DISTRIBUTOR_1.id,
    productionId = PRODUCTION_1.id,
    technologyId = TECHNOLOGY_1.id
)
private val MOVIE_3 = createMovie(
    originalId = 3,
    rawTitle = "Oppenheimer 35 (ST)",
    title = "Oppenheimer",
    distributorId = DISTRIBUTOR_2.id,
    productionId = PRODUCTION_1.id,
    technologyId = TECHNOLOGY_3.id
)
private val MOVIE_4 = createMovie(
    originalId = 4,
    rawTitle = "Pelíšky 2D",
    title = "Pelíšky",
    distributorId = DISTRIBUTOR_2.id,
    productionId = PRODUCTION_2.id,
    technologyId = TECHNOLOGY_2.id
)

// screenings and prices
private val PRICE_CATEGORY_1 = createPriceCategory(originalId = 1, title = "Do 17h")
private val PRICE_CATEGORY_2 = createPriceCategory(originalId = 2, title = "Po 17h")
private val PRICE_CATEGORY_ITEM_1 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_1,
    price = 5.toBigDecimal()
)
private val PRICE_CATEGORY_ITEM_2 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_1.id,
    number = PriceCategoryItemNumber.PRICE_2,
    price = 4.toBigDecimal()
)
private val PRICE_CATEGORY_ITEM_3 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_2.id,
    number = PriceCategoryItemNumber.PRICE_1,
    price = 7.toBigDecimal()
)
private val PRICE_CATEGORY_ITEM_4 = createPriceCategoryItem(
    priceCategoryId = PRICE_CATEGORY_2.id,
    number = PriceCategoryItemNumber.PRICE_2,
    price = 6.toBigDecimal(),
    discounted = true
)
private val SCREENING_1 = createScreening(
    originalId = 1,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(1),
    time = LocalTime.of(20, 0),
    auditoriumId = AUDITORIUM_1.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_1.id,
    movieId = MOVIE_2.id,
    priceCategoryId = PRICE_CATEGORY_2.id,
    proCommission = 1,
    filmFondCommission = 2,
    distributorCommission = 50
)
private val SCREENING_2 = createScreening(
    originalId = 2,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(1),
    time = LocalTime.of(18, 0),
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = MOVIE_1.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    proCommission = 1,
    filmFondCommission = 1,
    distributorCommission = 30
)
private val SCREENING_3 = createScreening(
    originalId = 3,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(2),
    time = LocalTime.of(19, 0),
    auditoriumId = AUDITORIUM_3.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_3.id,
    movieId = MOVIE_4.id,
    priceCategoryId = PRICE_CATEGORY_2.id
)
private val SCREENING_4 = createScreening(
    originalId = 4,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(2),
    time = LocalTime.of(18, 0),
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = MOVIE_3.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    proCommission = 1,
    filmFondCommission = 2,
    distributorCommission = 50
)
private val SCREENING_5 = createScreening(
    originalId = 5,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(4),
    time = LocalTime.of(18, 0),
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = MOVIE_3.id,
    priceCategoryId = PRICE_CATEGORY_1.id
)
private val SCREENING_6 = createScreening(
    originalId = 6,
    date = INTEGRATION_TEST_DATE_TIME.toLocalDate().plusDays(6),
    time = LocalTime.of(18, 0),
    auditoriumId = AUDITORIUM_2.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT_2.id,
    movieId = MOVIE_3.id,
    priceCategoryId = PRICE_CATEGORY_1.id,
    cancelled = true
)
private val SCREENING_FEE_1 = createScreeningFee(
    originalScreeningId = 1,
    screeningId = SCREENING_1.id,
    surchargeVip = 0.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    surchargeImax = 1.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 1.toBigDecimal(),
    serviceFeeImax = 0.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal()
)
private val SCREENING_FEE_2 = createScreeningFee(
    originalScreeningId = 2,
    screeningId = SCREENING_2.id,
    surchargeVip = 0.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    surchargeImax = 0.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 0.toBigDecimal(),
    serviceFeeImax = 0.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal()
)
private val SCREENING_FEE_3 = createScreeningFee(
    originalScreeningId = 3,
    screeningId = SCREENING_3.id,
    surchargeVip = 0.toBigDecimal(),
    surchargePremium = 1.toBigDecimal(),
    surchargeImax = 0.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 0.toBigDecimal(),
    serviceFeeImax = 1.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    serviceFeeGeneral = 0.toBigDecimal()
)
private val SCREENING_FEE_4 = createScreeningFee(
    originalScreeningId = 4,
    screeningId = SCREENING_4.id,
    surchargeVip = 1.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    surchargeImax = 0.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 0.toBigDecimal(),
    serviceFeeImax = 0.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    serviceFeeGeneral = 1.toBigDecimal()
)
private val SCREENING_FEE_5 = createScreeningFee(
    originalScreeningId = 5,
    screeningId = SCREENING_5.id,
    surchargeVip = 1.toBigDecimal(),
    surchargePremium = 0.toBigDecimal(),
    surchargeImax = 0.toBigDecimal(),
    surchargeUltraX = 0.toBigDecimal(),
    surchargeDBox = 0.toBigDecimal(),
    serviceFeeVip = 0.toBigDecimal(),
    serviceFeePremium = 0.toBigDecimal(),
    serviceFeeImax = 0.toBigDecimal(),
    serviceFeeUltraX = 0.toBigDecimal(),
    serviceFeeGeneral = 1.toBigDecimal()
)
private val SCREENING_TYPE_1 = createScreeningType(
    originalId = 1,
    title = "Festival"
)
private val SCREENING_TYPE_2 = createScreeningType(
    originalId = 2,
    title = "IMAX"
)
private val SCREENING_TYPES_1 = createScreeningTypes(
    screeningId = SCREENING_4.id,
    screeningTypeId = SCREENING_TYPE_1.id
)
private val SCREENING_TYPES_2 = createScreeningTypes(
    screeningId = SCREENING_1.id,
    screeningTypeId = SCREENING_TYPE_2.id
)

// discounts and discount cards
private val TICKET_DISCOUNT_1 = createTicketDiscount(
    originalId = 1,
    code = "01",
    title = "Sleva 15%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 15,
    usageType = TicketDiscountUsageType.PRIMARY,
    freeCount = null
)
private val TICKET_DISCOUNT_2 = createTicketDiscount(
    originalId = 2,
    code = "02",
    title = "Sleva 30%",
    type = TicketDiscountType.PERCENTAGE,
    percentage = 30,
    usageType = TicketDiscountUsageType.SECONDARY,
    freeCount = null
)
private val TICKET_DISCOUNT_3 = createTicketDiscount(
    originalId = 3,
    code = "03",
    title = "Sleva 1€",
    type = TicketDiscountType.ABSOLUTE,
    amount = 1.toBigDecimal(),
    usageType = TicketDiscountUsageType.PRIMARY,
    freeCount = null
)
private val TICKET_DISCOUNT_4 = createTicketDiscount(
    originalId = 4,
    code = "04",
    title = "Sleva 2€",
    type = TicketDiscountType.ABSOLUTE,
    amount = 2.toBigDecimal(),
    usageType = TicketDiscountUsageType.SECONDARY,
    freeCount = null
)
private val DISCOUNT_CARD_1 = createDiscountCard(
    originalId = 1,
    ticketDiscountId = TICKET_DISCOUNT_1.id,
    type = DiscountCardType.CARD,
    title = "FILM karta",
    code = "333333333"
)
private val DISCOUNT_CARD_2 = createDiscountCard(
    originalId = 2,
    ticketDiscountId = TICKET_DISCOUNT_3.id,
    title = "Slovak Telekom Voucher",
    code = "555555555"
)

// one product item just so we have this covered
private val PRODUCT_CATEGORY_1 = createProductCategory()
private val PRODUCT_COMPONENT_CATEGORY_1 = createProductComponentCategory()
private val PRODUCT_COMPONENT_1 = createProductComponent(
    title = "Kukurica",
    stockQuantity = 10.toBigDecimal(),
    productComponentCategoryId = PRODUCT_COMPONENT_CATEGORY_1.id
)
private val PRODUCT_1 = createProduct(productCategoryId = PRODUCT_CATEGORY_1.id, title = "Popcorn XXL")
private val PRODUCT_COMPOSITION_1 = createProductComposition(
    productId = PRODUCT_1.id,
    productComponentId = PRODUCT_COMPONENT_1.id
)
