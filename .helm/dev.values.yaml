#-----------------------
# Basic
#-----------------------
application:
  name: cinemax-api-devel
  port: 8080

  resources:
    limits:
      memory: 2048Mi
      cpu: 1
    requests:
      memory: 512Mi
      cpu: 500m

  readinessProbe:
    httpGet:
      port: 8080
      path: /actuator/health
    initialDelaySeconds: 120
    timeoutSeconds: 5
    periodSeconds: 15
    failureThreshold: 5

  env:
    - name: OTEL_EXPORTER_OTLP_PROTOCOL
      value: grpc
    - name: SPRING_PROFILES_ACTIVE
      value: dev
    - name: DB_DSN
      valueFrom:
        secretKeyRef:
          key: DB_DSN
          name: env-variables
    - name: DB_USERNAME
      valueFrom:
        secretKeyRef:
          key: DB_USERNAME
          name: env-variables
    - name: DB_PASSWORD
      valueFrom:
        secretKeyRef:
          key: DB_PASSWORD
          name: env-variables
    - name: DB_MSSQL_DSN
      valueFrom:
        secretKeyRef:
          key: DB_MSSQL_DSN
          name: env-variables
    - name: DB_MSSQL_USERNAME
      valueFrom:
        secretKeyRef:
          key: DB_MSSQL_USERNAME
          name: env-variables
    - name: DB_MSSQL_PASSWORD
      valueFrom:
        secretKeyRef:
          key: DB_MSSQL_PASSWORD
          name: env-variables
    - name: DB_MSSQL_CINEMAX_DATABASE
      valueFrom:
        secretKeyRef:
          key: DB_MSSQL_CINEMAX_DATABASE
          name: env-variables
    - name: DB_MSSQL_BUFFET_DATABASE
      valueFrom:
        secretKeyRef:
          key: DB_MSSQL_BUFFET_DATABASE
          name: env-variables
    - name: REDIS_URL
      valueFrom:
        secretKeyRef:
          key: REDIS_URL
          name: env-variables
    - name: CINEMAX_WEB_USERNAME
      valueFrom:
        secretKeyRef:
          key: CINEMAX_WEB_USERNAME
          name: env-variables
    - name: CINEMAX_WEB_PASSWORD
      valueFrom:
        secretKeyRef:
          key: CINEMAX_WEB_PASSWORD
          name: env-variables
    - name: GCP_CREDENTIALS_FILE
      valueFrom:
        secretKeyRef:
          key: GCP_CREDENTIALS_FILE
          name: env-variables
    - name: CINEMAX_WEB_APP_SECURITY_APIKEY
      valueFrom:
        secretKeyRef:
          key: CINEMAX_WEB_APP_SECURITY_APIKEY
          name: env-variables
    - name: CINEMAX_VIP_APP_SECURITY_APIKEY
      valueFrom:
        secretKeyRef:
          key: CINEMAX_VIP_APP_SECURITY_APIKEY
          name: env-variables

  image:
    repository: gitlab.cleevio.cz:4567/backend/cinemax-api/master
    tag: latest

  replicaCount: 1

#-----------------------
# Ingress
#-----------------------
ingress:
  certificateIssuer: letsencrypt-http

  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"

  hosts:
    - host: api.cinemax.devel.cleevio.dev
      paths: [ "/" ]

  tls:
    - hosts:
        - api.cinemax.devel.cleevio.dev

#-----------------------
# Service
#-----------------------
service:
  port: 8080

#-----------------------
# Persistent Volume
#-----------------------
persistentVolume:
  enabled: true                     # Enables / Disables mounting
  mountName: pos                    # (Optional) Mount name
  size: "5Gi"                       # Size of mount
  mountPath: "/pos"                 # Mount path in the container
  storageClassName: "smb-cinemax"   # Storage class name - different on each provider
  cdn:
    enabled: false

secretVolumes:
  - name: gcp-service-account-secret
    mountPath: /gcp
    readOnly: true
    secretName: gcp-service-account-secret
