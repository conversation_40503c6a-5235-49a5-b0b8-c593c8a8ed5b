FROM eclipse-temurin:21-jre

ARG CI_PROJECT_NAME
ARG CI_COMMIT_SHORT_SHA
ARG KUBE_DOMAIN

VOLUME /tmp
ADD target/cinemax-api-1.0.0-SNAPSHOT.jar application.jar
ADD https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/download/v2.7.0/opentelemetry-javaagent.jar agent-ot.jar

RUN echo "\
java \
-javaagent:agent-ot.jar \
-Dserver.use-forward-headers=true \
-Dsentry.release=$CI_COMMIT_SHORT_SHA \
-Dspringdoc.swagger-server=https://$CI_PROJECT_NAME-$CI_COMMIT_SHORT_SHA.$KUBE_DOMAIN \
-jar /application.jar \
" > /run.sh

ENTRYPOINT ["/bin/sh", "/run.sh"]
